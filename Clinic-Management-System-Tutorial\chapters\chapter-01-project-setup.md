# Chapter 1: Project Setup and Foundation

## Building Upon the Laravel Starter Kit with <PERSON>act

In this chapter, we'll set up our Clinic Management System by extending the existing Laravel Starter Kit with React. This foundation provides us with authentication, user management, and a modern React frontend already configured.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Understand the Laravel Starter Kit structure and components
- Set up the development environment for clinic management
- Configure the project for healthcare-specific requirements
- Understand the project architecture and file organization
- Set up version control and development workflow

## 📋 Prerequisites Check

Before starting, ensure you have:
- **PHP 8.2+** with required extensions
- **Composer** for PHP dependency management
- **Node.js 18+** and **npm/yarn**
- **MySQL 8.0+** or **PostgreSQL 13+**
- **Git** for version control
- A code editor (VS Code recommended)

## 🏗 Understanding the Starter Kit Foundation

### Project Structure Overview

The Laravel Starter Kit with React provides:

```
laravel-starter-kit-react/
├── app/                          # Laravel application logic
│   ├── Http/Controllers/         # API and web controllers
│   ├── Models/                   # Eloquent models
│   └── Providers/               # Service providers
├── database/                     # Database migrations and seeders
├── resources/                    # Frontend and views
│   ├── js/                      # React application
│   │   ├── components/          # Reusable React components
│   │   ├── pages/              # Page components
│   │   ├── layouts/            # Layout components
│   │   └── types/              # TypeScript definitions
│   └── css/                    # Stylesheets
├── routes/                      # Route definitions
└── config/                     # Configuration files
```

### Key Technologies Already Configured

1. **Laravel 11**: Modern PHP framework with latest features
2. **Inertia.js**: Seamless Laravel-React integration
3. **React 18**: Modern React with TypeScript support
4. **Tailwind CSS**: Utility-first CSS framework
5. **Shadcn/ui**: Modern component library
6. **Laravel Sanctum**: API authentication (ready for extension)

## 🚀 Project Setup Steps

### Step 1: Clone and Initialize the Project

```bash
# Clone the starter kit (adjust the repository URL as needed)
git clone https://github.com/your-repo/laravel-starter-kit-react clinic-management-system
cd clinic-management-system

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### Step 2: Environment Configuration

Create and configure your environment file:

```bash
# Copy the environment template
cp .env.example .env

# Generate application key
php artisan key:generate
```

Configure your `.env` file for clinic management:

```env
# Application Configuration
APP_NAME="Clinic Management System"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_TIMEZONE=Asia/Jakarta
APP_URL=http://localhost:8000

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=clinic_management
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Mail Configuration (for notifications)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Session Configuration
SESSION_DRIVER=database
SESSION_LIFETIME=120

# Cache Configuration
CACHE_STORE=database

# Queue Configuration (for background jobs)
QUEUE_CONNECTION=database

# Filesystem Configuration
FILESYSTEM_DISK=local

# BPJS Configuration (we'll use these later)
JKN_PPK_CODE=your_ppk_code
JKN_CONS_ID=your_cons_id
JKN_CONS_SECRET=your_cons_secret
JKN_VCLAIM_USER_KEY=your_vclaim_key
JKN_ANTREAN_USER_KEY=your_antrean_key
```

### Step 3: Database Setup

Create the database and run initial migrations:

```bash
# Create database (MySQL example)
mysql -u root -p -e "CREATE DATABASE clinic_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run existing migrations
php artisan migrate

# Seed the database with initial data
php artisan db:seed
```

### Step 4: Frontend Setup

Configure the frontend for clinic management:

```bash
# Build frontend assets
npm run build

# For development, run the dev server
npm run dev
```

### Step 5: Verify Installation

Test that everything is working:

```bash
# Start the Laravel development server
php artisan serve

# In another terminal, start the frontend dev server
npm run dev
```

Visit `http://localhost:8000` to see the application running.

## 🔧 Customizing for Clinic Management

### Update Application Configuration

Let's customize the application for healthcare use:

**config/app.php** - Update application settings:
```php
'name' => env('APP_NAME', 'Clinic Management System'),
'timezone' => env('APP_TIMEZONE', 'Asia/Jakarta'),
```

**resources/js/app.tsx** - Update the app name:
```typescript
const appName = import.meta.env.VITE_APP_NAME || 'Clinic Management System';
```

### Prepare for Healthcare-Specific Features

Create directories for our clinic-specific code:

```bash
# Create directories for clinic features
mkdir -p app/Http/Controllers/Clinic
mkdir -p app/Models/Clinic
mkdir -p app/Services
mkdir -p resources/js/pages/clinic
mkdir -p resources/js/components/clinic
mkdir -p database/migrations/clinic
mkdir -p database/seeders/clinic
```

## 📁 Project Architecture for Clinic Management

### Backend Structure (Laravel)

```
app/
├── Http/Controllers/
│   ├── Auth/                    # Authentication (from starter kit)
│   └── Clinic/                  # Clinic-specific controllers
│       ├── PatientController.php
│       ├── DoctorController.php
│       ├── AppointmentController.php
│       └── MedicalRecordController.php
├── Models/
│   ├── User.php                 # Extended user model
│   └── Clinic/                  # Clinic-specific models
│       ├── Patient.php
│       ├── Doctor.php
│       ├── Appointment.php
│       └── MedicalRecord.php
├── Services/                    # Business logic services
│   ├── PatientService.php
│   ├── AppointmentService.php
│   └── BPJSService.php
└── Providers/
    └── ClinicServiceProvider.php
```

### Frontend Structure (React)

```
resources/js/
├── components/
│   ├── ui/                      # Shadcn/ui components
│   ├── clinic/                  # Clinic-specific components
│   │   ├── PatientCard.tsx
│   │   ├── AppointmentCalendar.tsx
│   │   └── MedicalRecordForm.tsx
│   └── shared/                  # Shared components
├── pages/
│   ├── auth/                    # Authentication pages
│   ├── clinic/                  # Clinic management pages
│   │   ├── patients/
│   │   ├── appointments/
│   │   ├── doctors/
│   │   └── reports/
│   └── dashboard.tsx            # Main dashboard
├── layouts/
│   ├── app-layout.tsx           # Main application layout
│   └── clinic-layout.tsx        # Clinic-specific layout
└── types/
    ├── index.d.ts               # Base types
    └── clinic.d.ts              # Clinic-specific types
```

## 🔐 Security Considerations for Healthcare

Healthcare applications require special security considerations:

### 1. Data Encryption
```php
// config/app.php - Ensure encryption is properly configured
'cipher' => 'AES-256-CBC',
```

### 2. Session Security
```php
// config/session.php
'secure' => env('SESSION_SECURE_COOKIE', true),
'http_only' => true,
'same_site' => 'strict',
```

### 3. CORS Configuration
```php
// config/cors.php - Configure for API access
'allowed_origins' => [env('FRONTEND_URL', 'http://localhost:3000')],
'allowed_headers' => ['*'],
'exposed_headers' => [],
'max_age' => 0,
'supports_credentials' => true,
```

## 🧪 Testing the Setup

Create a simple test to verify everything works:

```bash
# Run PHP tests
php artisan test

# Run frontend tests (if configured)
npm run test

# Check code style
npm run lint
```

## 📝 Development Workflow

### Git Setup
```bash
# Initialize git repository
git init
git add .
git commit -m "Initial clinic management system setup"

# Create development branch
git checkout -b develop
```

### Development Commands
```bash
# Start development servers
php artisan serve &
npm run dev &

# Watch for file changes
php artisan queue:work &  # For background jobs
```

## 🎯 Chapter Summary

In this chapter, we:

1. ✅ **Set up the development environment** based on Laravel Starter Kit
2. ✅ **Configured the project** for clinic management requirements
3. ✅ **Understood the project architecture** and file organization
4. ✅ **Prepared security configurations** for healthcare data
5. ✅ **Established development workflow** and version control

### What We Have Now

- A working Laravel-React application with authentication
- Proper environment configuration for clinic management
- Project structure ready for healthcare features
- Security configurations appropriate for medical data
- Development workflow established

### Next Steps

In **Chapter 2: Database Design and Models**, we'll:
- Design the comprehensive database schema for clinic management
- Create migrations for all healthcare entities
- Build Eloquent models with proper relationships
- Set up data validation and business rules

---

**Ready to continue?** Proceed to [Chapter 2: Database Design and Models](./chapter-02-database-models.md) to start building the data foundation for our clinic management system.
