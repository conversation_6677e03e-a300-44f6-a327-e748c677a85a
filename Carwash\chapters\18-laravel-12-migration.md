# Chapter 18: Laravel 11 to Laravel 12 Migration

Welcome to Chapter 18! In this chapter, we'll guide you through migrating your complete Car Wash Management System from Laravel 11 to Laravel 12, ensuring all business operations modules (POS, Queue, Bay Management, and Midtrans integration) continue to work seamlessly.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Understand Laravel 12's new features and breaking changes
- Successfully migrate your car wash management system to Laravel 12
- Update all business operations modules for Laravel 12 compatibility
- Optimize performance using new Laravel 12 features
- Ensure Midtrans integration works with Laravel 12
- Test all functionality after migration
- Implement Laravel 12 best practices

## 📋 What We'll Cover

1. Pre-migration preparation and backup strategies
2. Laravel 12 compatibility assessment
3. Step-by-step migration process
4. Code updates for Laravel 12
5. Database migration considerations
6. Testing migrated business modules
7. Performance optimizations
8. Troubleshooting common issues
9. Post-migration cleanup

## ⚠️ Important Prerequisites

Before starting this migration:
- Ensure you have completed Chapters 1-17
- Have a working Laravel 11 car wash management system
- All tests are passing in your current Laravel 11 application
- You have administrative access to your development environment

## 🛠 Step 1: Pre-Migration Preparation

### 1.1 Create Complete System Backup

First, let's create comprehensive backups of your entire system:

```bash
# Create backup directory
mkdir -p backups/pre-laravel-12-migration
cd backups/pre-laravel-12-migration

# Backup database
mysqldump -u your_username -p car_wash_management > database_backup_$(date +%Y%m%d_%H%M%S).sql

# Backup entire application
cd ../../
tar -czf backups/pre-laravel-12-migration/application_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
  --exclude=node_modules \
  --exclude=vendor \
  --exclude=storage/logs \
  --exclude=storage/framework/cache \
  --exclude=storage/framework/sessions \
  --exclude=storage/framework/views \
  .

# Backup .env file separately
cp .env backups/pre-laravel-12-migration/.env.backup

echo "✅ Backup completed successfully"
```

### 1.2 Document Current System State

Create a system state documentation:

```bash
# Create documentation file
touch backups/pre-laravel-12-migration/system_state.md
```

Add this content to `system_state.md`:

```markdown
# Car Wash Management System - Pre-Laravel 12 Migration State

## Current Laravel Version
- Laravel: 11.x
- PHP: 8.2+

## Installed Packages
- Laravel Breeze (Authentication)
- Spatie Laravel Permission (RBAC)
- Laravel Cashier (Stripe)
- Midtrans PHP SDK
- Laravel Broadcasting
- Laravel WebSockets (if using)

## Business Modules Status
- ✅ POS System (Chapter 14)
- ✅ Queue Management (Chapter 15)
- ✅ Bay Management (Chapter 16)
- ✅ Midtrans Integration (Chapter 17)

## Database Tables
- Core: users, customers, services, bookings, payments
- POS: pos_transactions, pos_transaction_items, products, cash_drawers, pos_sessions, discounts
- Queue: queue_numbers, queue_displays, queue_settings
- Bay: service_bays, bay_assignments, bay_equipment, bay_maintenance
- Midtrans: midtrans_transactions, midtrans_notifications

## Critical Integrations
- Midtrans Payment Gateway
- Real-time Broadcasting
- Email Notifications
- File Storage
```

### 1.3 Audit Current Dependencies

Check your current dependencies and their Laravel 12 compatibility:

```bash
# Check current Laravel version
php artisan --version

# List all installed packages
composer show --direct

# Check for outdated packages
composer outdated

# Generate dependency report
composer show --tree > backups/pre-laravel-12-migration/dependencies_tree.txt
```

### 1.4 Run Complete Test Suite

Ensure everything works before migration:

```bash
# Run all tests
php artisan test

# Run specific business module tests
php artisan test --filter=PosTest
php artisan test --filter=QueueTest
php artisan test --filter=BayTest
php artisan test --filter=MidtransTest

# Check code quality
./vendor/bin/phpstan analyse
./vendor/bin/php-cs-fixer fix --dry-run
```

## 🛠 Step 2: Laravel 12 Compatibility Assessment

### 2.1 Review Laravel 12 Breaking Changes

Key breaking changes in Laravel 12 that affect our car wash system:

```php
// Laravel 12 Breaking Changes Checklist

// 1. Minimum PHP Version
// Laravel 12 requires PHP 8.3+
// Current requirement: PHP 8.2+
// Action: Upgrade PHP to 8.3

// 2. Database Changes
// - New migration file structure
// - Updated Eloquent casting system
// - Changes to database connection handling

// 3. Authentication Changes
// - Laravel Breeze updates
// - Session handling improvements
// - Password reset flow changes

// 4. Broadcasting Changes
// - WebSocket connection improvements
// - Event broadcasting optimizations
// - Real-time update enhancements

// 5. Payment Integration Changes
// - Cashier updates for Stripe
// - Webhook handling improvements
// - Multi-currency enhancements

// 6. Queue System Changes
// - Job batching improvements
// - Failed job handling updates
// - Queue worker optimizations
```

### 2.2 Check Package Compatibility

Create a compatibility check script:

```php
<?php
// scripts/check_laravel_12_compatibility.php

$packages = [
    'laravel/breeze' => '^2.0',
    'spatie/laravel-permission' => '^6.0',
    'laravel/cashier' => '^15.0',
    'midtrans/midtrans-php' => '^2.6',
    'pusher/pusher-php-server' => '^7.2',
];

echo "🔍 Checking Laravel 12 Package Compatibility\n\n";

foreach ($packages as $package => $version) {
    echo "📦 {$package}: {$version}\n";
    
    // Check if package supports Laravel 12
    $command = "composer show {$package} --available";
    $output = shell_exec($command);
    
    if (strpos($output, 'laravel/framework: ^12.0') !== false) {
        echo "✅ Compatible with Laravel 12\n";
    } else {
        echo "⚠️  Needs verification for Laravel 12\n";
    }
    echo "\n";
}
```

## 🛠 Step 3: Step-by-Step Migration Process

### 3.1 Update PHP Version

First, ensure you're running PHP 8.3+:

```bash
# Check current PHP version
php -v

# If using Ubuntu/Debian
sudo apt update
sudo apt install php8.3 php8.3-cli php8.3-fpm php8.3-mysql php8.3-xml php8.3-curl php8.3-mbstring php8.3-zip

# Update composer to use PHP 8.3
sudo update-alternatives --set php /usr/bin/php8.3

# Verify PHP version
php -v
```

### 3.2 Update Composer Dependencies

Update your `composer.json` file:

```json
{
    "name": "carwash/management-system",
    "type": "project",
    "description": "Car Wash Management System with Laravel 12",
    "keywords": ["laravel", "car wash", "management", "pos", "queue", "bay"],
    "license": "MIT",
    "require": {
        "php": "^8.3",
        "laravel/framework": "^12.0",
        "laravel/breeze": "^2.0",
        "laravel/cashier": "^15.0",
        "laravel/tinker": "^2.9",
        "spatie/laravel-permission": "^6.0",
        "midtrans/midtrans-php": "^2.6",
        "pusher/pusher-php-server": "^7.2",
        "intervention/image": "^3.0",
        "maatwebsite/excel": "^3.1",
        "barryvdh/laravel-dompdf": "^3.0"
    },
    "require-dev": {
        "fakerphp/faker": "^1.23",
        "laravel/pint": "^1.13",
        "laravel/sail": "^1.26",
        "mockery/mockery": "^1.6",
        "nunomaduro/collision": "^8.0",
        "phpunit/phpunit": "^11.0",
        "spatie/laravel-ignition": "^2.4"
    },
    "autoload": {
        "psr-4": {
            "App\\": "app/",
            "Database\\Factories\\": "database/factories/",
            "Database\\Seeders\\": "database/seeders/"
        }
    },
    "autoload-dev": {
        "psr-4": {
            "Tests\\": "tests/"
        }
    },
    "scripts": {
        "post-autoload-dump": [
            "Illuminate\\Foundation\\ComposerScripts::postAutoloadDump",
            "@php artisan package:discover --ansi"
        ],
        "post-update-cmd": [
            "@php artisan vendor:publish --tag=laravel-assets --ansi --force"
        ],
        "post-root-package-install": [
            "@php -r \"file_exists('.env') || copy('.env.example', '.env');\""
        ],
        "post-create-project-cmd": [
            "@php artisan key:generate --ansi",
            "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"",
            "@php artisan migrate --graceful --ansi"
        ]
    },
    "extra": {
        "laravel": {
            "dont-discover": []
        }
    },
    "config": {
        "optimize-autoloader": true,
        "preferred-install": "dist",
        "sort-packages": true,
        "allow-plugins": {
            "pestphp/pest-plugin": true,
            "php-http/discovery": true
        }
    },
    "minimum-stability": "stable",
    "prefer-stable": true
}
```

### 3.3 Perform the Migration

Execute the migration process:

```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Remove vendor directory
rm -rf vendor/

# Remove composer.lock
rm composer.lock

# Install Laravel 12 dependencies
composer install

# Update Laravel application
php artisan migrate --force

# Clear and rebuild caches
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Publish new Laravel 12 assets
php artisan vendor:publish --tag=laravel-assets --force
```

## 🛠 Step 4: Code Updates for Laravel 12

### 4.1 Update Application Configuration

Update `config/app.php` for Laravel 12:

```php
<?php
// config/app.php - Laravel 12 Updates

return [
    'name' => env('APP_NAME', 'Car Wash Management'),
    'env' => env('APP_ENV', 'production'),
    'debug' => (bool) env('APP_DEBUG', false),
    'url' => env('APP_URL', 'http://localhost'),
    'asset_url' => env('ASSET_URL'),
    'timezone' => env('APP_TIMEZONE', 'Asia/Jakarta'), // Updated for Indonesian market
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => env('APP_KEY'),
    'previous_keys' => [
        ...array_filter(
            explode(',', env('APP_PREVIOUS_KEYS', ''))
        ),
    ],

    // Laravel 12 - Enhanced maintenance mode
    'maintenance' => [
        'driver' => env('APP_MAINTENANCE_DRIVER', 'file'),
        'store' => env('APP_MAINTENANCE_STORE', 'database'),
        'refresh' => env('APP_MAINTENANCE_REFRESH', 15),
        'secret' => env('APP_MAINTENANCE_SECRET'),
        'template' => env('APP_MAINTENANCE_TEMPLATE'),
        'redirect' => env('APP_MAINTENANCE_REDIRECT'),
    ],

    // Laravel 12 - New providers structure
    'providers' => [
        // Laravel Framework Service Providers
        Illuminate\Auth\AuthServiceProvider::class,
        Illuminate\Broadcasting\BroadcastServiceProvider::class,
        Illuminate\Bus\BusServiceProvider::class,
        Illuminate\Cache\CacheServiceProvider::class,
        Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class,
        Illuminate\Cookie\CookieServiceProvider::class,
        Illuminate\Database\DatabaseServiceProvider::class,
        Illuminate\Encryption\EncryptionServiceProvider::class,
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        Illuminate\Foundation\Providers\FoundationServiceProvider::class,
        Illuminate\Hashing\HashServiceProvider::class,
        Illuminate\Mail\MailServiceProvider::class,
        Illuminate\Notifications\NotificationServiceProvider::class,
        Illuminate\Pagination\PaginationServiceProvider::class,
        Illuminate\Auth\Passwords\PasswordResetServiceProvider::class,
        Illuminate\Pipeline\PipelineServiceProvider::class,
        Illuminate\Queue\QueueServiceProvider::class,
        Illuminate\Redis\RedisServiceProvider::class,
        Illuminate\Session\SessionServiceProvider::class,
        Illuminate\Translation\TranslationServiceProvider::class,
        Illuminate\Validation\ValidationServiceProvider::class,
        Illuminate\View\ViewServiceProvider::class,

        // Package Service Providers
        Spatie\Permission\PermissionServiceProvider::class,
        Laravel\Cashier\CashierServiceProvider::class,

        // Application Service Providers
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        
        // Car Wash Business Module Providers
        App\Providers\PosServiceProvider::class,
        App\Providers\QueueServiceProvider::class,
        App\Providers\BayServiceProvider::class,
        App\Providers\MidtransServiceProvider::class,
    ],

    // Laravel 12 - Enhanced aliases
    'aliases' => Facade::defaultAliases()->merge([
        'Midtrans' => App\Facades\Midtrans::class,
        'PosHelper' => App\Helpers\PosHelper::class,
        'QueueHelper' => App\Helpers\QueueHelper::class,
        'BayHelper' => App\Helpers\BayHelper::class,
    ])->toArray(),
];
```

### 4.2 Update Models for Laravel 12

Update your models to use Laravel 12's enhanced features:

```php
<?php
// app/Models/User.php - Laravel 12 Updates

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Laravel\Cashier\Billable;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles, Billable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'is_active',
        'last_login_at',
        'email_verified_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    // Laravel 12 - Enhanced casting with validation
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'is_active' => 'boolean',
            'password' => 'hashed', // Laravel 12 automatic password hashing
        ];
    }

    // Laravel 12 - New attribute accessor syntax
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->name,
        );
    }

    // Laravel 12 - Enhanced relationship methods
    public function posTransactions(): HasMany
    {
        return $this->hasMany(PosTransaction::class, 'cashier_id');
    }

    public function bayAssignments(): HasMany
    {
        return $this->hasMany(BayAssignment::class, 'assigned_by');
    }

    public function queueManagement(): HasMany
    {
        return $this->hasMany(QueueNumber::class, 'assigned_by');
    }
}
```

### 4.3 Update Controllers for Laravel 12

Update controllers to use Laravel 12's new features:

```php
<?php
// app/Http/Controllers/PosController.php - Laravel 12 Updates

namespace App\Http\Controllers;

use App\Models\PosTransaction;
use App\Models\Product;
use App\Models\PosSession;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class PosController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:manage_pos');

        // Laravel 12 - Enhanced middleware with parameters
        $this->middleware('throttle:pos,60,1')->only(['processTransaction']);
    }

    public function index(): View
    {
        // Laravel 12 - Enhanced query builder with better performance
        $activeSession = PosSession::query()
            ->where('user_id', auth()->id())
            ->where('status', 'active')
            ->with(['cashDrawer'])
            ->first();

        $todayTransactions = PosTransaction::query()
            ->whereDate('created_at', today())
            ->where('cashier_id', auth()->id())
            ->with(['items.product', 'customer'])
            ->latest()
            ->paginate(20);

        $products = Product::query()
            ->where('is_active', true)
            ->where('stock_quantity', '>', 0)
            ->orderBy('category')
            ->orderBy('name')
            ->get()
            ->groupBy('category');

        return view('pos.index', compact('activeSession', 'todayTransactions', 'products'));
    }

    public function processTransaction(Request $request): JsonResponse
    {
        // Laravel 12 - Enhanced validation with custom rules
        $validated = $request->validate([
            'items' => ['required', 'array', 'min:1'],
            'items.*.product_id' => ['required', 'exists:products,id'],
            'items.*.quantity' => ['required', 'integer', 'min:1'],
            'items.*.price' => ['required', 'numeric', 'min:0'],
            'payment_method' => ['required', 'string', 'in:cash,card,digital_wallet,bank_transfer'],
            'payment_amount' => ['required', 'numeric', 'min:0'],
            'customer_id' => ['nullable', 'exists:customers,id'],
            'discount_code' => ['nullable', 'string'],
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        try {
            // Laravel 12 - Enhanced database transactions with better error handling
            $transaction = DB::transaction(function () use ($validated) {
                $posTransaction = PosTransaction::create([
                    'transaction_number' => PosTransaction::generateTransactionNumber(),
                    'cashier_id' => auth()->id(),
                    'customer_id' => $validated['customer_id'] ?? null,
                    'subtotal' => 0,
                    'tax_amount' => 0,
                    'discount_amount' => 0,
                    'total_amount' => 0,
                    'payment_method' => $validated['payment_method'],
                    'payment_amount' => $validated['payment_amount'],
                    'change_amount' => 0,
                    'status' => 'completed',
                    'notes' => $validated['notes'] ?? null,
                ]);

                $subtotal = 0;
                foreach ($validated['items'] as $item) {
                    $product = Product::findOrFail($item['product_id']);

                    // Check stock availability
                    if ($product->stock_quantity < $item['quantity']) {
                        throw ValidationException::withMessages([
                            'stock' => "Insufficient stock for {$product->name}. Available: {$product->stock_quantity}"
                        ]);
                    }

                    $lineTotal = $item['quantity'] * $item['price'];
                    $subtotal += $lineTotal;

                    // Create transaction item
                    $posTransaction->items()->create([
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['price'],
                        'line_total' => $lineTotal,
                    ]);

                    // Update product stock
                    $product->updateStock(-$item['quantity']);
                }

                // Calculate totals
                $taxAmount = $subtotal * 0.1; // 10% tax
                $discountAmount = 0; // Apply discount logic here
                $totalAmount = $subtotal + $taxAmount - $discountAmount;
                $changeAmount = max(0, $validated['payment_amount'] - $totalAmount);

                $posTransaction->update([
                    'subtotal' => $subtotal,
                    'tax_amount' => $taxAmount,
                    'discount_amount' => $discountAmount,
                    'total_amount' => $totalAmount,
                    'change_amount' => $changeAmount,
                ]);

                return $posTransaction;
            });

            // Laravel 12 - Enhanced event broadcasting
            broadcast(new \App\Events\PosTransactionCompleted($transaction))->toOthers();

            return response()->json([
                'success' => true,
                'transaction' => $transaction->load(['items.product', 'customer']),
                'message' => 'Transaction completed successfully',
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction failed: ' . $e->getMessage(),
            ], 500);
        }
    }
}
```

### 4.4 Update Middleware for Laravel 12

Update middleware to use Laravel 12's enhanced features:

```php
<?php
// app/Http/Middleware/CheckPosSession.php - Laravel 12 Updates

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\PosSession;

class CheckPosSession
{
    public function handle(Request $request, Closure $next): Response
    {
        // Laravel 12 - Enhanced middleware with better performance
        if ($request->routeIs('pos.*')) {
            $activeSession = PosSession::query()
                ->where('user_id', auth()->id())
                ->where('status', 'active')
                ->exists();

            if (!$activeSession && !$request->routeIs('pos.session.*')) {
                return redirect()
                    ->route('pos.session.create')
                    ->with('warning', 'Please start a POS session before proceeding.');
            }
        }

        return $next($request);
    }
}
```

## 🛠 Step 5: Database Migration Considerations

### 5.1 Laravel 12 Migration Updates

Create new migrations for Laravel 12 enhancements:

```bash
# Create Laravel 12 enhancement migrations
php artisan make:migration add_laravel_12_enhancements_to_users_table
php artisan make:migration add_laravel_12_features_to_pos_transactions_table
php artisan make:migration add_laravel_12_optimizations_to_queue_numbers_table
```

Edit the enhancement migrations:

```php
<?php
// database/migrations/add_laravel_12_enhancements_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Laravel 12 - Enhanced user tracking
            $table->json('preferences')->nullable()->after('avatar');
            $table->timestamp('password_changed_at')->nullable()->after('password');
            $table->string('timezone')->default('Asia/Jakarta')->after('email_verified_at');
            $table->json('login_history')->nullable()->after('last_login_at');

            // Laravel 12 - Enhanced security features
            $table->boolean('two_factor_enabled')->default(false)->after('is_active');
            $table->string('two_factor_secret')->nullable()->after('two_factor_enabled');
            $table->json('recovery_codes')->nullable()->after('two_factor_secret');

            // Indexes for better performance
            $table->index(['is_active', 'email_verified_at']);
            $table->index(['timezone']);
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'preferences',
                'password_changed_at',
                'timezone',
                'login_history',
                'two_factor_enabled',
                'two_factor_secret',
                'recovery_codes'
            ]);
        });
    }
};
```

### 5.2 Update Business Module Migrations

Update POS system for Laravel 12:

```php
<?php
// database/migrations/add_laravel_12_features_to_pos_transactions_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('pos_transactions', function (Blueprint $table) {
            // Laravel 12 - Enhanced transaction tracking
            $table->json('payment_metadata')->nullable()->after('payment_amount');
            $table->string('receipt_format')->default('thermal')->after('notes');
            $table->boolean('is_refunded')->default(false)->after('status');
            $table->foreignId('refunded_by')->nullable()->constrained('users')->after('is_refunded');
            $table->timestamp('refunded_at')->nullable()->after('refunded_by');
            $table->decimal('refund_amount', 10, 2)->nullable()->after('refunded_at');

            // Laravel 12 - Performance optimizations
            $table->index(['created_at', 'status']);
            $table->index(['cashier_id', 'created_at']);
            $table->index(['is_refunded']);
        });

        Schema::table('products', function (Blueprint $table) {
            // Laravel 12 - Enhanced inventory management
            $table->json('variants')->nullable()->after('description');
            $table->decimal('cost_price', 10, 2)->nullable()->after('price');
            $table->integer('reorder_level')->default(10)->after('stock_quantity');
            $table->boolean('track_inventory')->default(true)->after('reorder_level');
            $table->json('supplier_info')->nullable()->after('track_inventory');

            // Performance indexes
            $table->index(['category', 'is_active']);
            $table->index(['stock_quantity', 'reorder_level']);
        });
    }

    public function down(): void
    {
        Schema::table('pos_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'payment_metadata',
                'receipt_format',
                'is_refunded',
                'refunded_by',
                'refunded_at',
                'refund_amount'
            ]);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'variants',
                'cost_price',
                'reorder_level',
                'track_inventory',
                'supplier_info'
            ]);
        });
    }
};
```

## 🛠 Step 6: Testing Migrated Business Modules

### 6.1 Create Laravel 12 Test Suite

Create comprehensive tests for Laravel 12:

```php
<?php
// tests/Feature/Laravel12/PosSystemTest.php

namespace Tests\Feature\Laravel12;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\PosSession;
use App\Models\PosTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PosSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected PosSession $session;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->user->givePermissionTo('manage_pos');

        $this->session = PosSession::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_can_process_transaction_with_laravel_12_features()
    {
        $this->actingAs($this->user);

        $products = Product::factory()->count(3)->create([
            'stock_quantity' => 10,
            'is_active' => true
        ]);

        $transactionData = [
            'items' => $products->map(fn($product) => [
                'product_id' => $product->id,
                'quantity' => 2,
                'price' => $product->price
            ])->toArray(),
            'payment_method' => 'cash',
            'payment_amount' => 1000,
            'notes' => 'Laravel 12 test transaction'
        ];

        $response = $this->postJson(route('pos.process-transaction'), $transactionData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Transaction completed successfully'
                ]);

        // Verify transaction was created
        $this->assertDatabaseHas('pos_transactions', [
            'cashier_id' => $this->user->id,
            'payment_method' => 'cash',
            'status' => 'completed'
        ]);

        // Verify stock was updated
        foreach ($products as $product) {
            $this->assertDatabaseHas('products', [
                'id' => $product->id,
                'stock_quantity' => 8 // 10 - 2
            ]);
        }
    }

    /** @test */
    public function it_handles_insufficient_stock_with_laravel_12_validation()
    {
        $this->actingAs($this->user);

        $product = Product::factory()->create([
            'stock_quantity' => 1,
            'is_active' => true
        ]);

        $transactionData = [
            'items' => [[
                'product_id' => $product->id,
                'quantity' => 5, // More than available stock
                'price' => $product->price
            ]],
            'payment_method' => 'cash',
            'payment_amount' => 1000
        ];

        $response = $this->postJson(route('pos.process-transaction'), $transactionData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['stock']);
    }

    /** @test */
    public function it_can_refund_transaction_with_laravel_12_features()
    {
        $this->actingAs($this->user);

        $transaction = PosTransaction::factory()->create([
            'cashier_id' => $this->user->id,
            'status' => 'completed',
            'total_amount' => 100
        ]);

        $response = $this->postJson(route('pos.refund-transaction', $transaction), [
            'refund_amount' => 100,
            'reason' => 'Customer request'
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('pos_transactions', [
            'id' => $transaction->id,
            'is_refunded' => true,
            'refund_amount' => 100,
            'refunded_by' => $this->user->id
        ]);
    }
}
```

### 6.2 Test Queue Management with Laravel 12

```php
<?php
// tests/Feature/Laravel12/QueueSystemTest.php

namespace Tests\Feature\Laravel12;

use Tests\TestCase;
use App\Models\User;
use App\Models\QueueNumber;
use App\Models\Customer;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use App\Events\QueueUpdated;

class QueueSystemTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->user->givePermissionTo('manage_queue');
    }

    /** @test */
    public function it_can_assign_queue_number_with_laravel_12_broadcasting()
    {
        Event::fake();

        $this->actingAs($this->user);

        $customer = Customer::factory()->create();
        $service = Service::factory()->create();

        $queueData = [
            'customer_name' => $customer->first_name . ' ' . $customer->last_name,
            'service_type' => $service->name,
            'estimated_duration' => 30,
            'customer_id' => $customer->id
        ];

        $response = $this->postJson(route('queue.store'), $queueData);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);

        // Verify queue number was created
        $this->assertDatabaseHas('queue_numbers', [
            'customer_id' => $customer->id,
            'service_type' => $service->name,
            'status' => 'waiting'
        ]);

        // Verify event was broadcasted
        Event::assertDispatched(QueueUpdated::class);
    }

    /** @test */
    public function it_calculates_wait_time_correctly_with_laravel_12_features()
    {
        $this->actingAs($this->user);

        // Create existing queue numbers
        QueueNumber::factory()->count(3)->create([
            'status' => 'waiting',
            'estimated_duration' => 30,
            'assigned_at' => now()->subMinutes(10)
        ]);

        // Create new queue number
        $newQueue = QueueNumber::factory()->create([
            'status' => 'waiting',
            'estimated_duration' => 30,
            'assigned_at' => now()
        ]);

        // The new queue should be position 4 with 90 minutes wait time
        $this->assertEquals(4, $newQueue->position_in_queue);
        $this->assertEquals(90, $newQueue->estimated_wait_time); // 3 * 30 minutes
    }
}
```

## 🛠 Step 7: Performance Optimizations

### 7.1 Laravel 12 Performance Features

Implement Laravel 12's new performance optimizations:

```php
<?php
// config/cache.php - Laravel 12 Optimizations

return [
    'default' => env('CACHE_DRIVER', 'redis'),

    'stores' => [
        'redis' => [
            'driver' => 'redis',
            'connection' => 'cache',
            'lock_connection' => 'default',
            // Laravel 12 - Enhanced Redis configuration
            'serializer' => env('CACHE_REDIS_SERIALIZER', 'igbinary'),
            'compression' => env('CACHE_REDIS_COMPRESSION', 'lz4'),
        ],

        // Laravel 12 - New DynamoDB cache driver
        'dynamodb' => [
            'driver' => 'dynamodb',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'table' => env('DYNAMODB_CACHE_TABLE', 'cache'),
            'endpoint' => env('DYNAMODB_ENDPOINT'),
        ],
    ],

    // Laravel 12 - Enhanced cache prefixes
    'prefix' => env('CACHE_PREFIX', 'carwash_' . md5(__DIR__)),
];
```

### 7.2 Optimize Business Module Queries

Create optimized query classes for Laravel 12:

```php
<?php
// app/Queries/Laravel12/OptimizedPosQueries.php

namespace App\Queries\Laravel12;

use App\Models\PosTransaction;
use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class OptimizedPosQueries
{
    // Laravel 12 - Enhanced query optimization with better caching
    public static function getTodayTransactions(int $cashierId): Collection
    {
        return Cache::remember(
            "pos_transactions_today_{$cashierId}",
            now()->addMinutes(5),
            fn() => PosTransaction::query()
                ->where('cashier_id', $cashierId)
                ->whereDate('created_at', today())
                ->with(['items.product:id,name,category', 'customer:id,first_name,last_name'])
                ->select(['id', 'transaction_number', 'total_amount', 'payment_method', 'created_at', 'customer_id'])
                ->latest()
                ->get()
        );
    }

    // Laravel 12 - Optimized inventory queries
    public static function getLowStockProducts(): Collection
    {
        return Cache::remember(
            'low_stock_products',
            now()->addMinutes(15),
            fn() => Product::query()
                ->where('is_active', true)
                ->where('track_inventory', true)
                ->whereColumn('stock_quantity', '<=', 'reorder_level')
                ->select(['id', 'name', 'stock_quantity', 'reorder_level', 'category'])
                ->orderBy('stock_quantity')
                ->get()
        );
    }

    // Laravel 12 - Advanced analytics with window functions
    public static function getSalesAnalytics(string $period = 'week'): array
    {
        $cacheKey = "sales_analytics_{$period}";

        return Cache::remember($cacheKey, now()->addHour(), function() use ($period) {
            $dateRange = match($period) {
                'day' => [now()->startOfDay(), now()->endOfDay()],
                'week' => [now()->startOfWeek(), now()->endOfWeek()],
                'month' => [now()->startOfMonth(), now()->endOfMonth()],
                default => [now()->startOfWeek(), now()->endOfWeek()],
            };

            return DB::table('pos_transactions')
                ->selectRaw('
                    DATE(created_at) as date,
                    COUNT(*) as transaction_count,
                    SUM(total_amount) as total_sales,
                    AVG(total_amount) as average_sale,
                    SUM(CASE WHEN payment_method = "cash" THEN total_amount ELSE 0 END) as cash_sales,
                    SUM(CASE WHEN payment_method = "card" THEN total_amount ELSE 0 END) as card_sales
                ')
                ->whereBetween('created_at', $dateRange)
                ->where('status', 'completed')
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->toArray();
        });
    }
}
```

## 🛠 Step 8: Troubleshooting Common Migration Issues

### 8.1 Common Laravel 12 Migration Problems

Here are the most common issues and their solutions:

**Problem 1: Composer Dependency Conflicts**
```bash
# Error: Package conflicts with Laravel 12
# Solution: Update package versions
composer update --with-all-dependencies

# If specific packages conflict:
composer remove problematic/package
composer require updated/package:^new-version
```

**Problem 2: Middleware Registration Issues**
```php
// Error: Middleware not found
// Solution: Update middleware registration in bootstrap/app.php (Laravel 12)

// OLD (Laravel 11): app/Http/Kernel.php
// NEW (Laravel 12): bootstrap/app.php
return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'check.pos.session' => \App\Http\Middleware\CheckPosSession::class,
            'check.bay.access' => \App\Http\Middleware\CheckBayAccess::class,
            'queue.manager' => \App\Http\Middleware\QueueManagerMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
```

**Problem 3: Broadcasting Configuration Issues**
```php
// Error: Broadcasting not working with Laravel 12
// Solution: Update broadcasting configuration

// config/broadcasting.php - Laravel 12 updates
'pusher' => [
    'driver' => 'pusher',
    'key' => env('PUSHER_APP_KEY'),
    'secret' => env('PUSHER_APP_SECRET'),
    'app_id' => env('PUSHER_APP_ID'),
    'options' => [
        'cluster' => env('PUSHER_APP_CLUSTER'),
        'useTLS' => true,
        // Laravel 12 - Enhanced encryption
        'encryption_master_key_base64' => env('PUSHER_ENCRYPTION_KEY'),
    ],
],
```

**Problem 4: Midtrans Integration Issues**
```php
// Error: Midtrans webhook signature validation fails
// Solution: Update webhook handling for Laravel 12

// app/Http/Controllers/MidtransWebhookController.php
public function handle(Request $request): JsonResponse
{
    // Laravel 12 - Enhanced request validation
    $signature = $request->header('X-Midtrans-Signature');
    $payload = $request->getContent();

    // Verify signature with Laravel 12's improved hash verification
    if (!hash_equals(
        hash_hmac('sha512', $payload, config('midtrans.server_key')),
        $signature
    )) {
        return response()->json(['error' => 'Invalid signature'], 401);
    }

    // Process webhook...
    return response()->json(['status' => 'success']);
}
```

### 8.2 Performance Troubleshooting

**Problem: Slow Query Performance**
```bash
# Enable Laravel 12 query logging
php artisan db:monitor --max=1000

# Optimize database with Laravel 12 tools
php artisan model:optimize
php artisan route:cache
php artisan config:cache
php artisan view:cache

# Laravel 12 - New optimization commands
php artisan optimize:clear
php artisan optimize
```

**Problem: Memory Usage Issues**
```php
// Solution: Use Laravel 12's enhanced memory management
// config/app.php
'memory_limit' => env('APP_MEMORY_LIMIT', '512M'),

// Use chunking for large datasets
PosTransaction::chunk(1000, function ($transactions) {
    foreach ($transactions as $transaction) {
        // Process transaction
    }
});
```

## 🛠 Step 9: Post-Migration Cleanup

### 9.1 Remove Deprecated Code

Clean up Laravel 11 specific code:

```bash
# Remove Laravel 11 specific files
rm -f app/Http/Kernel.php  # Replaced by bootstrap/app.php in Laravel 12
rm -f config/cors.php      # Now handled in bootstrap/app.php

# Clean up old cache files
php artisan optimize:clear
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Remove old compiled files
rm -rf bootstrap/cache/*.php
```

### 9.2 Update Documentation

Update your project documentation:

```markdown
# Car Wash Management System - Laravel 12

## System Requirements (Updated)
- PHP 8.3 or higher
- Laravel 12.x
- MySQL 8.0 or PostgreSQL 13+
- Redis 6.0+ (recommended for caching)
- Node.js 18+ and npm

## New Laravel 12 Features Implemented
- Enhanced middleware system
- Improved broadcasting performance
- Better query optimization
- Advanced caching strategies
- Enhanced security features
- Improved testing capabilities

## Business Module Updates
- POS System: Enhanced transaction processing and inventory management
- Queue Management: Improved real-time updates and performance
- Bay Management: Better resource allocation and tracking
- Midtrans Integration: Enhanced security and webhook handling
```

### 9.3 Final Testing and Validation

Run comprehensive tests to ensure everything works:

```bash
# Run all tests
php artisan test

# Run specific business module tests
php artisan test --filter=Laravel12

# Performance testing
php artisan test --filter=Performance

# Integration testing
php artisan test --filter=Integration

# Load testing (if you have load testing setup)
php artisan test --filter=Load
```

## 🎯 Chapter Summary

Congratulations! You've successfully migrated your Car Wash Management System to Laravel 12. Here's what you've accomplished:

✅ **Pre-migration Preparation**: Created comprehensive backups and documented system state
✅ **Compatibility Assessment**: Evaluated Laravel 12 compatibility for all components
✅ **Step-by-step Migration**: Updated PHP, Composer dependencies, and Laravel framework
✅ **Code Updates**: Modernized models, controllers, and middleware for Laravel 12
✅ **Database Enhancements**: Added Laravel 12 specific database optimizations
✅ **Business Module Updates**: Ensured POS, Queue, Bay, and Midtrans systems work with Laravel 12
✅ **Performance Optimizations**: Implemented Laravel 12's enhanced caching and query features
✅ **Comprehensive Testing**: Validated all functionality with Laravel 12 test suite
✅ **Troubleshooting**: Addressed common migration issues and solutions
✅ **Post-migration Cleanup**: Removed deprecated code and updated documentation

### Laravel 12 Migration Benefits:
- **Enhanced Performance**: Improved query optimization and caching
- **Better Security**: Advanced authentication and validation features
- **Improved Broadcasting**: Better real-time updates for queue and bay management
- **Enhanced Testing**: More robust testing capabilities for business modules
- **Modern PHP Features**: Leveraging PHP 8.3+ capabilities
- **Indonesian Market Ready**: Optimized Midtrans integration with Laravel 12
- **Future-Proof**: Ready for upcoming Laravel features and improvements

Your car wash management system is now running on Laravel 12 with all business operations modules fully optimized and ready for production use!

## 🚀 Next Steps

1. **Monitor Performance**: Use Laravel 12's enhanced monitoring tools
2. **Implement New Features**: Explore Laravel 12's new capabilities
3. **Security Hardening**: Implement Laravel 12's advanced security features
4. **Scaling Preparation**: Use Laravel 12's improved scaling capabilities
5. **Community Engagement**: Share your migration experience with the Laravel community

---

**Congratulations on successfully migrating to Laravel 12!** 🎉

Your Car Wash Management System is now future-ready with the latest Laravel framework!
