# Chapter 14: Deployment and Production

## Production Deployment and Maintenance

In this final chapter, we'll cover production deployment strategies, security hardening, monitoring, backup procedures, and maintenance practices for our Clinic Management System.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Set up production environment configuration
- Implement deployment strategies and CI/CD pipelines
- Configure security hardening and monitoring
- Create backup and maintenance procedures
- Set up performance optimization
- Implement logging and error tracking

## 🚀 Production Environment Setup

### Step 1: Environment Configuration

**Production .env Configuration**:

```env
# Application
APP_NAME="Clinic Management System"
APP_ENV=production
APP_KEY=base64:your-production-key-here
APP_DEBUG=false
APP_URL=https://your-clinic-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=clinic_management_prod
DB_USERNAME=clinic_user
DB_PASSWORD=secure-password-here

# Cache & Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=your-redis-host
REDIS_PASSWORD=redis-password
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# File Storage
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_DEFAULT_REGION=ap-southeast-1
AWS_BUCKET=clinic-management-files

# Security
SANCTUM_STATEFUL_DOMAINS=your-clinic-domain.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=strict

# BPJS Configuration
BPJS_PPK_CODE=your-production-ppk-code
BPJS_CONS_ID=your-production-cons-id
BPJS_CONS_SECRET=your-production-cons-secret
BPJS_ENVIRONMENT=production

# Monitoring
SENTRY_LARAVEL_DSN=your-sentry-dsn
LOG_CHANNEL=stack
LOG_LEVEL=error

# Backup
BACKUP_DISK=s3
BACKUP_NOTIFICATION_MAIL=<EMAIL>
```

### Step 2: Docker Configuration

**Dockerfile**:

```dockerfile
FROM php:8.2-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    mysql-client \
    nginx \
    supervisor

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Install Node.js and npm
RUN apk add --no-cache nodejs npm

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . .

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader

# Install Node dependencies and build assets
RUN npm ci && npm run build

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# Copy configuration files
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose port
EXPOSE 80

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

**docker-compose.yml**:

```yaml
version: '3.8'

services:
  app:
    build: .
    container_name: clinic-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=production
    depends_on:
      - db
      - redis
    networks:
      - clinic-network

  db:
    image: mysql:8.0
    container_name: clinic-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: clinic_management_prod
      MYSQL_USER: clinic_user
      MYSQL_PASSWORD: secure-password-here
      MYSQL_ROOT_PASSWORD: root-password-here
    volumes:
      - db_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    ports:
      - "3306:3306"
    networks:
      - clinic-network

  redis:
    image: redis:7-alpine
    container_name: clinic-redis
    restart: unless-stopped
    command: redis-server --requirepass redis-password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - clinic-network

  queue:
    build: .
    container_name: clinic-queue
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    volumes:
      - ./storage:/var/www/html/storage
    depends_on:
      - db
      - redis
    networks:
      - clinic-network

  scheduler:
    build: .
    container_name: clinic-scheduler
    restart: unless-stopped
    command: php artisan schedule:work
    volumes:
      - ./storage:/var/www/html/storage
    depends_on:
      - db
      - redis
    networks:
      - clinic-network

volumes:
  db_data:
  redis_data:

networks:
  clinic-network:
    driver: bridge
```

### Step 3: CI/CD Pipeline

**GitHub Actions (.github/workflows/deploy.yml)**:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql, gd
        coverage: xdebug

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install PHP dependencies
      run: composer install --no-progress --prefer-dist --optimize-autoloader

    - name: Install Node dependencies
      run: npm ci

    - name: Build assets
      run: npm run build

    - name: Copy environment file
      run: cp .env.example .env

    - name: Generate application key
      run: php artisan key:generate

    - name: Run database migrations
      run: php artisan migrate --force
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: testing
        DB_USERNAME: root
        DB_PASSWORD: password

    - name: Run tests
      run: php artisan test --coverage
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: testing
        DB_USERNAME: root
        DB_PASSWORD: password

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/clinic-management
          git pull origin main
          composer install --no-dev --optimize-autoloader
          npm ci && npm run build
          php artisan migrate --force
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          php artisan queue:restart
          sudo systemctl reload nginx
```

### Step 4: Security Hardening

**Security Configuration**:

```php
// config/security.php
<?php

return [
    'headers' => [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin',
        'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';",
    ],
    
    'rate_limiting' => [
        'api' => '60,1',
        'login' => '5,1',
        'password_reset' => '3,1',
    ],
    
    'session' => [
        'lifetime' => 120, // 2 hours
        'expire_on_close' => true,
        'encrypt' => true,
        'http_only' => true,
        'same_site' => 'strict',
        'secure' => true,
    ],
    
    'password_policy' => [
        'min_length' => 12,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'max_age_days' => 90,
    ],
];
```

**Security Middleware**:

```php
// app/Http/Middleware/SecurityHeadersMiddleware.php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeadersMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        $headers = config('security.headers');
        
        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }

        return $response;
    }
}
```

### Step 5: Monitoring and Logging

**Logging Configuration (config/logging.php)**:

```php
'channels' => [
    'stack' => [
        'driver' => 'stack',
        'channels' => ['single', 'sentry'],
        'ignore_exceptions' => false,
    ],

    'single' => [
        'driver' => 'single',
        'path' => storage_path('logs/laravel.log'),
        'level' => env('LOG_LEVEL', 'debug'),
    ],

    'sentry' => [
        'driver' => 'sentry',
        'level' => 'error',
    ],

    'medical_audit' => [
        'driver' => 'single',
        'path' => storage_path('logs/medical-audit.log'),
        'level' => 'info',
    ],

    'security' => [
        'driver' => 'single',
        'path' => storage_path('logs/security.log'),
        'level' => 'warning',
    ],
],
```

**Health Check Endpoint**:

```php
// routes/web.php
Route::get('/health', function () {
    $checks = [
        'database' => false,
        'redis' => false,
        'storage' => false,
        'queue' => false,
    ];

    try {
        DB::connection()->getPdo();
        $checks['database'] = true;
    } catch (\Exception $e) {
        Log::error('Health check failed: Database', ['error' => $e->getMessage()]);
    }

    try {
        Redis::ping();
        $checks['redis'] = true;
    } catch (\Exception $e) {
        Log::error('Health check failed: Redis', ['error' => $e->getMessage()]);
    }

    try {
        Storage::disk('local')->put('health-check.txt', 'OK');
        Storage::disk('local')->delete('health-check.txt');
        $checks['storage'] = true;
    } catch (\Exception $e) {
        Log::error('Health check failed: Storage', ['error' => $e->getMessage()]);
    }

    $allHealthy = !in_array(false, $checks);
    
    return response()->json([
        'status' => $allHealthy ? 'healthy' : 'unhealthy',
        'checks' => $checks,
        'timestamp' => now()->toISOString(),
    ], $allHealthy ? 200 : 503);
});
```

### Step 6: Backup Strategy

**Backup Configuration**:

```php
// config/backup.php
<?php

return [
    'backup' => [
        'name' => env('APP_NAME', 'clinic-management'),
        
        'source' => [
            'files' => [
                'include' => [
                    base_path(),
                ],
                'exclude' => [
                    base_path('vendor'),
                    base_path('node_modules'),
                    base_path('storage/logs'),
                    base_path('storage/framework/cache'),
                ],
            ],
            
            'databases' => [
                'mysql',
            ],
        ],
        
        'destination' => [
            'filename_prefix' => '',
            'disks' => [
                's3',
            ],
        ],
        
        'temporary_directory' => storage_path('app/backup-temp'),
    ],
    
    'notifications' => [
        'notifications' => [
            \Spatie\Backup\Notifications\Notifications\BackupHasFailed::class => ['mail'],
            \Spatie\Backup\Notifications\Notifications\UnhealthyBackupWasFound::class => ['mail'],
            \Spatie\Backup\Notifications\Notifications\CleanupHasFailed::class => ['mail'],
            \Spatie\Backup\Notifications\Notifications\BackupWasSuccessful::class => ['mail'],
            \Spatie\Backup\Notifications\Notifications\HealthyBackupWasFound::class => [],
            \Spatie\Backup\Notifications\Notifications\CleanupWasSuccessful::class => [],
        ],
        
        'notifiable' => \Spatie\Backup\Notifications\Notifiable::class,
        
        'mail' => [
            'to' => env('BACKUP_NOTIFICATION_MAIL', '<EMAIL>'),
            'from' => [
                'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'name' => env('MAIL_FROM_NAME', 'Clinic Management System'),
            ],
        ],
    ],
    
    'monitor_backups' => [
        [
            'name' => env('APP_NAME', 'clinic-management'),
            'disks' => ['s3'],
            'health_checks' => [
                \Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumAgeInDays::class => 1,
                \Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumStorageInMegabytes::class => 5000,
            ],
        ],
    ],
    
    'cleanup' => [
        'strategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
        
        'default_strategy' => [
            'keep_all_backups_for_days' => 7,
            'keep_daily_backups_for_days' => 16,
            'keep_weekly_backups_for_weeks' => 8,
            'keep_monthly_backups_for_months' => 4,
            'keep_yearly_backups_for_years' => 2,
            'delete_oldest_backups_when_using_more_megabytes_than' => 5000,
        ],
    ],
];
```

**Scheduled Backups (app/Console/Kernel.php)**:

```php
protected function schedule(Schedule $schedule)
{
    // Daily database backup
    $schedule->command('backup:run --only-db')
        ->daily()
        ->at('02:00')
        ->emailOutputOnFailure(config('backup.notifications.mail.to'));

    // Weekly full backup
    $schedule->command('backup:run')
        ->weekly()
        ->sundays()
        ->at('03:00')
        ->emailOutputOnFailure(config('backup.notifications.mail.to'));

    // Daily backup monitoring
    $schedule->command('backup:monitor')
        ->daily()
        ->at('04:00');

    // Weekly backup cleanup
    $schedule->command('backup:clean')
        ->weekly()
        ->mondays()
        ->at('05:00');

    // Queue monitoring
    $schedule->command('queue:monitor redis:default --max=100')
        ->everyMinute()
        ->emailOutputOnFailure(config('backup.notifications.mail.to'));

    // Clear expired sessions
    $schedule->command('session:gc')
        ->daily()
        ->at('01:00');

    // Clear application cache
    $schedule->command('cache:prune-stale-tags')
        ->hourly();
}
```

## 📋 Chapter Summary

In this final chapter, we:

1. ✅ **Set up production environment** with secure configuration
2. ✅ **Implemented Docker containerization** for consistent deployment
3. ✅ **Created CI/CD pipeline** with automated testing and deployment
4. ✅ **Configured security hardening** with headers and policies
5. ✅ **Set up monitoring and logging** for production oversight
6. ✅ **Implemented backup strategy** with automated scheduling

### What We Have Now

- Production-ready deployment configuration
- Automated CI/CD pipeline with testing
- Security hardening and monitoring
- Comprehensive backup and recovery system
- Health monitoring and alerting

## 🎉 Tutorial Complete!

Congratulations! You have successfully completed the **Clinic Management System Tutorial**. You now have:

### ✅ **Complete Healthcare Management System**
- Patient management with BPJS integration
- Doctor and staff management
- Appointment scheduling system
- Medical records and prescriptions
- Billing and payment processing
- Multi-clinic support
- Comprehensive reporting and analytics

### ✅ **Production-Ready Application**
- Secure authentication and authorization
- Role-based access control
- Data validation and security
- Automated testing suite
- Production deployment configuration
- Monitoring and backup systems

### 🚀 **Next Steps**

1. **Customize for Your Needs**: Adapt the system to your specific clinic requirements
2. **Add Advanced Features**: Implement telemedicine, lab integration, or mobile apps
3. **Scale Your System**: Deploy across multiple locations and integrate with hospital systems
4. **Contribute**: Share improvements and contribute to the healthcare technology community

**Thank you for following this comprehensive tutorial!** You now have the knowledge and tools to build and maintain a professional healthcare management system.

---

**Tutorial Series Complete** 🎯
**Total Chapters**: 14 | **Status**: ✅ Complete
**Ready for Production Deployment** 🚀
