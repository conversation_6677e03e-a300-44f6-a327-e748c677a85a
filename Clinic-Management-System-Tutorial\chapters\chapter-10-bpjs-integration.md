# Chapter 10: BPJS Integration

## Integrating Indonesian Health Insurance (BPJS) API

In this chapter, we'll integrate the Indonesian Health Insurance (BPJS) system using the @ssecd/jkn package. This integration will enable patient verification, claim processing, and insurance coverage validation directly within our clinic management system.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Understand BPJS system architecture and API structure
- Install and configure the @ssecd/jkn package
- Implement patient verification through BPJS
- Build claim processing workflows
- Create insurance coverage validation
- Handle BPJS error scenarios and fallbacks
- Design user interfaces for BPJS operations

## 🏥 Understanding BPJS Integration

### BPJS System Overview

BPJS (Badan Penyelenggara Jaminan Sosial) is Indonesia's national health insurance system. The integration provides:

1. **Patient Verification**: Validate patient BPJS membership
2. **Eligibility Checking**: Verify coverage and benefits
3. **Claim Processing**: Submit and track insurance claims
4. **Provider Management**: Manage healthcare provider information
5. **Referral System**: Handle patient referrals between facilities

### API Services Available

- **VClaim**: Claims processing and management
- **Aplicares**: Healthcare provider applications
- **Antrean**: Queue management system
- **Apotek**: Pharmacy services
- **i-Care**: Integrated care services
- **Rekam Medis**: Medical records integration

## 🛠 Installation and Configuration

### Step 1: Install BPJS Package

Since we're using Laravel (PHP), we'll create a PHP wrapper for the Node.js package or use HTTP client to interact with BPJS APIs directly:

```bash
# Install HTTP client for API calls
composer require guzzlehttp/guzzle

# Create BPJS service
php artisan make:service BPJSService
```

### Step 2: Environment Configuration

Update your `.env` file with BPJS credentials:

```env
# BPJS Configuration
BPJS_PPK_CODE=your_ppk_code
BPJS_CONS_ID=your_cons_id
BPJS_CONS_SECRET=your_cons_secret
BPJS_VCLAIM_USER_KEY=your_vclaim_key
BPJS_ANTREAN_USER_KEY=your_antrean_key
BPJS_APOTEK_USER_KEY=your_apotek_key
BPJS_ICARE_USER_KEY=your_icare_key
BPJS_REKAM_MEDIS_USER_KEY=your_rekam_medis_key

# BPJS Environment (development/production)
BPJS_ENVIRONMENT=development

# BPJS Base URLs
BPJS_VCLAIM_BASE_URL=https://apijkn-dev.bpjs-kesehatan.go.id/vclaim-rest-dev
BPJS_ANTREAN_BASE_URL=https://apijkn-dev.bpjs-kesehatan.go.id/antrean-rs-dev
```

### Step 3: BPJS Configuration File

Create **config/bpjs.php**:

```php
<?php

return [
    'ppk_code' => env('BPJS_PPK_CODE'),
    'cons_id' => env('BPJS_CONS_ID'),
    'cons_secret' => env('BPJS_CONS_SECRET'),
    
    'user_keys' => [
        'vclaim' => env('BPJS_VCLAIM_USER_KEY'),
        'antrean' => env('BPJS_ANTREAN_USER_KEY'),
        'apotek' => env('BPJS_APOTEK_USER_KEY'),
        'icare' => env('BPJS_ICARE_USER_KEY'),
        'rekam_medis' => env('BPJS_REKAM_MEDIS_USER_KEY'),
    ],
    
    'environment' => env('BPJS_ENVIRONMENT', 'development'),
    
    'base_urls' => [
        'development' => [
            'vclaim' => 'https://apijkn-dev.bpjs-kesehatan.go.id/vclaim-rest-dev',
            'antrean' => 'https://apijkn-dev.bpjs-kesehatan.go.id/antrean-rs-dev',
            'apotek' => 'https://apijkn-dev.bpjs-kesehatan.go.id/apotek-rest-dev',
        ],
        'production' => [
            'vclaim' => 'https://apijkn.bpjs-kesehatan.go.id/vclaim-rest',
            'antrean' => 'https://apijkn.bpjs-kesehatan.go.id/antrean-rs',
            'apotek' => 'https://apijkn.bpjs-kesehatan.go.id/apotek-rest',
        ],
    ],
    
    'timeout' => 30,
    'retry_attempts' => 3,
];
```

## 🔧 BPJS Service Implementation

### Step 1: Base BPJS Service

Create **app/Services/BPJSService.php**:

```php
<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BPJSService
{
    protected $client;
    protected $config;
    
    public function __construct()
    {
        $this->config = config('bpjs');
        $this->client = new Client([
            'timeout' => $this->config['timeout'],
            'verify' => false, // For development only
        ]);
    }
    
    /**
     * Generate signature for BPJS API authentication
     */
    protected function generateSignature(string $service): string
    {
        $consId = $this->config['cons_id'];
        $consSecret = $this->config['cons_secret'];
        $userKey = $this->config['user_keys'][$service];
        $timestamp = time();
        
        $signature = hash_hmac('sha256', $consId . '&' . $timestamp, $consSecret, true);
        $encodedSignature = base64_encode($signature);
        
        return $encodedSignature;
    }
    
    /**
     * Get headers for BPJS API request
     */
    protected function getHeaders(string $service): array
    {
        $timestamp = time();
        $signature = $this->generateSignature($service);
        
        return [
            'X-cons-id' => $this->config['cons_id'],
            'X-timestamp' => $timestamp,
            'X-signature' => $signature,
            'user_key' => $this->config['user_keys'][$service],
            'Content-Type' => 'application/json',
        ];
    }
    
    /**
     * Make API request to BPJS
     */
    protected function makeRequest(string $service, string $endpoint, array $data = [], string $method = 'GET')
    {
        $baseUrl = $this->config['base_urls'][$this->config['environment']][$service];
        $url = $baseUrl . $endpoint;
        
        $options = [
            'headers' => $this->getHeaders($service),
        ];
        
        if ($method === 'POST' && !empty($data)) {
            $options['json'] = $data;
        }
        
        try {
            $response = $this->client->request($method, $url, $options);
            $body = json_decode($response->getBody()->getContents(), true);
            
            Log::info('BPJS API Request', [
                'service' => $service,
                'endpoint' => $endpoint,
                'method' => $method,
                'response_code' => $response->getStatusCode(),
                'response' => $body
            ]);
            
            return $body;
            
        } catch (RequestException $e) {
            Log::error('BPJS API Error', [
                'service' => $service,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            
            throw $e;
        }
    }
}
```

### Step 2: Patient Verification Service

Extend the BPJS service for patient operations:

```php
<?php

namespace App\Services;

class BPJSPatientService extends BPJSService
{
    /**
     * Verify patient by NIK (National ID)
     */
    public function verifyPatientByNIK(string $nik, string $tanggal = null): array
    {
        $tanggal = $tanggal ?: Carbon::now()->format('Y-m-d');
        $endpoint = "/Peserta/nik/{$nik}/tglSEP/{$tanggal}";
        
        return $this->makeRequest('vclaim', $endpoint);
    }
    
    /**
     * Verify patient by BPJS card number
     */
    public function verifyPatientByCard(string $cardNumber, string $tanggal = null): array
    {
        $tanggal = $tanggal ?: Carbon::now()->format('Y-m-d');
        $endpoint = "/Peserta/nokartu/{$cardNumber}/tglSEP/{$tanggal}";
        
        return $this->makeRequest('vclaim', $endpoint);
    }
    
    /**
     * Get patient eligibility information
     */
    public function getPatientEligibility(string $cardNumber, string $serviceType = '2'): array
    {
        $endpoint = "/Peserta/nokartu/{$cardNumber}/tglSEP/" . Carbon::now()->format('Y-m-d');
        
        return $this->makeRequest('vclaim', $endpoint);
    }
    
    /**
     * Search healthcare facilities
     */
    public function searchHealthcareFacilities(string $keyword, int $type = 2): array
    {
        $endpoint = "/referensi/faskes/{$keyword}/{$type}";
        
        return $this->makeRequest('vclaim', $endpoint);
    }
    
    /**
     * Get diagnosis reference
     */
    public function getDiagnosisReference(string $keyword): array
    {
        $endpoint = "/referensi/diagnosa/{$keyword}";
        
        return $this->makeRequest('vclaim', $endpoint);
    }
    
    /**
     * Get procedure reference
     */
    public function getProcedureReference(string $keyword): array
    {
        $endpoint = "/referensi/procedure/{$keyword}";
        
        return $this->makeRequest('vclaim', $endpoint);
    }
}
```

### Step 3: SEP (Surat Eligibilitas Peserta) Management

Create SEP management service:

```php
<?php

namespace App\Services;

use Carbon\Carbon;

class BPJSSEPService extends BPJSService
{
    /**
     * Create SEP (Surat Eligibilitas Peserta)
     */
    public function createSEP(array $sepData): array
    {
        $endpoint = '/SEP/2.0/insert';
        
        $requestData = [
            'request' => [
                't_sep' => [
                    'noKartu' => $sepData['no_kartu'],
                    'tglSep' => $sepData['tgl_sep'],
                    'ppkPelayanan' => $this->config['ppk_code'],
                    'jnsPelayanan' => $sepData['jns_pelayanan'], // 1=Rawat Inap, 2=Rawat Jalan
                    'klsRawat' => $sepData['kls_rawat'],
                    'noMR' => $sepData['no_mr'],
                    'rujukan' => [
                        'asalRujukan' => $sepData['asal_rujukan'],
                        'tglRujukan' => $sepData['tgl_rujukan'],
                        'noRujukan' => $sepData['no_rujukan'],
                        'ppkRujukan' => $sepData['ppk_rujukan'],
                    ],
                    'catatan' => $sepData['catatan'] ?? '',
                    'diagAwal' => $sepData['diag_awal'],
                    'poli' => [
                        'tujuan' => $sepData['poli_tujuan'],
                        'eksekutif' => $sepData['poli_eksekutif'] ?? '0',
                    ],
                    'cob' => [
                        'cob' => $sepData['cob'] ?? '0',
                    ],
                    'katarak' => [
                        'katarak' => $sepData['katarak'] ?? '0',
                    ],
                    'jaminan' => [
                        'lakaLantas' => $sepData['laka_lantas'] ?? '0',
                        'noLP' => $sepData['no_lp'] ?? '',
                        'penjamin' => [
                            'tglKejadian' => $sepData['tgl_kejadian'] ?? '',
                            'keterangan' => $sepData['keterangan_kejadian'] ?? '',
                            'suplesi' => [
                                'suplesi' => $sepData['suplesi'] ?? '0',
                                'noSepSuplesi' => $sepData['no_sep_suplesi'] ?? '',
                                'lokasiLaka' => [
                                    'kdPropinsi' => $sepData['kd_propinsi'] ?? '',
                                    'kdKabupaten' => $sepData['kd_kabupaten'] ?? '',
                                    'kdKecamatan' => $sepData['kd_kecamatan'] ?? '',
                                ],
                            ],
                        ],
                    ],
                    'tujuanKunj' => $sepData['tujuan_kunj'] ?? '0',
                    'flagProcedure' => $sepData['flag_procedure'] ?? '',
                    'kdPenunjang' => $sepData['kd_penunjang'] ?? '',
                    'assesmentPel' => $sepData['assesment_pel'] ?? '',
                    'skdp' => [
                        'noSurat' => $sepData['no_surat_skdp'] ?? '',
                        'kodeDPJP' => $sepData['kode_dpjp'] ?? '',
                    ],
                    'dpjpLayan' => $sepData['dpjp_layan'] ?? '',
                    'noTelp' => $sepData['no_telp'] ?? '',
                    'user' => auth()->user()->name ?? 'System',
                ]
            ]
        ];
        
        return $this->makeRequest('vclaim', $endpoint, $requestData, 'POST');
    }
    
    /**
     * Get SEP details
     */
    public function getSEP(string $sepNumber): array
    {
        $endpoint = "/SEP/{$sepNumber}";
        
        return $this->makeRequest('vclaim', $endpoint);
    }
    
    /**
     * Update SEP
     */
    public function updateSEP(string $sepNumber, array $updateData): array
    {
        $endpoint = '/SEP/2.0/update';
        
        $requestData = [
            'request' => [
                't_sep' => array_merge([
                    'noSep' => $sepNumber,
                    'user' => auth()->user()->name ?? 'System',
                ], $updateData)
            ]
        ];
        
        return $this->makeRequest('vclaim', $endpoint, $requestData, 'PUT');
    }
    
    /**
     * Delete SEP
     */
    public function deleteSEP(string $sepNumber, string $reason): array
    {
        $endpoint = '/SEP/delete';
        
        $requestData = [
            'request' => [
                't_sep' => [
                    'noSep' => $sepNumber,
                    'user' => auth()->user()->name ?? 'System',
                ]
            ]
        ];
        
        return $this->makeRequest('vclaim', $endpoint, $requestData, 'DELETE');
    }
}
```

## 🗄 Database Integration

### Step 1: BPJS Data Migration

Create migration for BPJS data storage:

```bash
php artisan make:migration create_bpjs_verifications_table
php artisan make:migration create_bpjs_seps_table
```

**database/migrations/xxxx_create_bpjs_verifications_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bpjs_verifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->string('verification_type'); // 'nik' or 'card'
            $table->string('identifier'); // NIK or card number
            $table->json('response_data'); // Store BPJS response
            $table->boolean('is_valid')->default(false);
            $table->string('status'); // 'active', 'inactive', 'error'
            $table->text('error_message')->nullable();
            $table->timestamp('verified_at');
            $table->timestamps();
            
            $table->index(['patient_id', 'verification_type']);
            $table->index(['identifier', 'verification_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bpjs_verifications');
    }
};
```

### Step 2: BPJS Models

Create **app/Models/BPJSVerification.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BPJSVerification extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id', 'verification_type', 'identifier', 'response_data',
        'is_valid', 'status', 'error_message', 'verified_at'
    ];

    protected function casts(): array
    {
        return [
            'response_data' => 'array',
            'is_valid' => 'boolean',
            'verified_at' => 'datetime',
        ];
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }
}
```

## 🎮 BPJS Controller Implementation

Create **app/Http/Controllers/Clinic/BPJSController.php**:

```php
<?php

namespace App\Http\Controllers\Clinic;

use App\Http\Controllers\Controller;
use App\Models\Patient;
use App\Models\BPJSVerification;
use App\Services\BPJSPatientService;
use App\Services\BPJSSEPService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BPJSController extends Controller
{
    protected $bpjsPatientService;
    protected $bpjsSEPService;

    public function __construct(BPJSPatientService $bpjsPatientService, BPJSSEPService $bpjsSEPService)
    {
        $this->bpjsPatientService = $bpjsPatientService;
        $this->bpjsSEPService = $bpjsSEPService;
    }

    /**
     * Verify patient BPJS eligibility
     */
    public function verifyPatient(Request $request, Patient $patient)
    {
        $request->validate([
            'verification_type' => 'required|in:nik,card',
            'identifier' => 'required|string',
        ]);

        try {
            $verificationType = $request->verification_type;
            $identifier = $request->identifier;

            // Call BPJS API based on verification type
            if ($verificationType === 'nik') {
                $response = $this->bpjsPatientService->verifyPatientByNIK($identifier);
            } else {
                $response = $this->bpjsPatientService->verifyPatientByCard($identifier);
            }

            // Store verification result
            $verification = BPJSVerification::create([
                'patient_id' => $patient->id,
                'verification_type' => $verificationType,
                'identifier' => $identifier,
                'response_data' => $response,
                'is_valid' => isset($response['response']) && $response['metaData']['code'] === '200',
                'status' => $response['metaData']['code'] === '200' ? 'active' : 'error',
                'error_message' => $response['metaData']['code'] !== '200' ? $response['metaData']['message'] : null,
                'verified_at' => now(),
            ]);

            // Update patient BPJS information if verification successful
            if ($verification->is_valid && isset($response['response'])) {
                $bpjsData = $response['response'];
                $patient->update([
                    'has_bpjs' => true,
                    'bpjs_number' => $bpjsData['noKartu'] ?? $patient->bpjs_number,
                    'bpjs_class' => $bpjsData['hakKelas']['kode'] ?? $patient->bpjs_class,
                ]);
            }

            return response()->json([
                'success' => $verification->is_valid,
                'message' => $verification->is_valid ? 'Patient BPJS verification successful' : 'BPJS verification failed',
                'data' => $verification,
                'bpjs_data' => $verification->is_valid ? $response['response'] : null,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'BPJS verification failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create SEP for patient
     */
    public function createSEP(Request $request, Patient $patient)
    {
        $request->validate([
            'jns_pelayanan' => 'required|in:1,2', // 1=Rawat Inap, 2=Rawat Jalan
            'kls_rawat' => 'required|in:1,2,3',
            'asal_rujukan' => 'required|in:1,2', // 1=Faskes 1, 2=Faskes 2
            'tgl_rujukan' => 'required|date',
            'no_rujukan' => 'required|string',
            'ppk_rujukan' => 'required|string',
            'diag_awal' => 'required|string',
            'poli_tujuan' => 'required|string',
            'catatan' => 'nullable|string',
        ]);

        try {
            $sepData = [
                'no_kartu' => $patient->bpjs_number,
                'tgl_sep' => Carbon::now()->format('Y-m-d'),
                'jns_pelayanan' => $request->jns_pelayanan,
                'kls_rawat' => $request->kls_rawat,
                'no_mr' => $patient->patient_number,
                'asal_rujukan' => $request->asal_rujukan,
                'tgl_rujukan' => $request->tgl_rujukan,
                'no_rujukan' => $request->no_rujukan,
                'ppk_rujukan' => $request->ppk_rujukan,
                'diag_awal' => $request->diag_awal,
                'poli_tujuan' => $request->poli_tujuan,
                'catatan' => $request->catatan,
            ];

            $response = $this->bpjsSEPService->createSEP($sepData);

            if ($response['metaData']['code'] === '200') {
                // Store SEP information in database
                // You can create a SEP model to store this data

                return response()->json([
                    'success' => true,
                    'message' => 'SEP created successfully',
                    'data' => $response['response'],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create SEP: ' . $response['metaData']['message'],
                    'error' => $response['metaData']['message'],
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'SEP creation failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get healthcare facilities reference
     */
    public function getHealthcareFacilities(Request $request)
    {
        $request->validate([
            'keyword' => 'required|string|min:3',
            'type' => 'nullable|integer|in:1,2', // 1=Faskes 1, 2=Faskes 2
        ]);

        try {
            $response = $this->bpjsPatientService->searchHealthcareFacilities(
                $request->keyword,
                $request->type ?? 2
            );

            return response()->json([
                'success' => $response['metaData']['code'] === '200',
                'data' => $response['response'] ?? [],
                'message' => $response['metaData']['message'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch healthcare facilities: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get diagnosis reference
     */
    public function getDiagnosisReference(Request $request)
    {
        $request->validate([
            'keyword' => 'required|string|min:3',
        ]);

        try {
            $response = $this->bpjsPatientService->getDiagnosisReference($request->keyword);

            return response()->json([
                'success' => $response['metaData']['code'] === '200',
                'data' => $response['response'] ?? [],
                'message' => $response['metaData']['message'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch diagnosis reference: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
```

## 🎨 Frontend BPJS Components

### Step 1: BPJS Verification Component

Create **resources/js/components/clinic/BPJSVerification.tsx**:

```tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { Patient } from '@/types/clinic';

interface Props {
    patient: Patient;
    onVerificationComplete?: (result: any) => void;
}

export default function BPJSVerification({ patient, onVerificationComplete }: Props) {
    const [verificationType, setVerificationType] = useState<'nik' | 'card'>('card');
    const [identifier, setIdentifier] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);
    const [verificationResult, setVerificationResult] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);

    const handleVerification = async () => {
        if (!identifier.trim()) {
            setError('Please enter NIK or BPJS card number');
            return;
        }

        setIsVerifying(true);
        setError(null);

        try {
            const response = await fetch(route('clinic.bpjs.verify', patient.id), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    verification_type: verificationType,
                    identifier: identifier,
                }),
            });

            const result = await response.json();

            if (result.success) {
                setVerificationResult(result);
                onVerificationComplete?.(result);
            } else {
                setError(result.message || 'Verification failed');
            }

        } catch (err) {
            setError('Network error occurred during verification');
        } finally {
            setIsVerifying(false);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    BPJS Verification
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {patient.has_bpjs && (
                    <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            Patient has BPJS coverage. Card Number: {patient.bpjs_number}
                        </AlertDescription>
                    </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="verification-type">Verification Type</Label>
                        <Select value={verificationType} onValueChange={(value: 'nik' | 'card') => setVerificationType(value)}>
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="nik">NIK (National ID)</SelectItem>
                                <SelectItem value="card">BPJS Card Number</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="identifier">
                            {verificationType === 'nik' ? 'NIK' : 'BPJS Card Number'}
                        </Label>
                        <Input
                            id="identifier"
                            value={identifier}
                            onChange={(e) => setIdentifier(e.target.value)}
                            placeholder={verificationType === 'nik' ? 'Enter NIK' : 'Enter BPJS card number'}
                        />
                    </div>
                </div>

                <Button
                    onClick={handleVerification}
                    disabled={isVerifying || !identifier.trim()}
                    className="w-full"
                >
                    {isVerifying ? (
                        <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Verifying...
                        </>
                    ) : (
                        <>
                            <Shield className="h-4 w-4 mr-2" />
                            Verify BPJS Eligibility
                        </>
                    )}
                </Button>

                {error && (
                    <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}

                {verificationResult && (
                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            {verificationResult.success ? (
                                <Badge variant="default" className="bg-green-500">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Verified
                                </Badge>
                            ) : (
                                <Badge variant="destructive">
                                    <XCircle className="h-3 w-3 mr-1" />
                                    Failed
                                </Badge>
                            )}
                        </div>

                        {verificationResult.success && verificationResult.bpjs_data && (
                            <div className="bg-muted p-3 rounded-lg space-y-2 text-sm">
                                <div><strong>Name:</strong> {verificationResult.bpjs_data.nama}</div>
                                <div><strong>Card Number:</strong> {verificationResult.bpjs_data.noKartu}</div>
                                <div><strong>NIK:</strong> {verificationResult.bpjs_data.nik}</div>
                                <div><strong>Class:</strong> {verificationResult.bpjs_data.hakKelas?.keterangan}</div>
                                <div><strong>Status:</strong> {verificationResult.bpjs_data.statusPeserta?.keterangan}</div>
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Integrated BPJS API services** for Indonesian health insurance
2. ✅ **Implemented patient verification** through NIK and card number
3. ✅ **Built SEP creation and management** system
4. ✅ **Created healthcare facility reference** lookup
5. ✅ **Designed BPJS verification UI** components
6. ✅ **Set up error handling and logging** for API calls

### What We Have Now

- Complete BPJS integration with patient verification
- SEP (Surat Eligibilitas Peserta) management system
- Healthcare facility and diagnosis reference lookup
- Secure API authentication and error handling
- User-friendly BPJS verification interface

### Next Steps

In **Chapter 11: Reporting and Analytics**, we'll:

- Build comprehensive reporting dashboard
- Create patient statistics and analytics
- Implement appointment and revenue reports
- Design data visualization components

---

**Ready to continue?** Proceed to [Chapter 11: Reporting and Analytics](./chapter-11-reporting.md) to build the reporting and analytics system.
