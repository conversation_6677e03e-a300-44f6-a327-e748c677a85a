# Chapter 9: Billing and Payment System

## Building Comprehensive Financial Management

In this chapter, we'll create a complete billing and payment system that handles invoice generation, payment processing, insurance claims, and financial reporting for healthcare services.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create billing and invoice management system
- Build payment processing and tracking
- Implement insurance claim handling
- Design financial reporting features
- Set up automated billing workflows
- Create payment reconciliation system

## 💰 Billing System Requirements

### Core Features

1. **Invoice Generation**: Automated billing for services and treatments
2. **Payment Processing**: Multiple payment methods and tracking
3. **Insurance Claims**: BPJS and private insurance integration
4. **Financial Reporting**: Revenue tracking and financial analytics
5. **Payment Plans**: Installment and payment plan management
6. **Billing Automation**: Automated billing workflows and reminders

## 🛠 Backend Implementation

### Step 1: Billing Tables Migration

```bash
php artisan make:migration create_billing_table
php artisan make:migration create_billing_items_table
php artisan make:migration create_payments_table
php artisan make:migration create_insurance_claims_table
```

**database/migrations/xxxx_create_billing_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('billing', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Invoice details
            $table->string('invoice_number')->unique();
            $table->date('invoice_date');
            $table->date('due_date');
            
            // Financial details
            $table->decimal('subtotal', 12, 2)->default(0);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2)->default(0);
            $table->decimal('paid_amount', 12, 2)->default(0);
            $table->decimal('outstanding_amount', 12, 2)->default(0);
            
            // Status and type
            $table->enum('status', ['draft', 'sent', 'paid', 'partial', 'overdue', 'cancelled'])->default('draft');
            $table->enum('payment_status', ['unpaid', 'partial', 'paid', 'refunded'])->default('unpaid');
            $table->enum('billing_type', ['service', 'consultation', 'procedure', 'medication', 'other'])->default('service');
            
            // Insurance information
            $table->boolean('uses_insurance')->default(false);
            $table->string('insurance_provider')->nullable();
            $table->string('insurance_policy_number')->nullable();
            $table->decimal('insurance_coverage_amount', 12, 2)->default(0);
            $table->decimal('patient_responsibility', 12, 2)->default(0);
            
            // Additional information
            $table->text('notes')->nullable();
            $table->text('payment_terms')->nullable();
            $table->json('billing_address')->nullable();
            
            // Timestamps
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
            
            $table->index(['patient_id', 'status']);
            $table->index(['clinic_id', 'invoice_date']);
            $table->index(['status', 'due_date']);
            $table->index('payment_status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('billing');
    }
};
```

**database/migrations/xxxx_create_billing_items_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('billing_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('billing_id')->constrained('billing')->onDelete('cascade');
            
            // Item details
            $table->string('item_type'); // service, medication, procedure, consultation
            $table->string('item_code')->nullable(); // Service code or medication code
            $table->string('description');
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 12, 2);
            
            // Service provider information
            $table->foreignId('provider_id')->nullable()->constrained('users')->onDelete('set null');
            $table->date('service_date')->nullable();
            
            // Insurance coverage
            $table->decimal('insurance_covered_amount', 10, 2)->default(0);
            $table->decimal('patient_portion', 10, 2)->default(0);
            
            // Additional details
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional item-specific data
            
            $table->timestamps();
            
            $table->index(['billing_id', 'item_type']);
            $table->index('provider_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('billing_items');
    }
};
```

**database/migrations/xxxx_create_payments_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('billing_id')->constrained('billing')->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('processed_by')->constrained('users')->onDelete('cascade');
            
            // Payment details
            $table->string('payment_number')->unique();
            $table->decimal('amount', 12, 2);
            $table->date('payment_date');
            $table->enum('payment_method', ['cash', 'credit_card', 'debit_card', 'bank_transfer', 'insurance', 'other']);
            
            // Payment processing information
            $table->string('transaction_id')->nullable();
            $table->string('reference_number')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
            
            // Payment gateway information (if applicable)
            $table->string('gateway_provider')->nullable();
            $table->json('gateway_response')->nullable();
            
            // Additional information
            $table->text('notes')->nullable();
            $table->timestamp('processed_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['billing_id', 'status']);
            $table->index(['patient_id', 'payment_date']);
            $table->index('payment_method');
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
```

### Step 2: Billing Models

**app/Models/Billing.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Billing extends Model
{
    use HasFactory;

    protected $table = 'billing';

    protected $fillable = [
        'patient_id', 'appointment_id', 'clinic_id', 'created_by',
        'invoice_number', 'invoice_date', 'due_date', 'subtotal',
        'tax_amount', 'discount_amount', 'total_amount', 'paid_amount',
        'outstanding_amount', 'status', 'payment_status', 'billing_type',
        'uses_insurance', 'insurance_provider', 'insurance_policy_number',
        'insurance_coverage_amount', 'patient_responsibility', 'notes',
        'payment_terms', 'billing_address'
    ];

    protected function casts(): array
    {
        return [
            'invoice_date' => 'date',
            'due_date' => 'date',
            'subtotal' => 'decimal:2',
            'tax_amount' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'paid_amount' => 'decimal:2',
            'outstanding_amount' => 'decimal:2',
            'insurance_coverage_amount' => 'decimal:2',
            'patient_responsibility' => 'decimal:2',
            'uses_insurance' => 'boolean',
            'billing_address' => 'array',
            'sent_at' => 'datetime',
            'paid_at' => 'datetime',
        ];
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items()
    {
        return $this->hasMany(BillingItem::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    // Scopes
    public function scopeUnpaid($query)
    {
        return $query->whereIn('payment_status', ['unpaid', 'partial']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', today())
            ->whereIn('payment_status', ['unpaid', 'partial']);
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    // Helper methods
    public function calculateTotals()
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->outstanding_amount = $this->total_amount - $this->paid_amount;
        
        // Update payment status based on amounts
        if ($this->paid_amount >= $this->total_amount) {
            $this->payment_status = 'paid';
            $this->paid_at = $this->paid_at ?: now();
        } elseif ($this->paid_amount > 0) {
            $this->payment_status = 'partial';
        } else {
            $this->payment_status = 'unpaid';
        }
        
        $this->save();
    }

    public function addPayment($amount, $method, $processedBy, $additionalData = [])
    {
        $payment = $this->payments()->create([
            'patient_id' => $this->patient_id,
            'amount' => $amount,
            'payment_date' => now(),
            'payment_method' => $method,
            'processed_by' => $processedBy,
            'status' => 'completed',
            'processed_at' => now(),
            ...$additionalData
        ]);

        $this->paid_amount += $amount;
        $this->calculateTotals();

        return $payment;
    }

    public function markAsSent()
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now()
        ]);
    }

    public function isOverdue()
    {
        return $this->due_date->isPast() && in_array($this->payment_status, ['unpaid', 'partial']);
    }

    public function getBalanceDueAttribute()
    {
        return $this->total_amount - $this->paid_amount;
    }

    // Boot method for auto-generating invoice number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($billing) {
            if (empty($billing->invoice_number)) {
                $billing->invoice_number = static::generateInvoiceNumber();
            }
        });
    }

    public static function generateInvoiceNumber()
    {
        $date = now()->format('Ymd');
        $lastBilling = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastBilling) {
            $lastNumber = substr($lastBilling->invoice_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return 'INV' . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

**app/Models/BillingItem.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BillingItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'billing_id', 'item_type', 'item_code', 'description', 'quantity',
        'unit_price', 'total_price', 'provider_id', 'service_date',
        'insurance_covered_amount', 'patient_portion', 'notes', 'metadata'
    ];

    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'total_price' => 'decimal:2',
            'insurance_covered_amount' => 'decimal:2',
            'patient_portion' => 'decimal:2',
            'service_date' => 'date',
            'metadata' => 'array',
        ];
    }

    // Relationships
    public function billing()
    {
        return $this->belongsTo(Billing::class);
    }

    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    // Helper methods
    public function calculateTotal()
    {
        $this->total_price = $this->quantity * $this->unit_price;
        $this->save();
    }
}
```

**app/Models/Payment.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'billing_id', 'patient_id', 'processed_by', 'payment_number',
        'amount', 'payment_date', 'payment_method', 'transaction_id',
        'reference_number', 'status', 'gateway_provider', 'gateway_response',
        'notes'
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'payment_date' => 'date',
            'gateway_response' => 'array',
            'processed_at' => 'datetime',
        ];
    }

    // Relationships
    public function billing()
    {
        return $this->belongsTo(Billing::class);
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function processedBy()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    // Boot method for auto-generating payment number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_number)) {
                $payment->payment_number = static::generatePaymentNumber();
            }
        });
    }

    public static function generatePaymentNumber()
    {
        $date = now()->format('Ymd');
        $lastPayment = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastPayment) {
            $lastNumber = substr($lastPayment->payment_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return 'PAY' . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

### Step 3: Billing Service

**app/Services/BillingService.php**:

```php
<?php

namespace App\Services;

use App\Models\Billing;
use App\Models\BillingItem;
use App\Models\Appointment;
use App\Models\Patient;
use Illuminate\Support\Facades\DB;

class BillingService
{
    /**
     * Create billing from appointment
     */
    public function createBillingFromAppointment(Appointment $appointment, array $services = []): Billing
    {
        DB::beginTransaction();
        
        try {
            // Create billing record
            $billing = Billing::create([
                'patient_id' => $appointment->patient_id,
                'appointment_id' => $appointment->id,
                'clinic_id' => $appointment->clinic_id,
                'created_by' => auth()->id(),
                'invoice_date' => now(),
                'due_date' => now()->addDays(30),
                'billing_type' => 'consultation',
                'uses_insurance' => $appointment->uses_bpjs,
            ]);

            // Add consultation fee
            if ($appointment->doctor->doctor) {
                $billing->items()->create([
                    'item_type' => 'consultation',
                    'item_code' => 'CONSULT',
                    'description' => 'Medical Consultation - ' . $appointment->doctor->name,
                    'quantity' => 1,
                    'unit_price' => $appointment->doctor->doctor->consultation_fee,
                    'total_price' => $appointment->doctor->doctor->consultation_fee,
                    'provider_id' => $appointment->doctor_id,
                    'service_date' => $appointment->appointment_date,
                ]);
            }

            // Add additional services
            foreach ($services as $service) {
                $billing->items()->create([
                    'item_type' => $service['type'],
                    'item_code' => $service['code'] ?? null,
                    'description' => $service['description'],
                    'quantity' => $service['quantity'] ?? 1,
                    'unit_price' => $service['unit_price'],
                    'total_price' => ($service['quantity'] ?? 1) * $service['unit_price'],
                    'provider_id' => $service['provider_id'] ?? $appointment->doctor_id,
                    'service_date' => $appointment->appointment_date,
                ]);
            }

            // Calculate totals
            $billing->calculateTotals();

            DB::commit();
            return $billing;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process payment for billing
     */
    public function processPayment(Billing $billing, array $paymentData): bool
    {
        try {
            $payment = $billing->addPayment(
                $paymentData['amount'],
                $paymentData['method'],
                auth()->id(),
                [
                    'transaction_id' => $paymentData['transaction_id'] ?? null,
                    'reference_number' => $paymentData['reference_number'] ?? null,
                    'notes' => $paymentData['notes'] ?? null,
                ]
            );

            return $payment->status === 'completed';

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get billing statistics for a clinic
     */
    public function getBillingStats($clinicId, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ?: now()->startOfMonth();
        $endDate = $endDate ?: now()->endOfMonth();

        $query = Billing::where('clinic_id', $clinicId)
            ->whereBetween('invoice_date', [$startDate, $endDate]);

        return [
            'total_invoices' => $query->count(),
            'total_revenue' => $query->sum('total_amount'),
            'total_paid' => $query->sum('paid_amount'),
            'total_outstanding' => $query->sum('outstanding_amount'),
            'paid_invoices' => $query->clone()->where('payment_status', 'paid')->count(),
            'unpaid_invoices' => $query->clone()->where('payment_status', 'unpaid')->count(),
            'overdue_invoices' => $query->clone()->overdue()->count(),
            'average_invoice_amount' => $query->avg('total_amount'),
        ];
    }

    /**
     * Generate aging report
     */
    public function getAgingReport($clinicId): array
    {
        $current = Billing::where('clinic_id', $clinicId)
            ->unpaid()
            ->where('due_date', '>=', now())
            ->sum('outstanding_amount');

        $days30 = Billing::where('clinic_id', $clinicId)
            ->unpaid()
            ->whereBetween('due_date', [now()->subDays(30), now()->subDay()])
            ->sum('outstanding_amount');

        $days60 = Billing::where('clinic_id', $clinicId)
            ->unpaid()
            ->whereBetween('due_date', [now()->subDays(60), now()->subDays(31)])
            ->sum('outstanding_amount');

        $days90 = Billing::where('clinic_id', $clinicId)
            ->unpaid()
            ->whereBetween('due_date', [now()->subDays(90), now()->subDays(61)])
            ->sum('outstanding_amount');

        $over90 = Billing::where('clinic_id', $clinicId)
            ->unpaid()
            ->where('due_date', '<', now()->subDays(90))
            ->sum('outstanding_amount');

        return [
            'current' => $current,
            '1-30_days' => $days30,
            '31-60_days' => $days60,
            '61-90_days' => $days90,
            'over_90_days' => $over90,
            'total_outstanding' => $current + $days30 + $days60 + $days90 + $over90,
        ];
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive billing system** with invoice generation
2. ✅ **Implemented payment processing** with multiple payment methods
3. ✅ **Created billing item management** for services and procedures
4. ✅ **Designed payment tracking** and reconciliation system
5. ✅ **Added billing statistics** and financial reporting
6. ✅ **Built billing service layer** for business logic automation

### What We Have Now

- Complete billing and invoice management system
- Payment processing with multiple payment methods
- Financial reporting and analytics capabilities
- Billing automation and workflow management
- Integration with appointments and medical services

### Next Steps

In **Chapter 11: Reporting and Analytics**, we'll:

- Build comprehensive reporting dashboard
- Create patient statistics and analytics
- Implement appointment and revenue reports
- Design data visualization components

---

**Ready to continue?** Proceed to [Chapter 11: Reporting and Analytics](./chapter-11-reporting.md) to build the reporting system.
