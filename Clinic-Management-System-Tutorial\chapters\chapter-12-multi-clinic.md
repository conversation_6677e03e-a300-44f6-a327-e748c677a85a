# Chapter 12: Multi-Clinic Support

## Building Multi-Tenant Architecture

In this chapter, we'll implement multi-tenant architecture to support multiple clinic branches with centralized management, branch-specific data isolation, and cross-clinic administrative capabilities.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Implement multi-tenant architecture for multiple clinics
- Create clinic management and switching interfaces
- Design cross-clinic data access controls
- Build centralized administration features
- Set up clinic-specific configurations and branding
- Create inter-clinic patient referral system

## 🏢 Multi-Clinic Requirements

### Core Features

1. **Clinic Management**: Create and manage multiple clinic branches
2. **Data Isolation**: Secure separation of clinic-specific data
3. **Centralized Administration**: Super admin controls across all clinics
4. **Clinic Switching**: Easy switching between clinic contexts
5. **Cross-Clinic Features**: Patient referrals and shared resources
6. **Clinic Branding**: Custom branding and configurations per clinic

## 🛠 Backend Implementation

### Step 1: Enhanced Clinic Management

**app/Models/Clinic.php** (Enhanced):

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Clinic extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'code', 'address', 'phone', 'email', 'license_number',
        'tax_id', 'bpjs_provider_code', 'bpjs_provider_type',
        'operating_hours', 'settings', 'is_active', 'parent_clinic_id',
        'clinic_type', 'branding', 'subscription_plan', 'subscription_expires_at'
    ];

    protected function casts(): array
    {
        return [
            'operating_hours' => 'array',
            'settings' => 'array',
            'branding' => 'array',
            'is_active' => 'boolean',
            'subscription_expires_at' => 'datetime',
        ];
    }

    // Relationships
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function departments()
    {
        return $this->hasMany(Department::class);
    }

    public function patients()
    {
        return $this->hasMany(Patient::class);
    }

    public function doctors()
    {
        return $this->hasMany(Doctor::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function billing()
    {
        return $this->hasMany(Billing::class);
    }

    // Parent-child clinic relationships
    public function parentClinic()
    {
        return $this->belongsTo(Clinic::class, 'parent_clinic_id');
    }

    public function childClinics()
    {
        return $this->hasMany(Clinic::class, 'parent_clinic_id');
    }

    // Clinic administrators
    public function administrators()
    {
        return $this->users()->where('role', 'admin');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeMainClinics($query)
    {
        return $query->whereNull('parent_clinic_id');
    }

    public function scopeBranches($query)
    {
        return $query->whereNotNull('parent_clinic_id');
    }

    // Helper methods
    public function isMainClinic()
    {
        return is_null($this->parent_clinic_id);
    }

    public function isBranch()
    {
        return !is_null($this->parent_clinic_id);
    }

    public function getAllRelatedClinics()
    {
        if ($this->isMainClinic()) {
            return $this->childClinics()->active()->get()->prepend($this);
        } else {
            return $this->parentClinic->childClinics()->active()->get()->prepend($this->parentClinic);
        }
    }

    public function hasSubscriptionExpired()
    {
        return $this->subscription_expires_at && $this->subscription_expires_at->isPast();
    }

    public function getLogoUrlAttribute()
    {
        return $this->branding['logo_url'] ?? '/images/default-clinic-logo.png';
    }

    public function getPrimaryColorAttribute()
    {
        return $this->branding['primary_color'] ?? '#3B82F6';
    }
}
```

### Step 2: Multi-Clinic Middleware

**app/Http/Middleware/MultiClinicMiddleware.php**:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class MultiClinicMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user has clinic access
        if (!$user->clinic_id && !$user->isAdmin()) {
            return redirect()->route('clinic.select')
                ->with('error', 'Please select a clinic to continue.');
        }

        // Check if clinic is active
        if ($user->clinic && !$user->clinic->is_active) {
            auth()->logout();
            return redirect()->route('login')
                ->with('error', 'Your clinic account has been deactivated.');
        }

        // Check subscription status
        if ($user->clinic && $user->clinic->hasSubscriptionExpired()) {
            return redirect()->route('subscription.expired')
                ->with('error', 'Your clinic subscription has expired.');
        }

        // Set current clinic in session
        session(['current_clinic_id' => $user->clinic_id]);

        return $next($request);
    }
}
```

### Step 3: Clinic Management Service

**app/Services/ClinicManagementService.php**:

```php
<?php

namespace App\Services;

use App\Models\Clinic;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ClinicManagementService
{
    /**
     * Create new clinic with admin user
     */
    public function createClinic(array $clinicData, array $adminData): Clinic
    {
        DB::beginTransaction();
        
        try {
            // Create clinic
            $clinic = Clinic::create([
                'name' => $clinicData['name'],
                'code' => $this->generateClinicCode($clinicData['name']),
                'address' => $clinicData['address'],
                'phone' => $clinicData['phone'],
                'email' => $clinicData['email'],
                'license_number' => $clinicData['license_number'],
                'clinic_type' => $clinicData['clinic_type'] ?? 'independent',
                'parent_clinic_id' => $clinicData['parent_clinic_id'] ?? null,
                'operating_hours' => $clinicData['operating_hours'] ?? $this->getDefaultOperatingHours(),
                'settings' => $clinicData['settings'] ?? $this->getDefaultSettings(),
                'subscription_plan' => $clinicData['subscription_plan'] ?? 'basic',
                'subscription_expires_at' => now()->addYear(),
            ]);

            // Create admin user
            $admin = User::create([
                'name' => $adminData['name'],
                'email' => $adminData['email'],
                'password' => Hash::make($adminData['password']),
                'role' => 'admin',
                'clinic_id' => $clinic->id,
                'phone' => $adminData['phone'] ?? null,
            ]);

            // Create default department
            $clinic->departments()->create([
                'name' => 'General Medicine',
                'code' => 'GEN',
                'description' => 'General medical services',
                'is_active' => true,
            ]);

            DB::commit();
            return $clinic;

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Switch user's active clinic
     */
    public function switchClinic(User $user, $clinicId): bool
    {
        $clinic = Clinic::find($clinicId);
        
        if (!$clinic || !$clinic->is_active) {
            return false;
        }

        // Check if user has access to this clinic
        if (!$this->userHasAccessToClinic($user, $clinic)) {
            return false;
        }

        $user->update(['clinic_id' => $clinicId]);
        session(['current_clinic_id' => $clinicId]);
        
        return true;
    }

    /**
     * Check if user has access to clinic
     */
    public function userHasAccessToClinic(User $user, Clinic $clinic): bool
    {
        // Super admin has access to all clinics
        if ($user->role === 'super_admin') {
            return true;
        }

        // User's assigned clinic
        if ($user->clinic_id === $clinic->id) {
            return true;
        }

        // Check if user has multi-clinic access
        if ($user->hasPermission('access_multiple_clinics')) {
            // Check if clinics are related (parent-child relationship)
            $userClinic = $user->clinic;
            if ($userClinic) {
                $relatedClinics = $userClinic->getAllRelatedClinics();
                return $relatedClinics->contains('id', $clinic->id);
            }
        }

        return false;
    }

    /**
     * Get clinics accessible by user
     */
    public function getAccessibleClinics(User $user)
    {
        if ($user->role === 'super_admin') {
            return Clinic::active()->get();
        }

        if ($user->hasPermission('access_multiple_clinics') && $user->clinic) {
            return $user->clinic->getAllRelatedClinics();
        }

        return collect([$user->clinic])->filter();
    }

    /**
     * Transfer patient between clinics
     */
    public function transferPatient($patientId, $fromClinicId, $toClinicId, $reason = null): bool
    {
        DB::beginTransaction();
        
        try {
            $patient = Patient::where('id', $patientId)
                ->where('clinic_id', $fromClinicId)
                ->firstOrFail();

            $toClinic = Clinic::findOrFail($toClinicId);

            // Create transfer record
            $patient->transfers()->create([
                'from_clinic_id' => $fromClinicId,
                'to_clinic_id' => $toClinicId,
                'transferred_by' => auth()->id(),
                'reason' => $reason,
                'transferred_at' => now(),
            ]);

            // Update patient's clinic
            $patient->update(['clinic_id' => $toClinicId]);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            return false;
        }
    }

    /**
     * Generate unique clinic code
     */
    private function generateClinicCode($name): string
    {
        $code = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $name), 0, 3));
        $number = 1;
        
        while (Clinic::where('code', $code . str_pad($number, 3, '0', STR_PAD_LEFT))->exists()) {
            $number++;
        }
        
        return $code . str_pad($number, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Get default operating hours
     */
    private function getDefaultOperatingHours(): array
    {
        return [
            'monday' => ['08:00', '17:00'],
            'tuesday' => ['08:00', '17:00'],
            'wednesday' => ['08:00', '17:00'],
            'thursday' => ['08:00', '17:00'],
            'friday' => ['08:00', '17:00'],
            'saturday' => ['08:00', '12:00'],
            'sunday' => null
        ];
    }

    /**
     * Get default clinic settings
     */
    private function getDefaultSettings(): array
    {
        return [
            'appointment_duration' => 30,
            'max_advance_booking_days' => 30,
            'allow_online_booking' => true,
            'require_appointment_confirmation' => true,
            'send_appointment_reminders' => true,
            'reminder_hours_before' => 24,
            'timezone' => 'Asia/Jakarta',
            'currency' => 'IDR',
            'tax_rate' => 0.1,
        ];
    }
}
```

### Step 4: Clinic Controller

**app/Http/Controllers/Admin/ClinicController.php**:

```php
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Services\ClinicManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ClinicController extends Controller
{
    protected $clinicService;

    public function __construct(ClinicManagementService $clinicService)
    {
        $this->clinicService = $clinicService;
    }

    public function index(Request $request)
    {
        $this->authorize('viewAny', Clinic::class);

        $query = Clinic::with(['parentClinic', 'childClinics']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by type
        if ($request->filled('type')) {
            if ($request->type === 'main') {
                $query->whereNull('parent_clinic_id');
            } elseif ($request->type === 'branch') {
                $query->whereNotNull('parent_clinic_id');
            }
        }

        $clinics = $query->orderBy('name')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/clinics/index', [
            'clinics' => $clinics,
            'filters' => $request->only(['search', 'status', 'type'])
        ]);
    }

    public function create()
    {
        $this->authorize('create', Clinic::class);

        $parentClinics = Clinic::mainClinics()->active()->get();

        return Inertia::render('admin/clinics/create', [
            'parentClinics' => $parentClinics
        ]);
    }

    public function store(Request $request)
    {
        $this->authorize('create', Clinic::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|unique:clinics,email',
            'license_number' => 'required|string|unique:clinics,license_number',
            'clinic_type' => 'required|in:independent,branch,franchise',
            'parent_clinic_id' => 'nullable|exists:clinics,id',
            'subscription_plan' => 'required|in:basic,premium,enterprise',
            
            // Admin user data
            'admin_name' => 'required|string|max:255',
            'admin_email' => 'required|email|unique:users,email',
            'admin_password' => 'required|min:8|confirmed',
            'admin_phone' => 'nullable|string|max:20',
        ]);

        try {
            $clinic = $this->clinicService->createClinic(
                $validated,
                [
                    'name' => $validated['admin_name'],
                    'email' => $validated['admin_email'],
                    'password' => $validated['admin_password'],
                    'phone' => $validated['admin_phone'],
                ]
            );

            return redirect()->route('admin.clinics.show', $clinic)
                ->with('success', 'Clinic created successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create clinic. Please try again.']);
        }
    }

    public function show(Clinic $clinic)
    {
        $this->authorize('view', $clinic);

        $clinic->load([
            'parentClinic',
            'childClinics',
            'administrators',
            'departments',
        ]);

        // Get clinic statistics
        $stats = [
            'total_patients' => $clinic->patients()->count(),
            'total_doctors' => $clinic->doctors()->count(),
            'total_staff' => $clinic->users()->whereIn('role', ['doctor', 'nurse', 'receptionist'])->count(),
            'monthly_appointments' => $clinic->appointments()
                ->whereBetween('appointment_date', [now()->startOfMonth(), now()->endOfMonth()])
                ->count(),
            'monthly_revenue' => $clinic->billing()
                ->whereBetween('invoice_date', [now()->startOfMonth(), now()->endOfMonth()])
                ->sum('total_amount'),
        ];

        return Inertia::render('admin/clinics/show', [
            'clinic' => $clinic,
            'stats' => $stats
        ]);
    }

    public function switchClinic(Request $request)
    {
        $request->validate([
            'clinic_id' => 'required|exists:clinics,id'
        ]);

        $success = $this->clinicService->switchClinic(auth()->user(), $request->clinic_id);

        if ($success) {
            return redirect()->route('dashboard')
                ->with('success', 'Clinic switched successfully.');
        } else {
            return back()->withErrors(['error' => 'Unable to switch to the selected clinic.']);
        }
    }

    public function getAccessibleClinics()
    {
        $clinics = $this->clinicService->getAccessibleClinics(auth()->user());

        return response()->json([
            'clinics' => $clinics->map(function ($clinic) {
                return [
                    'id' => $clinic->id,
                    'name' => $clinic->name,
                    'code' => $clinic->code,
                    'is_current' => $clinic->id === auth()->user()->clinic_id,
                ];
            })
        ]);
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Implemented multi-tenant architecture** for multiple clinic support
2. ✅ **Created clinic management system** with parent-child relationships
3. ✅ **Built clinic switching functionality** for multi-clinic access
4. ✅ **Designed data isolation** and security controls
5. ✅ **Added clinic administration** and centralized management
6. ✅ **Created clinic service layer** for business logic

### What We Have Now

- Multi-tenant architecture supporting multiple clinics
- Clinic management with parent-child relationships
- Secure data isolation between clinics
- Clinic switching and access control
- Centralized administration capabilities

### Next Steps

In **Chapter 13: Testing and Quality Assurance**, we'll:

- Create comprehensive test suite for all features
- Implement unit and feature testing strategies
- Set up API testing and integration tests
- Build quality assurance practices

---

**Ready to continue?** Proceed to [Chapter 13: Testing and Quality Assurance](./chapter-13-testing.md) to build the testing framework.
