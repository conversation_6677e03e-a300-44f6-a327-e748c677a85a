# Comprehensive Documentation
## Hospital Employee Management System

### Table of Contents
1. [API Documentation](#api-documentation)
2. [User Manual](#user-manual)
3. [Deployment Guide](#deployment-guide)
4. [Troubleshooting Guide](#troubleshooting-guide)
5. [System Architecture](#system-architecture)

---

## 1. API Documentation

### Authentication Endpoints

**POST /api/login**
```json
{
  "email": "<EMAIL>",
  "password": "password",
  "remember": true
}

Response:
{
  "message": "Login berhasil",
  "data": {
    "user": {
      "id": 1,
      "name": "Administrator",
      "email": "<EMAIL>",
      "roles": ["Super Admin"]
    },
    "token": "1|abc123...",
    "expires_at": "2024-07-05T10:00:00Z"
  }
}
```

**POST /api/logout**
```json
Headers: Authorization: Bearer {token}

Response:
{
  "message": "Logout berhasil"
}
```

### Employee Management Endpoints

**GET /api/employees**
```json
Query Parameters:
- page: integer (default: 1)
- per_page: integer (default: 15, max: 100)
- search: string
- department_id: integer
- employment_status: string
- sort_by: string (default: 'created_at')
- sort_direction: string (default: 'desc')

Response:
{
  "data": [
    {
      "id": 1,
      "employee_number": "EMP001",
      "full_name": "Dr. Ahmad Wijaya",
      "email": "<EMAIL>",
      "phone_number": "628123456789",
      "department": {
        "id": 1,
        "name": "Kardiologi",
        "code": "CARD"
      },
      "position": {
        "id": 1,
        "title": "Dokter Spesialis Jantung"
      },
      "employment_status": "active",
      "hire_date": "2024-01-15",
      "profile_photo_url": "https://example.com/photos/1.jpg"
    }
  ],
  "meta": {
    "current_page": 1,
    "total": 150,
    "per_page": 15,
    "last_page": 10
  }
}
```

**POST /api/employees**
```json
{
  "first_name": "Dr. Ahmad",
  "last_name": "Wijaya",
  "email": "<EMAIL>",
  "phone_number": "08123456789",
  "date_of_birth": "1985-05-15",
  "gender": "male",
  "address": "Jl. Sudirman No. 123",
  "city": "Jakarta",
  "province": "DKI Jakarta",
  "postal_code": "12190",
  "emergency_contact_name": "Siti Wijaya",
  "emergency_contact_phone": "08987654321",
  "hire_date": "2024-01-15",
  "employment_status": "active",
  "department_id": 1,
  "position_id": 1,
  "employee_type_id": 1,
  "supervisor_id": 2,
  "profile_photo": "base64_encoded_image"
}

Response:
{
  "message": "Karyawan berhasil ditambahkan",
  "data": {
    "id": 1,
    "employee_number": "EMP001",
    "full_name": "Dr. Ahmad Wijaya"
  }
}
```

### Department Management Endpoints

**GET /api/departments**
```json
Response:
{
  "data": [
    {
      "id": 1,
      "name": "Kardiologi",
      "code": "CARD",
      "description": "Departemen Jantung dan Pembuluh Darah",
      "location": "Lantai 3 Gedung A",
      "capacity": 50,
      "current_employees_count": 25,
      "department_head": {
        "id": 1,
        "full_name": "Dr. Ahmad Wijaya"
      },
      "parent_department": null,
      "children": [
        {
          "id": 2,
          "name": "ICU Jantung",
          "code": "CARD-ICU"
        }
      ]
    }
  ]
}
```

### Shift Management Endpoints

**GET /api/shifts**
```json
Query Parameters:
- date: string (YYYY-MM-DD)
- department_id: integer
- employee_id: integer

Response:
{
  "data": [
    {
      "id": 1,
      "name": "Shift Pagi",
      "start_time": "07:00",
      "end_time": "15:00",
      "department": {
        "id": 1,
        "name": "Kardiologi"
      },
      "assigned_employees": [
        {
          "id": 1,
          "full_name": "Dr. Ahmad Wijaya",
          "assignment_date": "2024-07-05"
        }
      ]
    }
  ]
}
```

---

## 2. User Manual

### 2.1 Getting Started

**Login Process**
1. Buka aplikasi Hospital EMS di browser
2. Masukkan email dan password yang telah diberikan
3. Klik tombol "Masuk"
4. Jika menggunakan MFA, masukkan kode verifikasi

**Dashboard Overview**
- **Kartu Statistik**: Menampilkan ringkasan data karyawan, departemen, dan shift
- **Aksi Cepat**: Tombol untuk mengakses fitur yang sering digunakan
- **Aktivitas Terbaru**: Log aktivitas sistem dalam 24 jam terakhir

### 2.2 Employee Management

**Menambah Karyawan Baru**
1. Klik menu "Karyawan" di sidebar
2. Klik tombol "Tambah Karyawan"
3. Isi formulir dengan data lengkap:
   - **Data Pribadi**: Nama, tanggal lahir, jenis kelamin
   - **Kontak**: Email, nomor telepon, alamat
   - **Data Pekerjaan**: Departemen, posisi, tipe karyawan
   - **Kontak Darurat**: Nama dan nomor telepon
4. Upload foto profil (opsional)
5. Klik "Simpan"

**Mencari Karyawan**
1. Gunakan kotak pencarian di halaman daftar karyawan
2. Ketik nama, nomor karyawan, atau departemen
3. Gunakan filter untuk mempersempit hasil:
   - Departemen
   - Status kepegawaian
   - Tipe karyawan

**Mengedit Data Karyawan**
1. Klik nama karyawan di daftar
2. Klik tombol "Edit"
3. Ubah data yang diperlukan
4. Klik "Simpan Perubahan"

### 2.3 Department Management

**Membuat Departemen Baru**
1. Klik menu "Departemen"
2. Klik "Tambah Departemen"
3. Isi informasi departemen:
   - Nama departemen
   - Kode departemen
   - Deskripsi
   - Lokasi
   - Kapasitas
   - Departemen induk (jika ada)
4. Pilih kepala departemen
5. Klik "Simpan"

**Struktur Hierarki**
- Departemen dapat memiliki sub-departemen
- Setiap departemen dapat memiliki kepala departemen
- Sistem mencegah struktur hierarki yang melingkar

### 2.4 Shift Management

**Membuat Jadwal Shift**
1. Klik menu "Jadwal Shift"
2. Klik "Buat Shift Baru"
3. Isi detail shift:
   - Nama shift
   - Waktu mulai dan selesai
   - Departemen
   - Tipe shift (reguler/darurat)
4. Klik "Simpan"

**Menugaskan Karyawan ke Shift**
1. Pilih shift dari daftar
2. Klik "Tugaskan Karyawan"
3. Pilih karyawan dari daftar
4. Pilih tanggal penugasan
5. Sistem akan memvalidasi konflik jadwal
6. Klik "Tugaskan"

---

## 3. Deployment Guide

### 3.1 System Requirements

**Server Requirements**
- **OS**: Ubuntu 20.04 LTS atau CentOS 8
- **Web Server**: Nginx 1.18+ atau Apache 2.4+
- **PHP**: 8.2 atau lebih tinggi
- **Database**: MySQL 8.0 atau MariaDB 10.5
- **Node.js**: 18.x LTS
- **Redis**: 6.0+ (untuk caching dan sessions)

**PHP Extensions Required**
```bash
php8.2-cli php8.2-fpm php8.2-mysql php8.2-redis
php8.2-mbstring php8.2-xml php8.2-curl php8.2-zip
php8.2-gd php8.2-intl php8.2-bcmath
```

### 3.2 Installation Steps

**1. Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install nginx mysql-server redis-server
sudo apt install php8.2-fpm php8.2-cli php8.2-mysql php8.2-redis
sudo apt install php8.2-mbstring php8.2-xml php8.2-curl php8.2-zip
sudo apt install php8.2-gd php8.2-intl php8.2-bcmath

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

**2. Database Setup**
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE hospital_ems_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hospital_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON hospital_ems_production.* TO 'hospital_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

**3. Application Deployment**
```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/your-repo/hospital-ems.git
sudo chown -R www-data:www-data hospital-ems
cd hospital-ems

# Install dependencies
sudo -u www-data composer install --no-dev --optimize-autoloader
sudo -u www-data npm ci
sudo -u www-data npm run build

# Environment configuration
sudo -u www-data cp .env.production .env
sudo -u www-data php artisan key:generate

# Database migration
sudo -u www-data php artisan migrate --force
sudo -u www-data php artisan db:seed --class=RolePermissionSeeder

# Optimize application
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

# Set permissions
sudo chmod -R 755 /var/www/hospital-ems
sudo chmod -R 775 /var/www/hospital-ems/storage
sudo chmod -R 775 /var/www/hospital-ems/bootstrap/cache
```

**4. Nginx Configuration**
```nginx
# /etc/nginx/sites-available/hospital-ems
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/hospital-ems/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/hospital-ems /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3.3 SSL Configuration

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 4. Troubleshooting Guide

### 4.1 Common Issues

**Issue: "Class not found" errors**
```bash
# Solution: Clear and regenerate autoloader
composer dump-autoload
php artisan clear-compiled
php artisan config:clear
```

**Issue: Database connection failed**
```bash
# Check database credentials in .env
# Verify MySQL service is running
sudo systemctl status mysql

# Test connection
php artisan tinker
DB::connection()->getPdo();
```

**Issue: Permission denied errors**
```bash
# Fix file permissions
sudo chown -R www-data:www-data /var/www/hospital-ems
sudo chmod -R 755 /var/www/hospital-ems
sudo chmod -R 775 /var/www/hospital-ems/storage
sudo chmod -R 775 /var/www/hospital-ems/bootstrap/cache
```

**Issue: 500 Internal Server Error**
```bash
# Check Laravel logs
tail -f storage/logs/laravel.log

# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log

# Enable debug mode temporarily
# In .env: APP_DEBUG=true
```

### 4.2 Performance Issues

**Slow Database Queries**
```bash
# Enable query logging
# In .env: DB_LOG_QUERIES=true

# Check slow query log
sudo tail -f /var/log/mysql/mysql-slow.log

# Optimize database
php artisan db:optimize
```

**High Memory Usage**
```bash
# Monitor memory usage
free -h
top -p $(pgrep php-fpm)

# Optimize PHP-FPM
# Edit /etc/php/8.2/fpm/pool.d/www.conf
# pm.max_children = 50
# pm.start_servers = 5
# pm.min_spare_servers = 5
# pm.max_spare_servers = 35
```

### 4.3 Security Issues

**Suspicious Login Attempts**
```bash
# Check authentication logs
grep "authentication" storage/logs/laravel.log

# Review failed login attempts
php artisan tinker
\App\Models\User::where('failed_login_attempts', '>', 5)->get();
```

**File Upload Issues**
```bash
# Check upload permissions
ls -la storage/app/public/

# Verify file size limits
# In php.ini:
# upload_max_filesize = 10M
# post_max_size = 10M
```

---

## 5. System Architecture

### 5.1 Technology Stack

**Backend**
- **Framework**: Laravel 12
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Queue**: Redis
- **Authentication**: Laravel Sanctum
- **Authorization**: Spatie Laravel Permission

**Frontend**
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Library**: shadcn/ui
- **State Management**: React Context
- **HTTP Client**: Axios

**Infrastructure**
- **Web Server**: Nginx
- **Process Manager**: PHP-FPM
- **Task Scheduler**: Laravel Scheduler
- **Queue Worker**: Laravel Queue
- **File Storage**: Local/S3

### 5.2 Database Schema

**Core Tables**
- `users` - User authentication
- `employees` - Employee information
- `departments` - Department hierarchy
- `positions` - Job positions
- `employee_types` - Employee classifications
- `shifts` - Shift definitions
- `employee_shifts` - Shift assignments

**Advanced Tables**
- `performance_reviews` - Performance evaluations
- `performance_goals` - Goal tracking
- `leave_requests` - Leave management
- `payroll_records` - Payroll data

### 5.3 Security Architecture

**Authentication Flow**
1. User submits credentials
2. Laravel validates against database
3. Sanctum generates API token
4. Token stored in HTTP-only cookie
5. Subsequent requests include token

**Authorization Layers**
1. **Route Middleware**: Basic authentication check
2. **Permission Middleware**: Role-based access control
3. **Policy Classes**: Resource-specific permissions
4. **Department Filtering**: Data isolation by department

This comprehensive documentation provides everything needed to understand, deploy, and maintain the Hospital Employee Management System in production environments.
