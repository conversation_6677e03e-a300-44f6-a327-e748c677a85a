# Chapter 09 - MVP Testing dan Quality Assurance

## Tujuan Chapter
Pada chapter ini, kita akan:
- Implementasi comprehensive testing untuk semua MVP features
- Setup automated testing dengan PHPUnit dan Pest
- Membuat integration tests untuk critical workflows
- Implementasi browser testing dengan Laravel Dusk
- Setup continuous integration dan quality checks

## Langkah 1: Setup Testing Environment

### 1.1 Configure Testing Database
Edit `phpunit.xml`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Hospital">
            <directory>tests/Feature/Hospital</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

### 1.2 Create Test Database Seeder
```bash
php artisan make:seeder TestDataSeeder
```

Edit `database/seeders/TestDataSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Hospital\Department;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Staff;

class TestDataSeeder extends Seeder
{
    public function run(): void
    {
        // Create departments
        $departments = [
            ['name' => 'Poli Umum', 'code' => 'UMUM'],
            ['name' => 'Poli Anak', 'code' => 'ANAK'],
            ['name' => 'Unit Gawat Darurat', 'code' => 'UGD'],
        ];

        foreach ($departments as $dept) {
            Department::create(array_merge($dept, [
                'description' => 'Test department',
                'location' => 'Test location',
                'is_active' => true,
            ]));
        }

        // Create test users
        $admin = User::create([
            'name' => 'Test Admin',
            'first_name' => 'Test',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'phone' => '081234567890',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);

        $doctorUser = User::create([
            'name' => 'Dr. Test Doctor',
            'first_name' => 'Test',
            'last_name' => 'Doctor',
            'email' => '<EMAIL>',
            'role' => 'doctor',
            'phone' => '081234567891',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);

        $nurseUser = User::create([
            'name' => 'Test Nurse',
            'first_name' => 'Test',
            'last_name' => 'Nurse',
            'email' => '<EMAIL>',
            'role' => 'nurse',
            'phone' => '081234567892',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);

        // Create doctor profile
        $department = Department::first();
        Doctor::create([
            'user_id' => $doctorUser->id,
            'department_id' => $department->id,
            'license_number' => 'TEST001',
            'specialization' => 'Dokter Umum',
            'qualification' => 'S1 Kedokteran',
            'experience_years' => 5,
            'phone' => '081234567891',
            'consultation_fee' => 150000,
            'available_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            'available_from' => '08:00',
            'available_to' => '16:00',
            'is_available' => true,
            'is_active' => true,
        ]);

        // Create staff profile
        Staff::create([
            'user_id' => $nurseUser->id,
            'department_id' => $department->id,
            'position' => 'Perawat',
            'employee_type' => 'full-time',
            'hire_date' => now()->subYear(),
            'salary' => 5000000,
            'phone' => '081234567892',
            'address' => 'Test Address',
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '081234567893',
            'is_active' => true,
        ]);

        // Create test patients
        Patient::create([
            'nik' => '1234567890123456',
            'first_name' => 'Test',
            'last_name' => 'Patient',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone' => '081234567894',
            'email' => '<EMAIL>',
            'address' => 'Test Patient Address',
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '081234567895',
            'marital_status' => 'single',
            'is_active' => true,
        ]);
    }
}
```

## Langkah 2: Create Model Tests

### 2.1 Patient Model Test
```bash
php artisan make:test Hospital/PatientTest
```

Edit `tests/Feature/Hospital/PatientTest.php`:
```php
<?php

namespace Tests\Feature\Hospital;

use Tests\TestCase;
use App\Models\Hospital\Patient;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PatientTest extends TestCase
{
    use RefreshDatabase;

    public function test_patient_can_be_created(): void
    {
        $patientData = [
            'nik' => '1234567890123456',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone' => '081234567890',
            'address' => 'Test Address',
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '081234567891',
            'marital_status' => 'single',
        ];

        $patient = Patient::create($patientData);

        $this->assertDatabaseHas('patients', $patientData);
        $this->assertNotNull($patient->patient_id);
        $this->assertEquals('John Doe', $patient->full_name);
    }

    public function test_patient_id_is_auto_generated(): void
    {
        $patient = Patient::factory()->create();
        
        $this->assertNotNull($patient->patient_id);
        $this->assertStringStartsWith('P', $patient->patient_id);
    }

    public function test_patient_age_is_calculated_correctly(): void
    {
        $patient = Patient::factory()->create([
            'date_of_birth' => now()->subYears(25)->format('Y-m-d')
        ]);

        $this->assertEquals(25, $patient->age);
    }

    public function test_patient_search_scope_works(): void
    {
        $patient1 = Patient::factory()->create(['first_name' => 'John']);
        $patient2 = Patient::factory()->create(['first_name' => 'Jane']);

        $results = Patient::search('John')->get();

        $this->assertCount(1, $results);
        $this->assertEquals($patient1->id, $results->first()->id);
    }
}
```

### 2.2 Appointment Model Test
```bash
php artisan make:test Hospital/AppointmentTest
```

Edit `tests/Feature/Hospital/AppointmentTest.php`:
```php
<?php

namespace Tests\Feature\Hospital;

use Tests\TestCase;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Department;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class AppointmentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\TestDataSeeder::class);
    }

    public function test_appointment_can_be_created(): void
    {
        $patient = Patient::first();
        $doctor = Doctor::first();
        $department = Department::first();
        $user = User::where('role', 'admin')->first();

        $appointmentData = [
            'patient_id' => $patient->id,
            'doctor_id' => $doctor->id,
            'department_id' => $department->id,
            'appointment_date' => Carbon::tomorrow()->setHour(10),
            'appointment_type' => 'consultation',
            'reason_for_visit' => 'Regular checkup',
            'status' => 'scheduled',
            'fee' => 150000,
            'payment_status' => 'pending',
            'created_by' => $user->id,
        ];

        $appointment = Appointment::create($appointmentData);

        $this->assertDatabaseHas('appointments', [
            'patient_id' => $patient->id,
            'doctor_id' => $doctor->id,
            'status' => 'scheduled',
        ]);
        $this->assertNotNull($appointment->appointment_number);
    }

    public function test_appointment_conflict_detection(): void
    {
        $doctor = Doctor::first();
        $appointmentTime = Carbon::tomorrow()->setHour(10);

        // Create first appointment
        Appointment::factory()->create([
            'doctor_id' => $doctor->id,
            'appointment_date' => $appointmentTime,
            'status' => 'scheduled',
        ]);

        // Check for conflict
        $hasConflict = Appointment::hasConflict($doctor->id, $appointmentTime);

        $this->assertTrue($hasConflict);
    }

    public function test_appointment_can_be_checked_in(): void
    {
        $appointment = Appointment::factory()->create([
            'appointment_date' => Carbon::today()->setHour(10),
            'status' => 'confirmed',
        ]);

        $this->assertTrue($appointment->canBeCheckedIn());

        $appointment->update([
            'status' => 'in_progress',
            'checked_in_at' => now(),
        ]);

        $this->assertFalse($appointment->canBeCheckedIn());
        $this->assertTrue($appointment->canBeCheckedOut());
    }
}
```

## Langkah 3: Create Controller Tests

### 3.1 Patient Controller Test
```bash
php artisan make:test Hospital/PatientControllerTest
```

Edit `tests/Feature/Hospital/PatientControllerTest.php`:
```php
<?php

namespace Tests\Feature\Hospital;

use Tests\TestCase;
use App\Models\User;
use App\Models\Hospital\Patient;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PatientControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\TestDataSeeder::class);
    }

    public function test_admin_can_view_patients_index(): void
    {
        $admin = User::where('role', 'admin')->first();

        $response = $this->actingAs($admin)->get(route('patients.index'));

        $response->assertStatus(200);
    }

    public function test_admin_can_create_patient(): void
    {
        $admin = User::where('role', 'admin')->first();

        $patientData = [
            'nik' => '9876543210123456',
            'first_name' => 'New',
            'last_name' => 'Patient',
            'date_of_birth' => '1985-05-15',
            'gender' => 'female',
            'phone' => '081234567899',
            'address' => 'New Patient Address',
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '081234567898',
            'marital_status' => 'married',
        ];

        $response = $this->actingAs($admin)
                        ->post(route('patients.store'), $patientData);

        $response->assertRedirect();
        $this->assertDatabaseHas('patients', [
            'nik' => '9876543210123456',
            'first_name' => 'New',
            'last_name' => 'Patient',
        ]);
    }

    public function test_unauthorized_user_cannot_access_patients(): void
    {
        $response = $this->get(route('patients.index'));

        $response->assertRedirect(route('login'));
    }

    public function test_patient_validation_works(): void
    {
        $admin = User::where('role', 'admin')->first();

        $response = $this->actingAs($admin)
                        ->post(route('patients.store'), [
                            'first_name' => 'Test',
                            // Missing required fields
                        ]);

        $response->assertSessionHasErrors(['nik', 'last_name', 'date_of_birth']);
    }
}
```

## Langkah 4: Create Integration Tests

### 4.1 Hospital Workflow Test
```bash
php artisan make:test Hospital/HospitalWorkflowTest
```

Edit `tests/Feature/Hospital/HospitalWorkflowTest.php`:
```php
<?php

namespace Tests\Feature\Hospital;

use Tests\TestCase;
use App\Models\User;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\MedicalRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class HospitalWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\TestDataSeeder::class);
    }

    public function test_complete_patient_journey(): void
    {
        $admin = User::where('role', 'admin')->first();
        $doctor = Doctor::first();
        
        // 1. Register new patient
        $patientData = [
            'nik' => '1111222233334444',
            'first_name' => 'Journey',
            'last_name' => 'Patient',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone' => '081111222233',
            'address' => 'Journey Address',
            'emergency_contact_name' => 'Emergency',
            'emergency_contact_phone' => '081111222234',
            'marital_status' => 'single',
        ];

        $patientResponse = $this->actingAs($admin)
                               ->post(route('patients.store'), $patientData);
        
        $patientResponse->assertRedirect();
        $patient = Patient::where('nik', '1111222233334444')->first();
        $this->assertNotNull($patient);

        // 2. Create appointment
        $appointmentData = [
            'patient_id' => $patient->id,
            'doctor_id' => $doctor->id,
            'department_id' => $doctor->department_id,
            'appointment_date' => Carbon::tomorrow()->setHour(10)->format('Y-m-d H:i:s'),
            'appointment_type' => 'consultation',
            'reason_for_visit' => 'Regular checkup',
        ];

        $appointmentResponse = $this->actingAs($admin)
                                   ->post(route('appointments.store'), $appointmentData);
        
        $appointmentResponse->assertRedirect();
        $appointment = Appointment::where('patient_id', $patient->id)->first();
        $this->assertNotNull($appointment);

        // 3. Check in patient
        $checkInResponse = $this->actingAs($admin)
                               ->post(route('appointments.check-in', $appointment));
        
        $checkInResponse->assertRedirect();
        $appointment->refresh();
        $this->assertEquals('in_progress', $appointment->status);
        $this->assertNotNull($appointment->checked_in_at);

        // 4. Create medical record
        $doctorUser = $doctor->user;
        $medicalRecordData = [
            'patient_id' => $patient->id,
            'doctor_id' => $doctor->id,
            'appointment_id' => $appointment->id,
            'visit_date' => now()->format('Y-m-d H:i:s'),
            'chief_complaint' => 'Feeling unwell',
            'history_of_present_illness' => 'Patient reports feeling tired',
            'physical_examination' => 'Normal examination',
            'diagnosis' => 'Common cold',
            'treatment_plan' => 'Rest and medication',
            'vital_signs_temperature' => 37.2,
            'vital_signs_blood_pressure_systolic' => 120,
            'vital_signs_blood_pressure_diastolic' => 80,
            'vital_signs_heart_rate' => 72,
        ];

        $medicalRecordResponse = $this->actingAs($doctorUser)
                                     ->post(route('medical-records.store'), $medicalRecordData);
        
        $medicalRecordResponse->assertRedirect();
        $medicalRecord = MedicalRecord::where('patient_id', $patient->id)->first();
        $this->assertNotNull($medicalRecord);

        // 5. Check out patient
        $checkOutResponse = $this->actingAs($admin)
                                ->post(route('appointments.check-out', $appointment));
        
        $checkOutResponse->assertRedirect();
        $appointment->refresh();
        $this->assertEquals('completed', $appointment->status);
        $this->assertNotNull($appointment->checked_out_at);

        // Verify complete workflow
        $this->assertDatabaseHas('patients', ['nik' => '1111222233334444']);
        $this->assertDatabaseHas('appointments', [
            'patient_id' => $patient->id,
            'status' => 'completed'
        ]);
        $this->assertDatabaseHas('medical_records', [
            'patient_id' => $patient->id,
            'diagnosis' => 'Common cold'
        ]);
    }
}
```

## Langkah 5: Create Factory Classes

### 5.1 Patient Factory
```bash
php artisan make:factory Hospital/PatientFactory --model=Hospital/Patient
```

Edit `database/factories/Hospital/PatientFactory.php`:
```php
<?php

namespace Database\Factories\Hospital;

use Illuminate\Database\Eloquent\Factories\Factory;

class PatientFactory extends Factory
{
    protected $model = \App\Models\Hospital\Patient::class;

    public function definition(): array
    {
        return [
            'nik' => $this->faker->unique()->numerify('################'),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'date_of_birth' => $this->faker->dateTimeBetween('-80 years', '-18 years')->format('Y-m-d'),
            'gender' => $this->faker->randomElement(['male', 'female']),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->optional()->safeEmail(),
            'address' => $this->faker->address(),
            'emergency_contact_name' => $this->faker->name(),
            'emergency_contact_phone' => $this->faker->phoneNumber(),
            'blood_type' => $this->faker->optional()->randomElement(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']),
            'marital_status' => $this->faker->randomElement(['single', 'married', 'divorced', 'widowed']),
            'occupation' => $this->faker->optional()->jobTitle(),
            'is_active' => true,
        ];
    }
}
```

## Langkah 6: Run Tests

### 6.1 Execute Test Suite
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Hospital

# Run with coverage (requires Xdebug)
php artisan test --coverage

# Run specific test
php artisan test tests/Feature/Hospital/PatientTest.php
```

### 6.2 Performance Testing
```bash
# Create performance test
php artisan make:test Hospital/PerformanceTest
```

## Langkah 7: Create Test Documentation

### 7.1 Create Testing Guide
Create `Hospital-Management-System/resources/testing-guide.md`:
```markdown
# Hospital Management System - Testing Guide

## Test Coverage

### Unit Tests
- Model validation and business logic
- Helper methods and accessors
- Scope queries

### Feature Tests
- Controller endpoints
- Authentication and authorization
- Form validation
- Database interactions

### Integration Tests
- Complete user workflows
- Cross-module interactions
- API endpoints

## Running Tests

```bash
# Run all tests
php artisan test

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/Hospital/PatientTest.php
```

## Test Data

Test data is automatically seeded using `TestDataSeeder` which creates:
- Admin, Doctor, and Nurse users
- Sample departments
- Test patients
- Sample appointments

## Continuous Integration

Tests should be run on every commit and pull request to ensure code quality.
```

## Next Steps

Setelah MVP testing selesai, kita akan:
- Deploy ke production environment
- Membuat user documentation
- Memulai Advanced Phase development
- Setup monitoring dan logging

## Checklist Completion

- [ ] Testing environment configured
- [ ] Model tests implemented
- [ ] Controller tests created
- [ ] Integration tests for workflows
- [ ] Factory classes for test data
- [ ] Performance testing setup
- [ ] Test documentation created
- [ ] All tests passing
- [ ] Code coverage acceptable (>80%)

**Estimasi Waktu**: 180-240 menit

**Difficulty Level**: Advanced

**File yang Dibuat**:
- Multiple test files
- TestDataSeeder
- Factory classes
- Testing configuration
- Testing documentation

**MVP Phase Testing Complete**: ✅
Semua fitur MVP telah ditest dan siap untuk production deployment.
