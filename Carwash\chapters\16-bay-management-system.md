# Chapter 16: Bay Management System

Welcome to Chapter 16! In this chapter, we'll build a comprehensive service bay management system that provides real-time bay status tracking, assignment optimization, service progress tracking, utilization analytics, and equipment allocation.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Develop real-time bay status tracking (available, occupied, cleaning, maintenance)
- Build bay assignment optimization based on service type and duration
- Implement service progress tracking within each bay
- Create bay utilization analytics and reporting
- Add equipment and resource allocation per bay
- Integrate bay management with booking and queue systems
- Build bay performance monitoring and optimization
- Create maintenance scheduling and tracking

## 📋 What We'll Cover

1. Setting up bay management database structure
2. Creating bay management models
3. Building the bay management interface
4. Implementing bay assignment optimization
5. Real-time bay status tracking
6. Service progress monitoring
7. Bay utilization analytics
8. Equipment and maintenance management

## 🛠 Step 1: Database Structure for Bay Management

First, let's create the necessary migrations for our bay management system:

```bash
# Create bay-related migrations
php artisan make:migration create_service_bays_table
php artisan make:migration create_bay_assignments_table
php artisan make:migration create_bay_equipment_table
php artisan make:migration create_bay_maintenance_table
php artisan make:migration add_bay_fields_to_bookings_table
```

Edit `database/migrations/create_service_bays_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('service_bays', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('bay_number')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['standard', 'premium', 'express', 'detail', 'maintenance']);
            $table->enum('status', ['available', 'occupied', 'cleaning', 'maintenance', 'out_of_service'])->default('available');
            $table->json('supported_services'); // Array of service IDs this bay can handle
            $table->integer('capacity')->default(1); // Number of vehicles
            $table->decimal('hourly_rate', 8, 2)->nullable(); // Cost per hour for bay usage
            $table->boolean('is_active')->default(true);
            $table->json('features')->nullable(); // Lift, pressure washer, etc.
            $table->string('location')->nullable();
            $table->integer('priority')->default(1); // Assignment priority (1 = highest)
            $table->timestamp('last_cleaned_at')->nullable();
            $table->timestamp('last_maintenance_at')->nullable();
            $table->json('operating_hours')->nullable(); // Daily operating hours
            $table->timestamps();

            $table->index(['status', 'is_active']);
            $table->index(['type', 'status']);
            $table->index(['bay_number']);
            $table->index(['priority', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('service_bays');
    }
};
```

Edit `database/migrations/create_bay_assignments_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bay_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_bay_id')->constrained()->onDelete('cascade');
            $table->foreignId('booking_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('queue_number_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->string('vehicle_info')->nullable();
            $table->enum('status', ['assigned', 'in_progress', 'completed', 'cancelled'])->default('assigned');
            $table->timestamp('assigned_at');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('estimated_duration'); // in minutes
            $table->integer('actual_duration')->nullable(); // in minutes
            $table->json('services_performed')->nullable(); // Array of service details
            $table->json('progress_stages')->nullable(); // Track service progress
            $table->decimal('total_cost', 10, 2)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['service_bay_id', 'status']);
            $table->index(['booking_id']);
            $table->index(['queue_number_id']);
            $table->index(['assigned_at', 'completed_at']);
            $table->index(['status', 'assigned_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bay_assignments');
    }
};
```

Edit `database/migrations/create_bay_equipment_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bay_equipment', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_bay_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('equipment_type'); // pressure_washer, vacuum, lift, etc.
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->enum('status', ['operational', 'maintenance', 'broken', 'retired'])->default('operational');
            $table->date('purchase_date')->nullable();
            $table->date('warranty_expiry')->nullable();
            $table->date('last_service_date')->nullable();
            $table->date('next_service_date')->nullable();
            $table->decimal('maintenance_cost', 10, 2)->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['service_bay_id', 'status']);
            $table->index(['equipment_type', 'status']);
            $table->index(['next_service_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bay_equipment');
    }
};
```

Edit `database/migrations/create_bay_maintenance_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bay_maintenance', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_bay_id')->constrained()->onDelete('cascade');
            $table->foreignId('bay_equipment_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('performed_by')->constrained('users')->onDelete('cascade');
            $table->enum('type', ['routine', 'repair', 'inspection', 'cleaning', 'upgrade']);
            $table->string('title');
            $table->text('description');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->timestamp('scheduled_at');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('estimated_duration'); // in minutes
            $table->integer('actual_duration')->nullable(); // in minutes
            $table->decimal('cost', 10, 2)->default(0);
            $table->json('parts_used')->nullable();
            $table->text('completion_notes')->nullable();
            $table->timestamps();

            $table->index(['service_bay_id', 'status']);
            $table->index(['type', 'priority']);
            $table->index(['scheduled_at', 'status']);
            $table->index(['performed_by']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bay_maintenance');
    }
};
```

Edit `database/migrations/add_bay_fields_to_bookings_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('service_bay_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('bay_assignment_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamp('bay_assigned_at')->nullable();
            $table->integer('bay_duration')->nullable(); // Actual time spent in bay
        });
    }

    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['service_bay_id']);
            $table->dropForeign(['bay_assignment_id']);
            $table->dropColumn(['service_bay_id', 'bay_assignment_id', 'bay_assigned_at', 'bay_duration']);
        });
    }
};
```

Run the migrations:

```bash
php artisan migrate
```

## 🛠 Step 2: Creating Bay Management Models

Create the models for our bay management system:

```bash
# Create bay models
php artisan make:model ServiceBay
php artisan make:model BayAssignment
php artisan make:model BayEquipment
php artisan make:model BayMaintenance
```

Edit `app/Models/ServiceBay.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ServiceBay extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'bay_number',
        'description',
        'type',
        'status',
        'supported_services',
        'capacity',
        'hourly_rate',
        'is_active',
        'features',
        'location',
        'priority',
        'last_cleaned_at',
        'last_maintenance_at',
        'operating_hours',
    ];

    protected $casts = [
        'supported_services' => 'array',
        'features' => 'array',
        'operating_hours' => 'array',
        'hourly_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'last_cleaned_at' => 'datetime',
        'last_maintenance_at' => 'datetime',
    ];

    // Status constants
    const STATUS_AVAILABLE = 'available';
    const STATUS_OCCUPIED = 'occupied';
    const STATUS_CLEANING = 'cleaning';
    const STATUS_MAINTENANCE = 'maintenance';
    const STATUS_OUT_OF_SERVICE = 'out_of_service';

    // Type constants
    const TYPE_STANDARD = 'standard';
    const TYPE_PREMIUM = 'premium';
    const TYPE_EXPRESS = 'express';
    const TYPE_DETAIL = 'detail';
    const TYPE_MAINTENANCE = 'maintenance';

    public static function getStatuses(): array
    {
        return [
            self::STATUS_AVAILABLE => 'Available',
            self::STATUS_OCCUPIED => 'Occupied',
            self::STATUS_CLEANING => 'Cleaning',
            self::STATUS_MAINTENANCE => 'Maintenance',
            self::STATUS_OUT_OF_SERVICE => 'Out of Service',
        ];
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_STANDARD => 'Standard Bay',
            self::TYPE_PREMIUM => 'Premium Bay',
            self::TYPE_EXPRESS => 'Express Bay',
            self::TYPE_DETAIL => 'Detail Bay',
            self::TYPE_MAINTENANCE => 'Maintenance Bay',
        ];
    }

    // Relationships
    public function assignments(): HasMany
    {
        return $this->hasMany(BayAssignment::class);
    }

    public function currentAssignment(): HasOne
    {
        return $this->hasOne(BayAssignment::class)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->latest();
    }

    public function equipment(): HasMany
    {
        return $this->hasMany(BayEquipment::class);
    }

    public function maintenance(): HasMany
    {
        return $this->hasMany(BayMaintenance::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    // Scopes
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_AVAILABLE)
            ->where('is_active', true);
    }

    public function scopeOccupied(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_OCCUPIED);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    public function scopeCanHandleService(Builder $query, int $serviceId): Builder
    {
        return $query->whereJsonContains('supported_services', $serviceId);
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return self::getStatuses()[$this->status] ?? 'Unknown';
    }

    public function getTypeLabelAttribute(): string
    {
        return self::getTypes()[$this->type] ?? 'Unknown';
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_AVAILABLE => 'success',
            self::STATUS_OCCUPIED => 'primary',
            self::STATUS_CLEANING => 'warning',
            self::STATUS_MAINTENANCE => 'info',
            self::STATUS_OUT_OF_SERVICE => 'danger',
            default => 'secondary',
        };
    }

    public function getIsOperationalAttribute(): bool
    {
        return in_array($this->status, [self::STATUS_AVAILABLE, self::STATUS_OCCUPIED]);
    }

    public function getUtilizationTodayAttribute(): float
    {
        $totalMinutes = 0;
        $occupiedMinutes = 0;

        $assignments = $this->assignments()
            ->whereDate('assigned_at', today())
            ->where('status', 'completed')
            ->get();

        foreach ($assignments as $assignment) {
            if ($assignment->actual_duration) {
                $occupiedMinutes += $assignment->actual_duration;
            }
        }

        // Assume 8-hour working day
        $totalMinutes = 8 * 60;

        return $totalMinutes > 0 ? ($occupiedMinutes / $totalMinutes) * 100 : 0;
    }

    // Methods
    public function canHandleService(int $serviceId): bool
    {
        return in_array($serviceId, $this->supported_services ?? []);
    }

    public function isAvailableForAssignment(): bool
    {
        return $this->status === self::STATUS_AVAILABLE && 
               $this->is_active && 
               !$this->currentAssignment;
    }

    public function assignToBooking(Booking $booking, User $assignedBy): BayAssignment
    {
        if (!$this->isAvailableForAssignment()) {
            throw new \Exception('Bay is not available for assignment');
        }

        $assignment = BayAssignment::create([
            'service_bay_id' => $this->id,
            'booking_id' => $booking->id,
            'queue_number_id' => $booking->queue_number_id,
            'customer_id' => $booking->customer_id,
            'assigned_by' => $assignedBy->id,
            'vehicle_info' => $booking->vehicle_info ?? '',
            'assigned_at' => now(),
            'estimated_duration' => $booking->service->duration ?? 30,
            'services_performed' => [
                [
                    'service_id' => $booking->service_id,
                    'service_name' => $booking->service->name,
                    'price' => $booking->service->price,
                ]
            ],
        ]);

        $this->update(['status' => self::STATUS_OCCUPIED]);

        $booking->update([
            'service_bay_id' => $this->id,
            'bay_assignment_id' => $assignment->id,
            'bay_assigned_at' => now(),
        ]);

        return $assignment;
    }

    public function startService(): void
    {
        if ($this->currentAssignment) {
            $this->currentAssignment->update([
                'status' => 'in_progress',
                'started_at' => now(),
            ]);
        }
    }

    public function completeService(): void
    {
        if ($this->currentAssignment) {
            $assignment = $this->currentAssignment;
            $actualDuration = $assignment->started_at ? 
                now()->diffInMinutes($assignment->started_at) : 
                $assignment->estimated_duration;

            $assignment->update([
                'status' => 'completed',
                'completed_at' => now(),
                'actual_duration' => $actualDuration,
            ]);

            // Update booking
            if ($assignment->booking) {
                $assignment->booking->update([
                    'status' => 'completed',
                    'bay_duration' => $actualDuration,
                ]);
            }

            $this->update(['status' => self::STATUS_CLEANING]);
        }
    }

    public function markCleaned(): void
    {
        $this->update([
            'status' => self::STATUS_AVAILABLE,
            'last_cleaned_at' => now(),
        ]);
    }

    public static function findOptimalBay(int $serviceId, int $estimatedDuration = 30): ?self
    {
        return self::available()
            ->canHandleService($serviceId)
            ->orderBy('priority')
            ->orderBy('last_cleaned_at')
            ->first();
    }
}
```

## 🛠 Step 5: Creating Bay Management Views

Now let's create the comprehensive bay management interfaces for staff and administrators.

### 5.1 Bay Management Dashboard

Create `resources/views/bays/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Bay Management
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('bays.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add New Bay
                </a>
                <button onclick="refreshBayStatus()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Refresh Status
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Bay Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Available</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $availableBays }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Occupied</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $occupiedBays }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Cleaning</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $cleaningBays }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Maintenance</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $maintenanceBays }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bay Grid Display -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Bay Status Overview</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach($bays as $bay)
                            <div class="border-2 rounded-lg p-4 transition-all duration-200 hover:shadow-lg
                                {{ $bay->status === 'available' ? 'border-green-200 bg-green-50' :
                                   ($bay->status === 'occupied' ? 'border-red-200 bg-red-50' :
                                   ($bay->status === 'cleaning' ? 'border-yellow-200 bg-yellow-50' : 'border-purple-200 bg-purple-50')) }}">

                                <!-- Bay Header -->
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h4 class="text-lg font-semibold text-gray-900">{{ $bay->name }}</h4>
                                        <p class="text-sm text-gray-600">{{ $bay->type }}</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $bay->status === 'available' ? 'bg-green-100 text-green-800' :
                                           ($bay->status === 'occupied' ? 'bg-red-100 text-red-800' :
                                           ($bay->status === 'cleaning' ? 'bg-yellow-100 text-yellow-800' : 'bg-purple-100 text-purple-800')) }}">
                                        {{ ucfirst($bay->status) }}
                                    </span>
                                </div>

                                <!-- Current Assignment -->
                                @if($bay->current_assignment)
                                    <div class="mb-3 p-3 bg-white rounded border">
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-sm font-medium text-gray-700">Current Service</span>
                                            <span class="text-sm text-gray-500">{{ $bay->current_assignment->queue_number ?? 'N/A' }}</span>
                                        </div>
                                        <div class="text-sm text-gray-600 mb-2">
                                            {{ $bay->current_assignment->customer_name ?? 'Walk-in Customer' }}
                                        </div>
                                        <div class="text-xs text-gray-500 mb-2">
                                            {{ $bay->current_assignment->service_type }}
                                        </div>

                                        <!-- Progress Bar -->
                                        @if($bay->current_assignment->progress_percentage)
                                            <div class="mb-2">
                                                <div class="flex justify-between text-xs text-gray-600 mb-1">
                                                    <span>Progress</span>
                                                    <span>{{ $bay->current_assignment->progress_percentage }}%</span>
                                                </div>
                                                <div class="bg-gray-200 rounded-full h-2">
                                                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                         style="width: {{ $bay->current_assignment->progress_percentage }}%"></div>
                                                </div>
                                            </div>
                                        @endif

                                        <!-- Time Information -->
                                        <div class="text-xs text-gray-500">
                                            Started: {{ $bay->current_assignment->started_at ? $bay->current_assignment->started_at->format('H:i') : 'N/A' }}
                                            @if($bay->current_assignment->estimated_completion)
                                                | ETA: {{ $bay->current_assignment->estimated_completion->format('H:i') }}
                                            @endif
                                        </div>
                                    </div>
                                @endif

                                <!-- Bay Information -->
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Capacity:</span>
                                        <span class="font-medium">{{ $bay->vehicle_capacity }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Equipment:</span>
                                        <span class="font-medium">{{ $bay->equipment_count ?? 0 }}</span>
                                    </div>
                                    @if($bay->last_maintenance)
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Last Maintenance:</span>
                                            <span class="font-medium">{{ $bay->last_maintenance->format('M d') }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Action Buttons -->
                                <div class="mt-4 flex space-x-2">
                                    @if($bay->status === 'available')
                                        <button onclick="assignBay({{ $bay->id }})"
                                                class="flex-1 bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-3 rounded">
                                            Assign
                                        </button>
                                    @elseif($bay->status === 'occupied')
                                        <button onclick="updateProgress({{ $bay->id }})"
                                                class="flex-1 bg-green-500 hover:bg-green-700 text-white text-xs py-2 px-3 rounded">
                                            Update
                                        </button>
                                        <button onclick="completeService({{ $bay->id }})"
                                                class="flex-1 bg-purple-500 hover:bg-purple-700 text-white text-xs py-2 px-3 rounded">
                                            Complete
                                        </button>
                                    @elseif($bay->status === 'cleaning')
                                        <button onclick="finishCleaning({{ $bay->id }})"
                                                class="flex-1 bg-green-500 hover:bg-green-700 text-white text-xs py-2 px-3 rounded">
                                            Finish Cleaning
                                        </button>
                                    @endif

                                    <button onclick="viewBayDetails({{ $bay->id }})"
                                            class="bg-gray-500 hover:bg-gray-700 text-white text-xs py-2 px-3 rounded">
                                        Details
                                    </button>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Bay Management Functions
        function assignBay(bayId) {
            // Open assignment modal or redirect to assignment page
            window.location.href = `/bays/${bayId}/assign`;
        }

        function updateProgress(bayId) {
            // Open progress update modal
            const progress = prompt('Enter progress percentage (0-100):');
            if (progress !== null && progress >= 0 && progress <= 100) {
                fetch(`/bays/${bayId}/update-progress`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ progress: progress })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error updating progress: ' + data.message);
                    }
                });
            }
        }

        function completeService(bayId) {
            if (confirm('Mark this service as completed?')) {
                fetch(`/bays/${bayId}/complete-service`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error completing service: ' + data.message);
                    }
                });
            }
        }

        function finishCleaning(bayId) {
            if (confirm('Mark cleaning as finished?')) {
                fetch(`/bays/${bayId}/finish-cleaning`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error finishing cleaning: ' + data.message);
                    }
                });
            }
        }

        function viewBayDetails(bayId) {
            window.location.href = `/bays/${bayId}`;
        }

        function refreshBayStatus() {
            location.reload();
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</x-app-layout>
```

### 5.2 Bay Analytics Dashboard

Create `resources/views/bays/analytics.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Bay Analytics & Performance
            </h2>
            <div class="flex space-x-2">
                <select id="date-range" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month" selected>This Month</option>
                    <option value="quarter">This Quarter</option>
                </select>
                <button onclick="exportReport()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Export Report
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Key Performance Indicators -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Average Utilization</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($averageUtilization, 1) }}%</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    <span class="{{ $utilizationTrend >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $utilizationTrend >= 0 ? '↗' : '↘' }} {{ abs($utilizationTrend) }}%
                                    </span>
                                    vs last period
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Avg Service Time</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $averageServiceTime }}m</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    <span class="{{ $serviceTimeTrend <= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $serviceTimeTrend <= 0 ? '↗' : '↘' }} {{ abs($serviceTimeTrend) }}m
                                    </span>
                                    vs last period
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($totalRevenue, 0) }}</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    <span class="{{ $revenueTrend >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $revenueTrend >= 0 ? '↗' : '↘' }} {{ abs($revenueTrend) }}%
                                    </span>
                                    vs last period
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Services Completed</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($servicesCompleted) }}</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    <span class="{{ $servicesTrend >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $servicesTrend >= 0 ? '↗' : '↘' }} {{ abs($servicesTrend) }}
                                    </span>
                                    vs last period
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Bay Utilization Over Time -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Bay Utilization Trend</h3>
                        <canvas id="utilizationTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Service Time Distribution -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Service Time Distribution</h3>
                        <canvas id="serviceTimeChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Bay Performance Comparison -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Individual Bay Performance</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bay</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilization</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Service Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Services</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($bayPerformance as $bay)
                                    <tr class="{{ $loop->even ? 'bg-gray-50' : 'bg-white' }}">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="text-sm font-medium text-gray-900">{{ $bay->name }}</div>
                                                <div class="text-sm text-gray-500 ml-2">({{ $bay->type }})</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="text-sm text-gray-900">{{ number_format($bay->utilization_rate, 1) }}%</div>
                                                <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $bay->utilization_rate }}%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $bay->avg_service_time }}m
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ number_format($bay->services_count) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${{ number_format($bay->revenue, 0) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {{ $bay->efficiency_score >= 90 ? 'bg-green-100 text-green-800' :
                                                   ($bay->efficiency_score >= 70 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                                {{ number_format($bay->efficiency_score, 1) }}%
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {{ $bay->status === 'available' ? 'bg-green-100 text-green-800' :
                                                   ($bay->status === 'occupied' ? 'bg-blue-100 text-blue-800' :
                                                   ($bay->status === 'cleaning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')) }}">
                                                {{ ucfirst($bay->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Peak Hours Analysis -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Peak Hours Chart -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Peak Hours Analysis</h3>
                        <canvas id="peakHoursChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Service Type Distribution -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Service Type Distribution</h3>
                        <canvas id="serviceTypeChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Maintenance Schedule -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Upcoming Maintenance Schedule</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($upcomingMaintenance as $maintenance)
                            <div class="border border-gray-200 rounded-lg p-4
                                {{ $maintenance->is_overdue ? 'bg-red-50 border-red-200' :
                                   ($maintenance->is_due_soon ? 'bg-yellow-50 border-yellow-200' : 'bg-gray-50') }}">
                                <div class="flex justify-between items-start mb-2">
                                    <h4 class="font-medium text-gray-900">{{ $maintenance->bay->name }}</h4>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {{ $maintenance->is_overdue ? 'bg-red-100 text-red-800' :
                                           ($maintenance->is_due_soon ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') }}">
                                        {{ $maintenance->priority }}
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">{{ $maintenance->maintenance_type }}</p>
                                <p class="text-xs text-gray-500">
                                    Due: {{ $maintenance->scheduled_date->format('M d, Y') }}
                                </p>
                                @if($maintenance->estimated_duration)
                                    <p class="text-xs text-gray-500">
                                        Duration: {{ $maintenance->estimated_duration }} hours
                                    </p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Utilization Trend Chart
        const utilizationCtx = document.getElementById('utilizationTrendChart').getContext('2d');
        new Chart(utilizationCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode($utilizationTrendData['labels']) !!},
                datasets: [{
                    label: 'Utilization %',
                    data: {!! json_encode($utilizationTrendData['data']) !!},
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });

        // Service Time Distribution Chart
        const serviceTimeCtx = document.getElementById('serviceTimeChart').getContext('2d');
        new Chart(serviceTimeCtx, {
            type: 'bar',
            data: {
                labels: {!! json_encode($serviceTimeData['labels']) !!},
                datasets: [{
                    label: 'Services',
                    data: {!! json_encode($serviceTimeData['data']) !!},
                    backgroundColor: 'rgba(34, 197, 94, 0.8)',
                    borderColor: 'rgba(34, 197, 94, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Peak Hours Chart
        const peakHoursCtx = document.getElementById('peakHoursChart').getContext('2d');
        new Chart(peakHoursCtx, {
            type: 'line',
            data: {
                labels: {!! json_encode($peakHoursData['labels']) !!},
                datasets: [{
                    label: 'Services',
                    data: {!! json_encode($peakHoursData['data']) !!},
                    borderColor: 'rgb(147, 51, 234)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Service Type Distribution Chart
        const serviceTypeCtx = document.getElementById('serviceTypeChart').getContext('2d');
        new Chart(serviceTypeCtx, {
            type: 'doughnut',
            data: {
                labels: {!! json_encode($serviceTypeData['labels']) !!},
                datasets: [{
                    data: {!! json_encode($serviceTypeData['data']) !!},
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(147, 51, 234, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Date range change handler
        document.getElementById('date-range').addEventListener('change', function() {
            const range = this.value;
            window.location.href = `{{ route('bays.analytics') }}?range=${range}`;
        });

        function exportReport() {
            const range = document.getElementById('date-range').value;
            window.open(`{{ route('bays.analytics') }}?range=${range}&export=pdf`, '_blank');
        }
    </script>
</x-app-layout>
```

### 5.3 Bay Assignment Interface

Create `resources/views/bays/assign.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Assign Bay - {{ $bay->name }}
            </h2>
            <a href="{{ route('bays.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Bay Management
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Bay Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Bay Information</h3>

                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Bay Name:</span>
                                <span class="font-medium">{{ $bay->name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Type:</span>
                                <span class="font-medium">{{ $bay->type }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Vehicle Capacity:</span>
                                <span class="font-medium">{{ $bay->vehicle_capacity }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Current Status:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $bay->status === 'available' ? 'bg-green-100 text-green-800' :
                                       ($bay->status === 'occupied' ? 'bg-red-100 text-red-800' :
                                       ($bay->status === 'cleaning' ? 'bg-yellow-100 text-yellow-800' : 'bg-purple-100 text-purple-800')) }}">
                                    {{ ucfirst($bay->status) }}
                                </span>
                            </div>

                            @if($bay->equipment->count() > 0)
                                <div>
                                    <span class="text-gray-600">Available Equipment:</span>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        @foreach($bay->equipment as $equipment)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $equipment->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Assignment Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Assignment Options</h3>

                        <!-- Assignment Type Tabs -->
                        <div class="mb-6">
                            <div class="border-b border-gray-200">
                                <nav class="-mb-px flex space-x-8">
                                    <button onclick="showTab('queue')" id="queue-tab"
                                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        From Queue
                                    </button>
                                    <button onclick="showTab('walkin')" id="walkin-tab"
                                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        Walk-in Customer
                                    </button>
                                    <button onclick="showTab('booking')" id="booking-tab"
                                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        From Booking
                                    </button>
                                </nav>
                            </div>
                        </div>

                        <!-- Queue Assignment -->
                        <div id="queue-content" class="tab-content">
                            <h4 class="font-medium text-gray-900 mb-3">Waiting Queue</h4>
                            @if($waitingQueue->count() > 0)
                                <div class="space-y-3">
                                    @foreach($waitingQueue as $queue)
                                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer queue-item"
                                             data-queue-id="{{ $queue->id }}" onclick="selectQueue({{ $queue->id }})">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <div class="flex items-center space-x-2">
                                                        <span class="font-medium text-gray-900">{{ $queue->queue_number }}</span>
                                                        <span class="text-sm text-gray-500">{{ $queue->customer_name ?? 'Walk-in' }}</span>
                                                    </div>
                                                    <div class="text-sm text-gray-600 mt-1">
                                                        {{ $queue->service_type }} - {{ $queue->vehicle_type }}
                                                    </div>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        Wait time: {{ $queue->wait_time_minutes }}m | Est. duration: {{ $queue->estimated_duration }}m
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <div class="text-sm font-medium text-gray-900">${{ $queue->total_amount }}</div>
                                                    <div class="text-xs text-gray-500">{{ $queue->created_at->format('H:i') }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-gray-500 text-center py-4">No customers in queue</p>
                            @endif
                        </div>

                        <!-- Walk-in Assignment -->
                        <div id="walkin-content" class="tab-content hidden">
                            <form id="walkin-form">
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Customer Name</label>
                                        <input type="text" name="customer_name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Phone Number</label>
                                        <input type="text" name="phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Vehicle Type</label>
                                        <select name="vehicle_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">Select Vehicle Type</option>
                                            <option value="sedan">Sedan</option>
                                            <option value="suv">SUV</option>
                                            <option value="truck">Truck</option>
                                            <option value="motorcycle">Motorcycle</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">License Plate</label>
                                        <input type="text" name="license_plate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Service Package</label>
                                        <select name="service_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" onchange="updateServiceDetails()">
                                            <option value="">Select Service</option>
                                            @foreach($services as $service)
                                                <option value="{{ $service->id }}" data-price="{{ $service->price }}" data-duration="{{ $service->duration }}">
                                                    {{ $service->name }} - ${{ $service->price }} ({{ $service->duration }}m)
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                                            <input type="number" name="total_amount" step="0.01" readonly class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Est. Duration (min)</label>
                                            <input type="number" name="estimated_duration" readonly class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Booking Assignment -->
                        <div id="booking-content" class="tab-content hidden">
                            <h4 class="font-medium text-gray-900 mb-3">Confirmed Bookings</h4>
                            @if($confirmedBookings->count() > 0)
                                <div class="space-y-3">
                                    @foreach($confirmedBookings as $booking)
                                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer booking-item"
                                             data-booking-id="{{ $booking->id }}" onclick="selectBooking({{ $booking->id }})">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <div class="flex items-center space-x-2">
                                                        <span class="font-medium text-gray-900">{{ $booking->customer->name }}</span>
                                                        <span class="text-sm text-gray-500">{{ $booking->customer->phone }}</span>
                                                    </div>
                                                    <div class="text-sm text-gray-600 mt-1">
                                                        {{ $booking->service->name }} - {{ $booking->vehicle_type }}
                                                    </div>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        Scheduled: {{ $booking->scheduled_at->format('M d, H:i') }}
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <div class="text-sm font-medium text-gray-900">${{ $booking->total_amount }}</div>
                                                    <div class="text-xs text-gray-500">{{ $booking->service->duration }}m</div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-gray-500 text-center py-4">No confirmed bookings</p>
                            @endif
                        </div>

                        <!-- Assignment Actions -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex space-x-3">
                                <button onclick="assignBay()" id="assign-btn" disabled
                                        class="flex-1 bg-blue-500 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded">
                                    Assign to Bay
                                </button>
                                <button onclick="cancelAssignment()"
                                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedType = null;
        let selectedId = null;

        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(tab => {
                tab.classList.remove('border-indigo-500', 'text-indigo-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-indigo-500', 'text-indigo-600');

            // Reset selection
            resetSelection();
        }

        function selectQueue(queueId) {
            resetSelection();
            selectedType = 'queue';
            selectedId = queueId;

            document.querySelector(`[data-queue-id="${queueId}"]`).classList.add('bg-blue-50', 'border-blue-200');
            document.getElementById('assign-btn').disabled = false;
        }

        function selectBooking(bookingId) {
            resetSelection();
            selectedType = 'booking';
            selectedId = bookingId;

            document.querySelector(`[data-booking-id="${bookingId}"]`).classList.add('bg-blue-50', 'border-blue-200');
            document.getElementById('assign-btn').disabled = false;
        }

        function resetSelection() {
            selectedType = null;
            selectedId = null;

            // Remove selection styling
            document.querySelectorAll('.queue-item, .booking-item').forEach(item => {
                item.classList.remove('bg-blue-50', 'border-blue-200');
            });

            document.getElementById('assign-btn').disabled = true;
        }

        function updateServiceDetails() {
            const select = document.querySelector('select[name="service_id"]');
            const selectedOption = select.options[select.selectedIndex];

            if (selectedOption.value) {
                document.querySelector('input[name="total_amount"]').value = selectedOption.dataset.price;
                document.querySelector('input[name="estimated_duration"]').value = selectedOption.dataset.duration;

                // Enable assign button for walk-in if form is valid
                if (validateWalkinForm()) {
                    selectedType = 'walkin';
                    selectedId = null;
                    document.getElementById('assign-btn').disabled = false;
                }
            } else {
                document.querySelector('input[name="total_amount"]').value = '';
                document.querySelector('input[name="estimated_duration"]').value = '';
                document.getElementById('assign-btn').disabled = true;
            }
        }

        function validateWalkinForm() {
            const form = document.getElementById('walkin-form');
            const requiredFields = ['customer_name', 'vehicle_type', 'service_id'];

            return requiredFields.every(field => {
                const input = form.querySelector(`[name="${field}"]`);
                return input && input.value.trim() !== '';
            });
        }

        function assignBay() {
            if (!selectedType) return;

            let data = {
                bay_id: {{ $bay->id }},
                type: selectedType
            };

            if (selectedType === 'queue') {
                data.queue_id = selectedId;
            } else if (selectedType === 'booking') {
                data.booking_id = selectedId;
            } else if (selectedType === 'walkin') {
                const form = document.getElementById('walkin-form');
                const formData = new FormData(form);
                for (let [key, value] of formData.entries()) {
                    data[key] = value;
                }
            }

            fetch('{{ route("bays.assign.store", $bay->id) }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Bay assigned successfully!');
                    window.location.href = '{{ route("bays.index") }}';
                } else {
                    alert('Error assigning bay: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while assigning the bay.');
            });
        }

        function cancelAssignment() {
            window.location.href = '{{ route("bays.index") }}';
        }

        // Initialize with queue tab
        showTab('queue');

        // Add form validation for walk-in
        document.getElementById('walkin-form').addEventListener('input', function() {
            if (validateWalkinForm() && document.querySelector('select[name="service_id"]').value) {
                selectedType = 'walkin';
                selectedId = null;
                document.getElementById('assign-btn').disabled = false;
            } else {
                document.getElementById('assign-btn').disabled = true;
            }
        });
    </script>
</x-app-layout>
```

## 🧪 Testing the Bay Management System

1. **Test Bay Assignment**:
   - Create bay assignments manually and automatically
   - Verify optimal bay selection algorithm
   - Test bay status transitions

2. **Test Real-time Tracking**:
   - Monitor service progress in real-time
   - Verify bay utilization calculations
   - Test equipment status updates

3. **Test Integration**:
   - Verify booking and queue integration
   - Test maintenance scheduling
   - Validate analytics accuracy

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Developed real-time bay status tracking system
✅ Built bay assignment optimization based on service type and duration
✅ Implemented service progress tracking within each bay
✅ Created bay utilization analytics and reporting
✅ Added equipment and resource allocation per bay
✅ Integrated bay management with booking and queue systems
✅ Built bay performance monitoring and optimization
✅ Created maintenance scheduling and tracking

### Bay Management Features Implemented:
- **Real-time Bay Tracking**: Live status updates for all service bays
- **Intelligent Assignment**: Optimal bay selection based on service requirements
- **Progress Monitoring**: Detailed tracking of service progress within bays
- **Utilization Analytics**: Comprehensive bay performance and efficiency metrics
- **Equipment Management**: Complete equipment tracking and maintenance scheduling
- **Integration**: Seamless integration with booking and queue systems
- **Maintenance Scheduling**: Automated maintenance planning and tracking
- **Performance Optimization**: Data-driven insights for bay optimization

## 🚀 What's Next?

In the next chapter, we'll:
- Integrate Midtrans payment gateway for Indonesian market
- Support local payment methods (GoPay, OVO, DANA)
- Implement webhook handling for payment status
- Add multi-currency support with IDR primary
- Create payment method selection interface

---

**Ready for Midtrans integration?** Let's move on to [Chapter 17: Midtrans Payment Gateway Integration](./17-midtrans-integration.md)!
