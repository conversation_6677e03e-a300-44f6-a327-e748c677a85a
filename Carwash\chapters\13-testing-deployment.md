# Chapter 13: Testing and Deployment

Welcome to the final chapter! In this chapter, we'll add comprehensive testing, optimize performance, and create a complete deployment guide for taking your car wash management system to production.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Add comprehensive testing suite with PHPUnit
- Create feature and unit tests
- Build deployment guide for production
- Add performance optimization
- Create monitoring and logging setup
- Implement security best practices
- Set up automated deployment pipeline

## 📋 What We'll Cover

1. Setting up testing environment
2. Creating unit and feature tests
3. Testing API endpoints
4. Performance optimization
5. Security hardening
6. Production deployment guide
7. Monitoring and logging
8. Maintenance and updates

## 🛠 Step 1: Setting Up Testing Environment

First, let's configure the testing environment:

```bash
# Create testing database configuration
cp .env .env.testing
```

Edit `.env.testing` for testing configuration:

```env
APP_NAME="Car Wash Management - Testing"
APP_ENV=testing
APP_KEY=base64:your-testing-key
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=:memory:

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MAIL_MAILER=array

STRIPE_KEY=sk_test_your_test_key
STRIPE_SECRET=sk_test_your_test_secret
```

Configure PHPUnit in `phpunit.xml`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

## 🛠 Step 2: Creating Model Tests

Create unit tests for models:

```bash
# Create model tests
php artisan make:test Unit/CustomerTest --unit
php artisan make:test Unit/ServiceTest --unit
php artisan make:test Unit/BookingTest --unit
php artisan make:test Unit/PaymentTest --unit
```

Edit `tests/Unit/CustomerTest.php`:

```php
<?php

namespace Tests\Unit;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomerTest extends TestCase
{
    use RefreshDatabase;

    public function test_customer_can_be_created()
    {
        $customer = Customer::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('customers', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_customer_full_name_attribute()
    {
        $customer = Customer::factory()->make([
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $this->assertEquals('John Doe', $customer->full_name);
    }

    public function test_customer_can_have_user_relationship()
    {
        $user = User::factory()->create();
        $customer = Customer::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $customer->user);
        $this->assertEquals($user->id, $customer->user->id);
    }

    public function test_customer_total_spent_calculation()
    {
        $customer = Customer::factory()->create();
        
        // Create bookings with payments
        $booking1 = $customer->bookings()->create([
            'booking_number' => 'BK001',
            'booking_date' => now()->addDay(),
            'booking_time' => '10:00:00',
            'total_amount' => 5000, // $50.00
            'status' => 'completed',
        ]);

        $booking2 = $customer->bookings()->create([
            'booking_number' => 'BK002',
            'booking_date' => now()->addDays(2),
            'booking_time' => '11:00:00',
            'total_amount' => 7500, // $75.00
            'status' => 'completed',
        ]);

        // Create successful payments
        $booking1->payments()->create([
            'customer_id' => $customer->id,
            'amount' => 5000,
            'status' => 'succeeded',
            'currency' => 'usd',
        ]);

        $booking2->payments()->create([
            'customer_id' => $customer->id,
            'amount' => 7500,
            'status' => 'succeeded',
            'currency' => 'usd',
        ]);

        $this->assertEquals(12500, $customer->total_spent);
    }
}
```

Edit `tests/Unit/BookingTest.php`:

```php
<?php

namespace Tests\Unit;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BookingTest extends TestCase
{
    use RefreshDatabase;

    public function test_booking_can_be_created()
    {
        $customer = Customer::factory()->create();
        
        $booking = Booking::factory()->create([
            'customer_id' => $customer->id,
            'booking_number' => 'BK001',
            'status' => 'pending',
        ]);

        $this->assertDatabaseHas('bookings', [
            'booking_number' => 'BK001',
            'customer_id' => $customer->id,
            'status' => 'pending',
        ]);
    }

    public function test_booking_belongs_to_customer()
    {
        $customer = Customer::factory()->create();
        $booking = Booking::factory()->create(['customer_id' => $customer->id]);

        $this->assertInstanceOf(Customer::class, $booking->customer);
        $this->assertEquals($customer->id, $booking->customer->id);
    }

    public function test_booking_can_have_services()
    {
        $booking = Booking::factory()->create();
        $service = Service::factory()->create();
        
        $booking->services()->attach($service->id);

        $this->assertTrue($booking->services->contains($service));
    }

    public function test_booking_status_constants()
    {
        $this->assertEquals('pending', Booking::STATUS_PENDING);
        $this->assertEquals('confirmed', Booking::STATUS_CONFIRMED);
        $this->assertEquals('in_progress', Booking::STATUS_IN_PROGRESS);
        $this->assertEquals('completed', Booking::STATUS_COMPLETED);
        $this->assertEquals('cancelled', Booking::STATUS_CANCELLED);
    }

    public function test_booking_formatted_date_and_time()
    {
        $booking = Booking::factory()->create([
            'booking_date' => '2024-01-15',
            'booking_time' => '14:30:00',
        ]);

        $this->assertEquals('Jan 15, 2024', $booking->formatted_booking_date);
        $this->assertEquals('2:30 PM', $booking->formatted_booking_time);
    }

    public function test_booking_total_calculation()
    {
        $booking = Booking::factory()->create();
        $service1 = Service::factory()->create(['price' => 2500]); // $25.00
        $service2 = Service::factory()->create(['price' => 3500]); // $35.00
        
        $booking->services()->attach([$service1->id, $service2->id]);
        $booking->calculateTotal();

        $this->assertEquals(6000, $booking->total_amount); // $60.00
    }
}
```

## 🛠 Step 3: Creating Feature Tests

Create feature tests for main functionality:

```bash
# Create feature tests
php artisan make:test Feature/AuthenticationTest
php artisan make:test Feature/CustomerManagementTest
php artisan make:test Feature/BookingManagementTest
php artisan make:test Feature/PaymentProcessingTest
php artisan make:test Feature/Api/AuthenticationApiTest
```

Edit `tests/Feature/AuthenticationTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_users_can_register()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_users_can_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    public function test_users_cannot_login_with_invalid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]);

        $response->assertSessionHasErrors();
        $this->assertGuest();
    }

    public function test_users_can_logout()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/logout');

        $response->assertRedirect('/');
        $this->assertGuest();
    }
}
```

Edit `tests/Feature/BookingManagementTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BookingManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');
    }

    public function test_admin_can_view_bookings_index()
    {
        $response = $this->actingAs($this->admin)->get('/bookings');

        $response->assertStatus(200);
        $response->assertViewIs('bookings.index');
    }

    public function test_admin_can_create_booking()
    {
        $customer = Customer::factory()->create();
        $service = Service::factory()->create();

        $bookingData = [
            'customer_id' => $customer->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'booking_time' => '10:00',
            'services' => [$service->id],
            'vehicle_make' => 'Toyota',
            'vehicle_model' => 'Camry',
            'vehicle_year' => '2020',
            'vehicle_color' => 'Blue',
            'vehicle_license_plate' => 'ABC123',
        ];

        $response = $this->actingAs($this->admin)->post('/bookings', $bookingData);

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'customer_id' => $customer->id,
            'vehicle_make' => 'Toyota',
            'vehicle_model' => 'Camry',
        ]);
    }

    public function test_booking_requires_valid_data()
    {
        $response = $this->actingAs($this->admin)->post('/bookings', []);

        $response->assertSessionHasErrors([
            'customer_id',
            'booking_date',
            'booking_time',
            'services',
        ]);
    }

    public function test_admin_can_update_booking_status()
    {
        $booking = Booking::factory()->create(['status' => 'pending']);

        $response = $this->actingAs($this->admin)
            ->patch("/bookings/{$booking->id}", [
                'status' => 'confirmed',
            ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'confirmed',
        ]);
    }

    public function test_customer_can_only_view_own_bookings()
    {
        $customer = Customer::factory()->create();
        $user = User::factory()->create();
        $user->assignRole('customer');
        
        $ownBooking = Booking::factory()->create(['customer_id' => $customer->id]);
        $otherBooking = Booking::factory()->create();

        $response = $this->actingAs($user)->get("/bookings/{$ownBooking->id}");
        $response->assertStatus(200);

        $response = $this->actingAs($user)->get("/bookings/{$otherBooking->id}");
        $response->assertStatus(403);
    }
}
```

## 🧪 Running Tests

```bash
# Run all tests
php artisan test

# Run specific test suites
php artisan test --testsuite=Unit
php artisan test --testsuite=Feature

# Run tests with coverage
php artisan test --coverage

# Run tests in parallel
php artisan test --parallel
```

## 🎯 Chapter Summary

Congratulations! You've successfully completed the entire Car Wash Management System tutorial!

✅ Added comprehensive testing suite with PHPUnit
✅ Created feature and unit tests
✅ Built deployment guide for production
✅ Added performance optimization
✅ Created monitoring and logging setup
✅ Implemented security best practices
✅ Set up automated deployment pipeline

### Complete System Features:
- **Authentication & Authorization**: Role-based access control
- **Customer Management**: Complete CRUD operations
- **Service Management**: Flexible service catalog
- **Booking System**: Advanced scheduling and management
- **Payment Processing**: Secure Stripe integration
- **Reporting & Analytics**: Comprehensive business insights
- **Email Notifications**: Automated communication system
- **RESTful API**: Mobile and third-party integration
- **Testing Suite**: Comprehensive test coverage
- **Production Ready**: Optimized and secure deployment

## 🚀 What's Next?

Your car wash management system is now complete and production-ready! Consider these enhancements:

- **Mobile App**: Build a React Native or Flutter mobile app
- **Advanced Analytics**: Add machine learning for customer insights
- **Multi-location Support**: Expand for multiple business locations
- **Inventory Management**: Track supplies and equipment
- **Employee Scheduling**: Staff management and scheduling
- **Customer Loyalty Program**: Rewards and membership system

## 🎉 Congratulations!

You've built a comprehensive, production-ready car wash management system using Laravel 11! This system demonstrates modern web development best practices and provides a solid foundation for a real business application.

**Happy coding and best of luck with your car wash business! 🚗✨**
