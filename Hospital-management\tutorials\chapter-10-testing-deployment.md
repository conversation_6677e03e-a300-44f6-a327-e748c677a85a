# Chapter 10: Testing and Deployment

## Overview
In this chapter, we'll implement comprehensive testing strategies and deployment procedures for the Hospital Employee Management System. We'll cover unit testing, integration testing, end-to-end testing, and production deployment with Indonesian healthcare compliance requirements.

## Learning Objectives
- Create comprehensive test suites for all components
- Implement automated testing pipelines
- Set up production deployment procedures
- Configure security and performance optimizations
- Implement monitoring and logging systems
- Ensure Indonesian healthcare compliance

## Prerequisites
- Completed Chapter 1-9
- Understanding of testing methodologies
- Familiarity with deployment procedures

## Duration
120-150 minutes

---

## Step 1: Comprehensive Testing Strategy

### 1.1 Create Feature Tests

```bash
php artisan make:test EmployeeManagementTest
php artisan make:test DepartmentManagementTest
php artisan make:test ShiftManagementTest
php artisan make:test AuthenticationTest
```

Edit `tests/Feature/EmployeeManagementTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\EmployeeType;
use App\Models\Position;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EmployeeManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\RolePermissionSeeder::class);
        Storage::fake('public');
    }

    public function test_admin_can_view_all_employees(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        Employee::factory()->count(10)->create();

        $response = $this->actingAs($admin, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'employee_number',
                            'full_name',
                            'department',
                            'position',
                            'employment_status',
                        ]
                    ],
                    'meta'
                ]);
    }

    public function test_department_head_can_only_view_department_employees(): void
    {
        $department1 = Department::factory()->create();
        $department2 = Department::factory()->create();

        $departmentHead = Employee::factory()->create(['department_id' => $department1->id]);
        $departmentHead->user->assignRole('Department Head');

        // Employees in same department
        Employee::factory()->count(3)->create(['department_id' => $department1->id]);
        
        // Employees in different department
        Employee::factory()->count(2)->create(['department_id' => $department2->id]);

        $response = $this->actingAs($departmentHead->user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(200);
        
        $employees = $response->json('data');
        $this->assertCount(4, $employees); // 3 + department head
        
        foreach ($employees as $employee) {
            $this->assertEquals($department1->id, $employee['department']['id']);
        }
    }

    public function test_can_create_employee_with_valid_data(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $department = Department::factory()->create();
        $employeeType = EmployeeType::factory()->create();
        $position = Position::factory()->create();

        $employeeData = [
            'first_name' => 'Dr. Ahmad',
            'last_name' => 'Wijaya',
            'date_of_birth' => '1985-05-15',
            'gender' => 'male',
            'phone_number' => '************',
            'emergency_contact_name' => 'Siti Wijaya',
            'emergency_contact_phone' => '628987654321',
            'address' => 'Jl. Sudirman No. 123',
            'city' => 'Jakarta',
            'province' => 'DKI Jakarta',
            'postal_code' => '12190',
            'hire_date' => '2024-01-15',
            'employment_status' => 'active',
            'employee_type_id' => $employeeType->id,
            'department_id' => $department->id,
            'position_id' => $position->id,
            'email' => '<EMAIL>',
        ];

        $response = $this->actingAs($admin, 'sanctum')
                        ->postJson('/api/employees', $employeeData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'employee_number',
                        'full_name',
                    ]
                ]);

        $this->assertDatabaseHas('employees', [
            'first_name' => 'Dr. Ahmad',
            'last_name' => 'Wijaya',
            'phone_number' => '************',
        ]);

        // Check if user account was created
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_validates_indonesian_phone_number_format(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $department = Department::factory()->create();
        $employeeType = EmployeeType::factory()->create();
        $position = Position::factory()->create();

        $employeeData = [
            'first_name' => 'Ahmad',
            'last_name' => 'Wijaya',
            'date_of_birth' => '1985-05-15',
            'gender' => 'male',
            'phone_number' => '***********', // Indonesian format without +62
            'emergency_contact_name' => 'Siti Wijaya',
            'emergency_contact_phone' => '***********',
            'address' => 'Jl. Sudirman No. 123',
            'city' => 'Jakarta',
            'province' => 'DKI Jakarta',
            'postal_code' => '12190',
            'hire_date' => '2024-01-15',
            'employment_status' => 'active',
            'employee_type_id' => $employeeType->id,
            'department_id' => $department->id,
            'position_id' => $position->id,
        ];

        $response = $this->actingAs($admin, 'sanctum')
                        ->postJson('/api/employees', $employeeData);

        $response->assertStatus(201);

        // Check if phone numbers were normalized to +62 format
        $employee = Employee::latest()->first();
        $this->assertEquals('************', $employee->phone_number);
        $this->assertEquals('628987654321', $employee->emergency_contact_phone);
    }

    public function test_can_upload_profile_photo(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $department = Department::factory()->create();
        $employeeType = EmployeeType::factory()->create();
        $position = Position::factory()->create();

        $file = UploadedFile::fake()->image('profile.jpg', 300, 300);

        $employeeData = [
            'first_name' => 'Ahmad',
            'last_name' => 'Wijaya',
            'date_of_birth' => '1985-05-15',
            'gender' => 'male',
            'phone_number' => '************',
            'emergency_contact_name' => 'Siti Wijaya',
            'emergency_contact_phone' => '628987654321',
            'address' => 'Jl. Sudirman No. 123',
            'city' => 'Jakarta',
            'province' => 'DKI Jakarta',
            'postal_code' => '12190',
            'hire_date' => '2024-01-15',
            'employment_status' => 'active',
            'employee_type_id' => $employeeType->id,
            'department_id' => $department->id,
            'position_id' => $position->id,
            'profile_photo' => $file,
        ];

        $response = $this->actingAs($admin, 'sanctum')
                        ->postJson('/api/employees', $employeeData);

        $response->assertStatus(201);

        $employee = Employee::latest()->first();
        $this->assertNotNull($employee->profile_photo_path);
        Storage::disk('public')->assertExists($employee->profile_photo_path);
    }

    public function test_can_perform_bulk_operations(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $employees = Employee::factory()->count(5)->create(['employment_status' => 'inactive']);
        $employeeIds = $employees->pluck('id')->toArray();

        $response = $this->actingAs($admin, 'sanctum')
                        ->postJson('/api/employees/bulk-action', [
                            'action' => 'activate',
                            'employee_ids' => $employeeIds,
                        ]);

        $response->assertStatus(200)
                ->assertJsonFragment(['updated_count' => 5]);

        foreach ($employees as $employee) {
            $this->assertDatabaseHas('employees', [
                'id' => $employee->id,
                'employment_status' => 'active',
            ]);
        }
    }

    public function test_search_functionality_works_correctly(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $department = Department::factory()->create(['name' => 'Kardiologi']);
        
        Employee::factory()->create([
            'first_name' => 'Dr. Ahmad',
            'last_name' => 'Kardio',
            'department_id' => $department->id,
        ]);
        
        Employee::factory()->create([
            'first_name' => 'Siti',
            'last_name' => 'Perawat',
        ]);

        // Search by name
        $response = $this->actingAs($admin, 'sanctum')
                        ->getJson('/api/employees?search=Ahmad');

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertStringContainsString('Ahmad', $response->json('data.0.full_name'));

        // Search by department
        $response = $this->actingAs($admin, 'sanctum')
                        ->getJson('/api/employees?search=Kardiologi');

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('Kardiologi', $response->json('data.0.department.name'));
    }

    public function test_cannot_delete_employee_with_subordinates(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $supervisor = Employee::factory()->create();
        $subordinate = Employee::factory()->create(['supervisor_id' => $supervisor->id]);

        $response = $this->actingAs($admin, 'sanctum')
                        ->deleteJson("/api/employees/{$supervisor->id}");

        $response->assertStatus(422)
                ->assertJsonFragment([
                    'message' => 'Tidak dapat menghapus karyawan yang memiliki bawahan. Silakan pindahkan bawahan terlebih dahulu.'
                ]);

        $this->assertDatabaseHas('employees', ['id' => $supervisor->id]);
    }

    public function test_employee_statistics_are_accurate(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('Super Admin');

        $department = Department::factory()->create();
        $medicalType = EmployeeType::factory()->create(['is_medical_staff' => true]);
        $nonMedicalType = EmployeeType::factory()->create(['is_medical_staff' => false]);

        // Create medical staff
        Employee::factory()->count(3)->create([
            'department_id' => $department->id,
            'employee_type_id' => $medicalType->id,
            'employment_status' => 'active',
        ]);

        // Create non-medical staff
        Employee::factory()->count(2)->create([
            'department_id' => $department->id,
            'employee_type_id' => $nonMedicalType->id,
            'employment_status' => 'active',
        ]);

        // Create inactive employee
        Employee::factory()->create([
            'department_id' => $department->id,
            'employee_type_id' => $medicalType->id,
            'employment_status' => 'inactive',
        ]);

        $response = $this->actingAs($admin, 'sanctum')
                        ->getJson('/api/employees/statistics');

        $response->assertStatus(200);
        
        $stats = $response->json('data');
        $this->assertEquals(6, $stats['total_employees']);
        $this->assertEquals(5, $stats['active_employees']);
        $this->assertEquals(4, $stats['medical_staff_count']);
        $this->assertEquals(2, $stats['non_medical_staff_count']);
    }
}
```

---

## Step 2: Production Deployment Configuration

### 2.1 Create Production Environment Configuration

Create `.env.production`:

```env
APP_NAME="Hospital Employee Management System"
APP_ENV=production
APP_KEY=base64:your-production-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=your-production-db-host
DB_PORT=3306
DB_DATABASE=hospital_ems_production
DB_USERNAME=your-db-username
DB_PASSWORD=your-secure-db-password

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Indonesian Healthcare Specific
HOSPITAL_NAME="Rumah Sakit Umum"
HOSPITAL_ADDRESS="Jl. Kesehatan No. 123, Jakarta"
HOSPITAL_PHONE="+62-21-1234567"
HOSPITAL_EMAIL="<EMAIL>"

# Security Settings
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

### 2.2 Create Deployment Script

Create `deploy.sh`:

```bash
#!/bin/bash

# Hospital Employee Management System Deployment Script
# Indonesian Healthcare Context

echo "🏥 Starting Hospital EMS Deployment..."

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "❌ Please do not run this script as root"
    exit 1
fi

# Set variables
PROJECT_DIR="/var/www/hospital-ems"
BACKUP_DIR="/var/backups/hospital-ems"
LOG_FILE="/var/log/hospital-ems-deploy.log"

# Create backup directory if it doesn't exist
sudo mkdir -p $BACKUP_DIR
sudo mkdir -p $(dirname $LOG_FILE)

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | sudo tee -a $LOG_FILE
}

log_message "🚀 Starting deployment process"

# Navigate to project directory
cd $PROJECT_DIR || exit 1

# Create database backup
log_message "📦 Creating database backup"
sudo mysqldump -u root hospital_ems_production > $BACKUP_DIR/database_$(date +%Y%m%d_%H%M%S).sql

# Pull latest changes
log_message "📥 Pulling latest changes from repository"
git pull origin main

# Install/update dependencies
log_message "📦 Installing PHP dependencies"
composer install --no-dev --optimize-autoloader

log_message "📦 Installing Node.js dependencies"
npm ci

# Build assets
log_message "🔨 Building production assets"
npm run build

# Clear and cache config
log_message "⚙️ Optimizing configuration"
php artisan config:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run database migrations
log_message "🗄️ Running database migrations"
php artisan migrate --force

# Clear application cache
log_message "🧹 Clearing application cache"
php artisan cache:clear
php artisan queue:restart

# Set proper permissions
log_message "🔐 Setting file permissions"
sudo chown -R www-data:www-data $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR
sudo chmod -R 775 $PROJECT_DIR/storage
sudo chmod -R 775 $PROJECT_DIR/bootstrap/cache

# Restart services
log_message "🔄 Restarting services"
sudo systemctl restart nginx
sudo systemctl restart php8.2-fpm
sudo systemctl restart redis-server

# Health check
log_message "🏥 Performing health check"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost)

if [ $HTTP_STATUS -eq 200 ]; then
    log_message "✅ Deployment successful - Application is responding"
    echo "✅ Hospital EMS deployment completed successfully!"
else
    log_message "❌ Deployment failed - Application not responding (HTTP $HTTP_STATUS)"
    echo "❌ Deployment failed! Check logs at $LOG_FILE"
    exit 1
fi

log_message "🎉 Deployment process completed"
```
