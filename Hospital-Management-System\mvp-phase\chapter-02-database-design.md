# Chapter 02 - Database Design dan Schema

## Tujuan Chapter
Pada chapter ini, kita akan:
- Merancang database schema untuk Hospital Management System
- Membuat migrations untuk semua entities utama
- Setup relationships antar models
- Implementasi database seeding untuk data awal

## Database Schema Overview

### Core Entities untuk MVP:
1. **Users** - Sistem autentikasi
2. **Patients** - Data pasien
3. **Doctors** - Data dokter
4. **Staff** - Data staff rumah sakit
5. **Departments** - Departemen/unit rumah sakit
6. **Appointments** - Janji temu
7. **Medical Records** - Rekam medis dasar

## Langkah 1: Create Migrations

### 1.1 Migration untuk Departments
```bash
php artisan make:migration create_departments_table
```

Edit `database/migrations/xxxx_create_departments_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 10)->unique();
            $table->text('description')->nullable();
            $table->string('location')->nullable();
            $table->string('phone')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
```

### 1.2 Migration untuk Patients
```bash
php artisan make:migration create_patients_table
```

Edit `database/migrations/xxxx_create_patients_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('patients', function (Blueprint $table) {
            $table->id();
            $table->string('patient_id', 20)->unique();
            $table->string('nik', 16)->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female']);
            $table->string('phone', 15);
            $table->string('email')->nullable();
            $table->text('address');
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone', 15);
            $table->string('blood_type', 5)->nullable();
            $table->text('allergies')->nullable();
            $table->text('medical_history')->nullable();
            $table->enum('marital_status', ['single', 'married', 'divorced', 'widowed']);
            $table->string('occupation')->nullable();
            $table->string('insurance_number')->nullable();
            $table->string('insurance_provider')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('patients');
    }
};
```

### 1.3 Migration untuk Doctors
```bash
php artisan make:migration create_doctors_table
```

Edit `database/migrations/xxxx_create_doctors_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('restrict');
            $table->string('doctor_id', 20)->unique();
            $table->string('license_number', 50)->unique();
            $table->string('specialization');
            $table->string('qualification');
            $table->integer('experience_years');
            $table->string('phone', 15);
            $table->text('bio')->nullable();
            $table->decimal('consultation_fee', 10, 2);
            $table->json('available_days'); // ['monday', 'tuesday', ...]
            $table->time('available_from');
            $table->time('available_to');
            $table->boolean('is_available')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
```

### 1.4 Migration untuk Staff
```bash
php artisan make:migration create_staff_table
```

Edit `database/migrations/xxxx_create_staff_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('staff', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('restrict');
            $table->string('staff_id', 20)->unique();
            $table->string('position');
            $table->string('employee_type'); // full-time, part-time, contract
            $table->date('hire_date');
            $table->decimal('salary', 12, 2);
            $table->string('phone', 15);
            $table->text('address');
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone', 15);
            $table->json('shift_schedule')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('staff');
    }
};
```

### 1.5 Migration untuk Appointments
```bash
php artisan make:migration create_appointments_table
```

Edit `database/migrations/xxxx_create_appointments_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->string('appointment_number', 20)->unique();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained()->onDelete('restrict');
            $table->foreignId('department_id')->constrained()->onDelete('restrict');
            $table->datetime('appointment_date');
            $table->enum('appointment_type', ['consultation', 'follow_up', 'emergency', 'surgery']);
            $table->enum('status', ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show']);
            $table->text('reason_for_visit');
            $table->text('notes')->nullable();
            $table->decimal('fee', 10, 2);
            $table->enum('payment_status', ['pending', 'paid', 'cancelled']);
            $table->datetime('checked_in_at')->nullable();
            $table->datetime('checked_out_at')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
```

### 1.6 Migration untuk Medical Records
```bash
php artisan make:migration create_medical_records_table
```

Edit `database/migrations/xxxx_create_medical_records_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('medical_records', function (Blueprint $table) {
            $table->id();
            $table->string('record_number', 20)->unique();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained()->onDelete('restrict');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->datetime('visit_date');
            $table->text('chief_complaint');
            $table->text('history_of_present_illness');
            $table->text('physical_examination');
            $table->text('diagnosis');
            $table->text('treatment_plan');
            $table->text('medications')->nullable();
            $table->text('lab_results')->nullable();
            $table->text('notes')->nullable();
            $table->datetime('next_visit_date')->nullable();
            $table->decimal('vital_signs_temperature', 4, 1)->nullable();
            $table->integer('vital_signs_blood_pressure_systolic')->nullable();
            $table->integer('vital_signs_blood_pressure_diastolic')->nullable();
            $table->integer('vital_signs_heart_rate')->nullable();
            $table->integer('vital_signs_respiratory_rate')->nullable();
            $table->decimal('vital_signs_weight', 5, 2)->nullable();
            $table->decimal('vital_signs_height', 5, 2)->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('medical_records');
    }
};
```

## Langkah 2: Update Users Migration

### 2.1 Modify Users Table
```bash
php artisan make:migration add_hospital_fields_to_users_table --table=users
```

Edit migration file:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('first_name')->after('name');
            $table->string('last_name')->after('first_name');
            $table->enum('role', ['admin', 'doctor', 'nurse', 'receptionist', 'pharmacist', 'lab_technician'])->after('email');
            $table->string('phone', 15)->nullable()->after('role');
            $table->boolean('is_active')->default(true)->after('phone');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['first_name', 'last_name', 'role', 'phone', 'is_active']);
        });
    }
};
```

## Langkah 3: Run Migrations

```bash
php artisan migrate
```

## Langkah 4: Create Database Seeders

### 4.1 Department Seeder
```bash
php artisan make:seeder DepartmentSeeder
```

Edit `database/seeders/DepartmentSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DepartmentSeeder extends Seeder
{
    public function run(): void
    {
        $departments = [
            ['name' => 'Poli Umum', 'code' => 'UMUM', 'description' => 'Pelayanan kesehatan umum', 'location' => 'Lantai 1'],
            ['name' => 'Poli Anak', 'code' => 'ANAK', 'description' => 'Pelayanan kesehatan anak', 'location' => 'Lantai 2'],
            ['name' => 'Poli Kandungan', 'code' => 'OBGYN', 'description' => 'Pelayanan obstetri dan ginekologi', 'location' => 'Lantai 2'],
            ['name' => 'Poli Jantung', 'code' => 'JANTUNG', 'description' => 'Pelayanan kardiologi', 'location' => 'Lantai 3'],
            ['name' => 'Poli Mata', 'code' => 'MATA', 'description' => 'Pelayanan oftalmologi', 'location' => 'Lantai 1'],
            ['name' => 'Unit Gawat Darurat', 'code' => 'UGD', 'description' => 'Pelayanan gawat darurat 24 jam', 'location' => 'Lantai Dasar'],
            ['name' => 'Laboratorium', 'code' => 'LAB', 'description' => 'Pelayanan laboratorium medis', 'location' => 'Lantai Dasar'],
            ['name' => 'Farmasi', 'code' => 'FARMASI', 'description' => 'Pelayanan farmasi dan obat-obatan', 'location' => 'Lantai 1'],
        ];

        foreach ($departments as $department) {
            DB::table('departments')->insert(array_merge($department, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
```

### 4.2 Update DatabaseSeeder
Edit `database/seeders/DatabaseSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            DepartmentSeeder::class,
        ]);
    }
}
```

### 4.3 Run Seeders
```bash
php artisan db:seed
```

## Langkah 5: Verifikasi Database

### 5.1 Check Tables
```bash
php artisan tinker
```

```php
// Dalam tinker
DB::table('departments')->count();
Schema::hasTable('patients');
Schema::hasTable('doctors');
Schema::hasTable('appointments');
Schema::hasTable('medical_records');
```

## Database Relationships Summary

```
Users (1) -> (1) Doctors
Users (1) -> (1) Staff
Departments (1) -> (*) Doctors
Departments (1) -> (*) Staff
Patients (1) -> (*) Appointments
Doctors (1) -> (*) Appointments
Departments (1) -> (*) Appointments
Patients (1) -> (*) Medical Records
Doctors (1) -> (*) Medical Records
Appointments (1) -> (0..1) Medical Records
```

## Testing Database Schema

### Create Test Data
```bash
php artisan tinker
```

```php
// Test department creation
$dept = DB::table('departments')->first();
echo $dept->name;
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Membuat Eloquent models untuk semua entities
- Setup relationships antar models
- Implementasi model factories untuk testing

## Checklist Completion

- [ ] Semua migrations dibuat dan dijalankan
- [ ] Database schema sesuai dengan requirements
- [ ] Relationships antar tables sudah benar
- [ ] Department seeder berjalan dengan baik
- [ ] Database dapat diakses melalui tinker
- [ ] Semua foreign keys terkonfigurasi dengan benar

**Estimasi Waktu**: 45-60 menit

**Difficulty Level**: Intermediate

**File yang Dibuat**:
- 6 migration files
- 1 seeder file
- Database schema lengkap untuk MVP
