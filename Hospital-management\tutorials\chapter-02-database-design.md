# Chapter 2: Database Design and Migrations

## Overview
In this chapter, we'll design and implement a comprehensive database schema for our Hospital Employee Management System. The schema will accommodate the complex organizational structure of Indonesian hospitals, including various employee types, departments, shifts, and compliance requirements.

## Learning Objectives
- Design comprehensive database schema for hospital employee management
- Create Laravel migrations for all core tables
- Understand relationships between hospital entities
- Implement proper indexing and constraints

## Prerequisites
- Completed Chapter 1 (Project Setup)
- Understanding of database design principles
- Familiarity with Laravel migrations
- Knowledge of foreign key relationships

## Duration
60-75 minutes

---

## Step 1: Database Schema Planning

### 1.1 Hospital Organizational Structure Analysis

Indonesian hospitals typically have the following structure:
- **Direktur/CEO** (Hospital Director)
- **<PERSON><PERSON><PERSON>** (Deputy Directors)
- **<PERSON><PERSON><PERSON>** (Department Heads)
- **Supervisor/Koordinator** (Supervisors)
- **Staff** (Various types of staff)

### 1.2 Employee Types in Indonesian Healthcare

```
Medical Staff:
├── <PERSON>kt<PERSON> (Specialist Doctors)
├── <PERSON><PERSON><PERSON> (General Practitioners)
├── <PERSON><PERSON><PERSON> (Nurses)
├── <PERSON><PERSON><PERSON> (Midwives)
└── <PERSON><PERSON> (Other Healthcare Workers)

Non-Medical Staff:
├── <PERSON>mini<PERSON> (Administrative Staff)
├── <PERSON>aga <PERSON> (Support Staff)
├── <PERSON><PERSON> (Security Staff)
└── Tenaga <PERSON>an (Cleaning Staff)
```

### 1.3 Key Relationships
- Employee → Department (Many-to-One)
- Employee → Supervisor (Many-to-One, Self-referencing)
- Employee → Shifts (Many-to-Many)
- Employee → Licenses (One-to-Many)
- Department → Department Head (One-to-One)

---

## Step 2: Core Entity Migrations

### 2.1 Create Employee Types Migration

```bash
php artisan make:migration create_employee_types_table
```

Edit `database/migrations/xxxx_create_employee_types_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('employee_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., Dokter Spesialis, Perawat
            $table->string('code', 10)->unique(); // e.g., DS, PER, BID
            $table->text('description')->nullable();
            $table->boolean('requires_license')->default(false);
            $table->string('minimum_education_level')->nullable();
            $table->boolean('is_medical_staff')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employee_types');
    }
};
```

### 2.2 Create Departments Migration

```bash
php artisan make:migration create_departments_table
```

Edit `database/migrations/xxxx_create_departments_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., IGD, ICU, Rawat Inap
            $table->string('code', 10)->unique(); // e.g., ER, ICU, WARD
            $table->text('description')->nullable();
            $table->unsignedBigInteger('parent_department_id')->nullable();
            $table->unsignedBigInteger('department_head_id')->nullable();
            $table->string('location')->nullable();
            $table->string('phone_extension', 20)->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('parent_department_id')
                  ->references('id')
                  ->on('departments')
                  ->onDelete('set null');
            
            $table->index(['is_active', 'parent_department_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
```

### 2.3 Create Positions Migration

```bash
php artisan make:migration create_positions_table
```

Edit `database/migrations/xxxx_create_positions_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('positions', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // e.g., Kepala Departemen, Supervisor, Staff
            $table->enum('level', ['executive', 'manager', 'supervisor', 'staff']);
            $table->text('description')->nullable();
            $table->text('responsibilities')->nullable();
            $table->text('requirements')->nullable();
            $table->decimal('min_salary', 12, 2)->nullable();
            $table->decimal('max_salary', 12, 2)->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('positions');
    }
};
```

### 2.4 Create Employees Migration

```bash
php artisan make:migration create_employees_table
```

Edit `database/migrations/xxxx_create_employees_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('employee_number')->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('full_name')->virtualAs("CONCAT(first_name, ' ', last_name)");
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female']);
            $table->string('phone_number', 20);
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone', 20);
            $table->text('address');
            $table->string('city');
            $table->string('province');
            $table->string('postal_code', 10);
            $table->date('hire_date');
            $table->enum('employment_status', ['active', 'inactive', 'terminated', 'suspended'])
                  ->default('active');
            $table->foreignId('employee_type_id')->constrained();
            $table->foreignId('department_id')->constrained();
            $table->foreignId('position_id')->constrained();
            $table->unsignedBigInteger('supervisor_id')->nullable();
            $table->string('salary_grade', 10)->nullable();
            $table->decimal('basic_salary', 12, 2)->nullable();
            $table->string('profile_photo_path', 2048)->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('supervisor_id')
                  ->references('id')
                  ->on('employees')
                  ->onDelete('set null');

            // Indexes for performance
            $table->index(['department_id', 'employment_status']);
            $table->index(['employee_type_id', 'employment_status']);
            $table->index(['supervisor_id']);
            $table->index(['hire_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
```

---

## Step 3: Professional Licenses and Certifications

### 3.1 Create Employee Licenses Migration

```bash
php artisan make:migration create_employee_licenses_table
```

Edit `database/migrations/xxxx_create_employee_licenses_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('employee_licenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->string('license_type'); // STR, SIP, Sertifikat Kompetensi
            $table->string('license_number')->unique();
            $table->string('issuing_authority');
            $table->date('issue_date');
            $table->date('expiry_date');
            $table->enum('status', ['active', 'expired', 'suspended', 'revoked'])
                  ->default('active');
            $table->string('document_path', 2048)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Index for expiry tracking
            $table->index(['expiry_date', 'status']);
            $table->index(['employee_id', 'license_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employee_licenses');
    }
};
```

---

## Step 4: Shift Management Tables

### 4.1 Create Shifts Migration

```bash
php artisan make:migration create_shifts_table
```

Edit `database/migrations/xxxx_create_shifts_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('shifts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Pagi, Siang, Malam, Jaga
            $table->time('start_time');
            $table->time('end_time');
            $table->decimal('duration_hours', 4, 2);
            $table->boolean('is_overnight')->default(false);
            $table->enum('shift_type', ['regular', 'on_call', 'emergency'])
                  ->default('regular');
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('shifts');
    }
};
```

### 4.2 Create Employee Shifts Migration

```bash
php artisan make:migration create_employee_shifts_table
```

Edit `database/migrations/xxxx_create_employee_shifts_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('employee_shifts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('shift_id')->constrained();
            $table->foreignId('department_id')->constrained();
            $table->date('date');
            $table->enum('status', [
                'scheduled', 'confirmed', 'completed', 
                'absent', 'sick_leave', 'cancelled'
            ])->default('scheduled');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->time('break_start_time')->nullable();
            $table->time('break_end_time')->nullable();
            $table->decimal('overtime_hours', 4, 2)->default(0);
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            // Prevent double booking
            $table->unique(['employee_id', 'date', 'shift_id']);
            
            // Indexes for performance
            $table->index(['date', 'employee_id']);
            $table->index(['department_id', 'date']);
            $table->index(['status', 'date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employee_shifts');
    }
};
```

---

## Step 5: Leave Management Tables

### 5.1 Create Leave Types Migration

```bash
php artisan make:migration create_leave_types_table
```

Edit `database/migrations/xxxx_create_leave_types_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('leave_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Cuti Tahunan, Cuti Sakit, etc.
            $table->string('code', 10)->unique();
            $table->integer('max_days_per_year')->nullable();
            $table->boolean('requires_medical_certificate')->default(false);
            $table->boolean('is_paid')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('leave_types');
    }
};
```

### 5.2 Create Leave Requests Migration

```bash
php artisan make:migration create_leave_requests_table
```

Edit `database/migrations/xxxx_create_leave_requests_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('leave_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('leave_type_id')->constrained();
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('total_days');
            $table->text('reason');
            $table->enum('status', ['pending', 'approved', 'rejected', 'cancelled'])
                  ->default('pending');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->string('medical_certificate_path', 2048)->nullable();
            $table->timestamps();

            $table->foreign('approved_by')
                  ->references('id')
                  ->on('employees')
                  ->onDelete('set null');

            // Indexes for performance
            $table->index(['employee_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index(['status', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('leave_requests');
    }
};
```

---

## Step 6: Add Foreign Key to Departments

### 6.1 Update Departments Table for Department Head

```bash
php artisan make:migration add_department_head_foreign_key_to_departments_table
```

Edit the migration:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            $table->foreign('department_head_id')
                  ->references('id')
                  ->on('employees')
                  ->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            $table->dropForeign(['department_head_id']);
        });
    }
};
```

---

## Step 7: Run Migrations

### 7.1 Execute All Migrations

```bash
php artisan migrate
```

### 7.2 Verify Migration Status

```bash
php artisan migrate:status
```

You should see all migrations marked as "Ran".

---

## Step 8: Create Database Seeders

### 8.1 Create Employee Types Seeder

```bash
php artisan make:seeder EmployeeTypeSeeder
```

Edit `database/seeders/EmployeeTypeSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EmployeeTypeSeeder extends Seeder
{
    public function run(): void
    {
        $employeeTypes = [
            [
                'name' => 'Dokter Spesialis',
                'code' => 'DS',
                'description' => 'Dokter dengan spesialisasi tertentu',
                'requires_license' => true,
                'minimum_education_level' => 'S2',
                'is_medical_staff' => true,
            ],
            [
                'name' => 'Dokter Umum',
                'code' => 'DU',
                'description' => 'Dokter umum/general practitioner',
                'requires_license' => true,
                'minimum_education_level' => 'S1',
                'is_medical_staff' => true,
            ],
            [
                'name' => 'Perawat',
                'code' => 'PER',
                'description' => 'Tenaga keperawatan',
                'requires_license' => true,
                'minimum_education_level' => 'D3',
                'is_medical_staff' => true,
            ],
            [
                'name' => 'Bidan',
                'code' => 'BID',
                'description' => 'Tenaga kebidanan',
                'requires_license' => true,
                'minimum_education_level' => 'D3',
                'is_medical_staff' => true,
            ],
            [
                'name' => 'Tenaga Administrasi',
                'code' => 'ADM',
                'description' => 'Staff administrasi dan manajemen',
                'requires_license' => false,
                'minimum_education_level' => 'SMA',
                'is_medical_staff' => false,
            ],
        ];

        foreach ($employeeTypes as $type) {
            DB::table('employee_types')->insert(array_merge($type, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
```

### 8.2 Create Departments Seeder

```bash
php artisan make:seeder DepartmentSeeder
```

Edit `database/seeders/DepartmentSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DepartmentSeeder extends Seeder
{
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Instalasi Gawat Darurat',
                'code' => 'IGD',
                'description' => 'Unit pelayanan gawat darurat 24 jam',
                'location' => 'Lantai 1',
                'phone_extension' => '101',
            ],
            [
                'name' => 'Intensive Care Unit',
                'code' => 'ICU',
                'description' => 'Unit perawatan intensif',
                'location' => 'Lantai 3',
                'phone_extension' => '301',
            ],
            [
                'name' => 'Rawat Inap',
                'code' => 'RANAP',
                'description' => 'Unit rawat inap',
                'location' => 'Lantai 2-4',
                'phone_extension' => '201',
            ],
            [
                'name' => 'Rawat Jalan',
                'code' => 'RAJAL',
                'description' => 'Unit rawat jalan dan poliklinik',
                'location' => 'Lantai 1',
                'phone_extension' => '102',
            ],
            [
                'name' => 'Laboratorium',
                'code' => 'LAB',
                'description' => 'Laboratorium klinik',
                'location' => 'Lantai 1',
                'phone_extension' => '103',
            ],
            [
                'name' => 'Radiologi',
                'code' => 'RAD',
                'description' => 'Unit radiologi dan imaging',
                'location' => 'Lantai 1',
                'phone_extension' => '104',
            ],
            [
                'name' => 'Farmasi',
                'code' => 'FARM',
                'description' => 'Unit farmasi dan apotek',
                'location' => 'Lantai 1',
                'phone_extension' => '105',
            ],
            [
                'name' => 'Administrasi',
                'code' => 'ADM',
                'description' => 'Bagian administrasi dan keuangan',
                'location' => 'Lantai 5',
                'phone_extension' => '501',
            ],
        ];

        foreach ($departments as $dept) {
            DB::table('departments')->insert(array_merge($dept, [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
```

### 8.3 Create Positions Seeder

```bash
php artisan make:seeder PositionSeeder
```

Edit `database/seeders/PositionSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PositionSeeder extends Seeder
{
    public function run(): void
    {
        $positions = [
            [
                'title' => 'Direktur Rumah Sakit',
                'level' => 'executive',
                'description' => 'Pemimpin tertinggi rumah sakit',
                'min_salary' => 25000000,
                'max_salary' => 50000000,
            ],
            [
                'title' => 'Kepala Departemen',
                'level' => 'manager',
                'description' => 'Kepala unit/departemen',
                'min_salary' => 15000000,
                'max_salary' => 30000000,
            ],
            [
                'title' => 'Supervisor',
                'level' => 'supervisor',
                'description' => 'Supervisor/koordinator',
                'min_salary' => 8000000,
                'max_salary' => 15000000,
            ],
            [
                'title' => 'Staff Senior',
                'level' => 'staff',
                'description' => 'Staff senior berpengalaman',
                'min_salary' => 6000000,
                'max_salary' => 12000000,
            ],
            [
                'title' => 'Staff',
                'level' => 'staff',
                'description' => 'Staff pelaksana',
                'min_salary' => 4000000,
                'max_salary' => 8000000,
            ],
        ];

        foreach ($positions as $position) {
            DB::table('positions')->insert(array_merge($position, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
```

### 8.4 Create Shifts Seeder

```bash
php artisan make:seeder ShiftSeeder
```

Edit `database/seeders/ShiftSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ShiftSeeder extends Seeder
{
    public function run(): void
    {
        $shifts = [
            [
                'name' => 'Shift Pagi',
                'start_time' => '07:00:00',
                'end_time' => '14:00:00',
                'duration_hours' => 7.0,
                'is_overnight' => false,
                'shift_type' => 'regular',
                'description' => 'Shift pagi reguler',
            ],
            [
                'name' => 'Shift Siang',
                'start_time' => '14:00:00',
                'end_time' => '21:00:00',
                'duration_hours' => 7.0,
                'is_overnight' => false,
                'shift_type' => 'regular',
                'description' => 'Shift siang reguler',
            ],
            [
                'name' => 'Shift Malam',
                'start_time' => '21:00:00',
                'end_time' => '07:00:00',
                'duration_hours' => 10.0,
                'is_overnight' => true,
                'shift_type' => 'regular',
                'description' => 'Shift malam reguler',
            ],
            [
                'name' => 'Jaga',
                'start_time' => '08:00:00',
                'end_time' => '08:00:00',
                'duration_hours' => 24.0,
                'is_overnight' => true,
                'shift_type' => 'on_call',
                'description' => 'Shift jaga 24 jam',
            ],
        ];

        foreach ($shifts as $shift) {
            DB::table('shifts')->insert(array_merge($shift, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
```

### 8.5 Create Leave Types Seeder

```bash
php artisan make:seeder LeaveTypeSeeder
```

Edit `database/seeders/LeaveTypeSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeaveTypeSeeder extends Seeder
{
    public function run(): void
    {
        $leaveTypes = [
            [
                'name' => 'Cuti Tahunan',
                'code' => 'ANNUAL',
                'max_days_per_year' => 12,
                'requires_medical_certificate' => false,
                'is_paid' => true,
                'description' => 'Cuti tahunan karyawan',
            ],
            [
                'name' => 'Cuti Sakit',
                'code' => 'SICK',
                'max_days_per_year' => null,
                'requires_medical_certificate' => true,
                'is_paid' => true,
                'description' => 'Cuti karena sakit dengan surat dokter',
            ],
            [
                'name' => 'Cuti Melahirkan',
                'code' => 'MATERNITY',
                'max_days_per_year' => 90,
                'requires_medical_certificate' => true,
                'is_paid' => true,
                'description' => 'Cuti melahirkan untuk karyawan wanita',
            ],
            [
                'name' => 'Cuti Haji',
                'code' => 'HAJJ',
                'max_days_per_year' => null,
                'requires_medical_certificate' => false,
                'is_paid' => false,
                'description' => 'Cuti untuk menunaikan ibadah haji',
            ],
            [
                'name' => 'Cuti Darurat',
                'code' => 'EMERGENCY',
                'max_days_per_year' => 3,
                'requires_medical_certificate' => false,
                'is_paid' => true,
                'description' => 'Cuti darurat untuk keperluan mendesak',
            ],
        ];

        foreach ($leaveTypes as $type) {
            DB::table('leave_types')->insert(array_merge($type, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
```

### 8.6 Update DatabaseSeeder

Edit `database/seeders/DatabaseSeeder.php`:

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            EmployeeTypeSeeder::class,
            DepartmentSeeder::class,
            PositionSeeder::class,
            ShiftSeeder::class,
            LeaveTypeSeeder::class,
        ]);
    }
}
```

---

## Step 9: Run Seeders and Verify

### 9.1 Run All Seeders

```bash
php artisan db:seed
```

### 9.2 Verify Data in Database

```bash
php artisan tinker
```

In Tinker, run:

```php
// Check employee types
DB::table('employee_types')->count();
DB::table('employee_types')->get();

// Check departments
DB::table('departments')->count();
DB::table('departments')->get();

// Check positions
DB::table('positions')->count();

// Check shifts
DB::table('shifts')->count();

// Check leave types
DB::table('leave_types')->count();

exit
```

---

## Step 10: Database Performance Optimization

### 10.1 Create Additional Indexes

```bash
php artisan make:migration add_performance_indexes
```

Edit the migration:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            $table->index(['employment_status', 'created_at']);
            $table->index(['full_name']);
        });

        Schema::table('employee_shifts', function (Blueprint $table) {
            $table->index(['status', 'date', 'department_id']);
        });

        Schema::table('leave_requests', function (Blueprint $table) {
            $table->index(['status', 'start_date']);
        });
    }

    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            $table->dropIndex(['employment_status', 'created_at']);
            $table->dropIndex(['full_name']);
        });

        Schema::table('employee_shifts', function (Blueprint $table) {
            $table->dropIndex(['status', 'date', 'department_id']);
        });

        Schema::table('leave_requests', function (Blueprint $table) {
            $table->dropIndex(['status', 'start_date']);
        });
    }
};
```

Run the migration:

```bash
php artisan migrate
```

---

## Troubleshooting Common Issues

### Issue 1: Foreign Key Constraint Fails
**Solution:**
- Ensure parent tables are created before child tables
- Check that referenced IDs exist in parent tables
- Verify foreign key column types match primary key types

### Issue 2: Migration Rollback Issues
**Solution:**
- Always define proper `down()` methods
- Drop foreign keys before dropping tables
- Use `Schema::dropIfExists()` for safety

### Issue 3: Seeder Data Conflicts
**Solution:**
- Use `DB::table()->truncate()` before seeding if needed
- Handle unique constraint violations
- Use `updateOrCreate()` for idempotent seeding

---

## Chapter Summary

In this chapter, you've successfully:

✅ Designed comprehensive database schema for hospital employee management
✅ Created all necessary Laravel migrations
✅ Implemented proper relationships and constraints
✅ Added performance indexes
✅ Created comprehensive database seeders
✅ Verified database structure and data

### Database Tables Created
- `employee_types` - Types of hospital employees
- `departments` - Hospital departments and units
- `positions` - Job positions and levels
- `employees` - Main employee records
- `employee_licenses` - Professional licenses and certifications
- `shifts` - Shift patterns and schedules
- `employee_shifts` - Employee shift assignments
- `leave_types` - Types of leave available
- `leave_requests` - Employee leave requests

### What's Next?
In Chapter 3, we'll create Eloquent models for all these database entities and implement the complex relationships between them, including self-referencing relationships and polymorphic associations.

### Key Commands to Remember
```bash
# Create migration
php artisan make:migration create_table_name

# Run migrations
php artisan migrate

# Create seeder
php artisan make:seeder SeederName

# Run seeders
php artisan db:seed

# Check migration status
php artisan migrate:status

# Access database
php artisan tinker
```

---

## Additional Resources

- [Laravel Migrations Documentation](https://laravel.com/docs/12.x/migrations)
- [Database Seeding Documentation](https://laravel.com/docs/12.x/seeding)
- [Schema Builder Documentation](https://laravel.com/docs/12.x/migrations#creating-tables)
- [Foreign Key Constraints](https://laravel.com/docs/12.x/migrations#foreign-key-constraints)

Ready for Chapter 3? Let's create the Eloquent models and implement the relationships!
```
