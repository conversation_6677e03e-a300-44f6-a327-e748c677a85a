# Chapter 6: Basic Booking System

Welcome to Chapter 6! In this chapter, we'll build a comprehensive booking system that allows customers to book car wash services with date/time selection, conflict detection, and booking management.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a Booking model with proper relationships
- Build a booking system with date/time selection
- Implement booking validation and conflict detection
- Create booking status management
- Build booking confirmation and management views
- Add booking history and tracking
- Implement booking cancellation and rescheduling

## 📋 What We'll Cover

1. Creating the Booking model and relationships
2. Building the booking controller with validation
3. Creating booking views and forms
4. Implementing date/time selection with availability
5. Adding booking status management
6. Creating booking confirmation system
7. Building booking history and management
8. Testing the booking system

## 🛠 Step 1: Enhancing the Booking Model

Let's enhance our existing Booking model. Edit `app/Models/Booking.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Carbon\Carbon;

class Booking extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'customer_id',
        'user_id', // The user who created the booking (could be staff)
        'booking_number',
        'booking_date',
        'booking_time',
        'status',
        'total_amount',
        'payment_status',
        'payment_method',
        'notes',
        'special_instructions',
        'vehicle_info',
        'estimated_duration',
        'actual_start_time',
        'actual_end_time',
        'staff_notes',
        'customer_rating',
        'customer_feedback',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
    ];

    protected $casts = [
        'booking_date' => 'date',
        'booking_time' => 'datetime',
        'total_amount' => 'decimal:2',
        'estimated_duration' => 'integer',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'customer_rating' => 'integer',
        'cancelled_at' => 'datetime',
        'vehicle_info' => 'array',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    // Payment status constants
    const PAYMENT_PENDING = 'pending';
    const PAYMENT_PAID = 'paid';
    const PAYMENT_PARTIAL = 'partial';
    const PAYMENT_REFUNDED = 'refunded';

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'booking_services')
                    ->withPivot(['quantity', 'price', 'notes'])
                    ->withTimestamps();
    }

    // Accessors
    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->total_amount, 2);
    }

    public function getStatusBadgeAttribute(): string
    {
        $badges = [
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_CONFIRMED => 'bg-blue-100 text-blue-800',
            self::STATUS_IN_PROGRESS => 'bg-purple-100 text-purple-800',
            self::STATUS_COMPLETED => 'bg-green-100 text-green-800',
            self::STATUS_CANCELLED => 'bg-red-100 text-red-800',
            self::STATUS_NO_SHOW => 'bg-gray-100 text-gray-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getPaymentStatusBadgeAttribute(): string
    {
        $badges = [
            self::PAYMENT_PENDING => 'bg-yellow-100 text-yellow-800',
            self::PAYMENT_PAID => 'bg-green-100 text-green-800',
            self::PAYMENT_PARTIAL => 'bg-orange-100 text-orange-800',
            self::PAYMENT_REFUNDED => 'bg-red-100 text-red-800',
        ];

        return $badges[$this->payment_status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getFormattedBookingTimeAttribute(): string
    {
        return $this->booking_time->format('g:i A');
    }

    public function getFormattedBookingDateAttribute(): string
    {
        return $this->booking_date->format('M j, Y');
    }

    public function getEstimatedEndTimeAttribute(): Carbon
    {
        return $this->booking_time->addMinutes($this->estimated_duration);
    }

    // Scopes
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('booking_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('booking_date', '>=', today())
                    ->where('status', '!=', self::STATUS_CANCELLED);
    }

    public function scopePast($query)
    {
        return $query->where('booking_date', '<', today())
                    ->orWhere(function ($q) {
                        $q->whereDate('booking_date', today())
                          ->whereTime('booking_time', '<', now()->format('H:i:s'));
                    });
    }

    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('booking_number', 'like', "%{$search}%")
              ->orWhereHas('customer', function ($customerQuery) use ($search) {
                  $customerQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
              });
        });
    }

    // Helper methods
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]) &&
               $this->booking_time->isFuture();
    }

    public function canBeRescheduled(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]) &&
               $this->booking_time->isFuture();
    }

    public function canBeStarted(): bool
    {
        return $this->status === self::STATUS_CONFIRMED &&
               $this->booking_date->isToday();
    }

    public function canBeCompleted(): bool
    {
        return $this->status === self::STATUS_IN_PROGRESS;
    }

    public function isOverdue(): bool
    {
        return $this->booking_time->isPast() &&
               in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]);
    }

    public function generateBookingNumber(): string
    {
        $date = $this->booking_date->format('Ymd');
        $lastBooking = static::withTrashed()
            ->where('booking_number', 'like', "BK-{$date}-%")
            ->orderBy('booking_number', 'desc')
            ->first();

        if ($lastBooking) {
            $lastNumber = (int) substr($lastBooking->booking_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('BK-%s-%03d', $date, $newNumber);
    }

    public function calculateTotalAmount(): void
    {
        $total = $this->services->sum(function ($service) {
            return $service->pivot->price * $service->pivot->quantity;
        });

        $this->update(['total_amount' => $total]);
    }

    public function calculateEstimatedDuration(): void
    {
        $duration = $this->services->sum(function ($service) {
            return $service->duration_minutes * $service->pivot->quantity;
        });

        $this->update(['estimated_duration' => $duration]);
    }

    // Status management methods
    public function confirm(): bool
    {
        if ($this->status === self::STATUS_PENDING) {
            return $this->update(['status' => self::STATUS_CONFIRMED]);
        }
        return false;
    }

    public function start(): bool
    {
        if ($this->canBeStarted()) {
            return $this->update([
                'status' => self::STATUS_IN_PROGRESS,
                'actual_start_time' => now(),
            ]);
        }
        return false;
    }

    public function complete(): bool
    {
        if ($this->canBeCompleted()) {
            return $this->update([
                'status' => self::STATUS_COMPLETED,
                'actual_end_time' => now(),
            ]);
        }
        return false;
    }

    public function cancel($reason = null, $cancelledBy = null): bool
    {
        if ($this->canBeCancelled()) {
            return $this->update([
                'status' => self::STATUS_CANCELLED,
                'cancelled_at' => now(),
                'cancelled_by' => $cancelledBy,
                'cancellation_reason' => $reason,
            ]);
        }
        return false;
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (!$booking->booking_number) {
                $booking->booking_number = $booking->generateBookingNumber();
            }
        });

        static::created(function ($booking) {
            $booking->calculateTotalAmount();
            $booking->calculateEstimatedDuration();
        });
    }
}
```

## 🛠 Step 2: Creating Booking Migration Updates

Let's create a migration to add the new fields to our bookings table:

```bash
# Create migration to add new booking fields
php artisan make:migration add_advanced_fields_to_bookings_table --table=bookings
```

Edit the migration file:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('user_id')->nullable()->after('customer_id')->constrained()->onDelete('set null');
            $table->string('booking_number')->unique()->after('user_id');
            $table->time('booking_time')->after('booking_date');
            $table->enum('status', ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])
                  ->default('pending')->after('booking_time');
            $table->enum('payment_status', ['pending', 'paid', 'partial', 'refunded'])
                  ->default('pending')->after('total_amount');
            $table->string('payment_method')->nullable()->after('payment_status');
            $table->text('notes')->nullable()->after('payment_method');
            $table->text('special_instructions')->nullable()->after('notes');
            $table->json('vehicle_info')->nullable()->after('special_instructions');
            $table->integer('estimated_duration')->nullable()->after('vehicle_info'); // in minutes
            $table->timestamp('actual_start_time')->nullable()->after('estimated_duration');
            $table->timestamp('actual_end_time')->nullable()->after('actual_start_time');
            $table->text('staff_notes')->nullable()->after('actual_end_time');
            $table->integer('customer_rating')->nullable()->after('staff_notes');
            $table->text('customer_feedback')->nullable()->after('customer_rating');
            $table->timestamp('cancelled_at')->nullable()->after('customer_feedback');
            $table->string('cancelled_by')->nullable()->after('cancelled_at');
            $table->text('cancellation_reason')->nullable()->after('cancelled_by');
            $table->softDeletes()->after('updated_at');
            
            // Add indexes
            $table->index('booking_number');
            $table->index(['booking_date', 'booking_time']);
            $table->index('status');
            $table->index('payment_status');
        });
    }

    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropIndex(['booking_number']);
            $table->dropIndex(['booking_date', 'booking_time']);
            $table->dropIndex(['status']);
            $table->dropIndex(['payment_status']);
            $table->dropColumn([
                'user_id',
                'booking_number',
                'booking_time',
                'status',
                'payment_status',
                'payment_method',
                'notes',
                'special_instructions',
                'vehicle_info',
                'estimated_duration',
                'actual_start_time',
                'actual_end_time',
                'staff_notes',
                'customer_rating',
                'customer_feedback',
                'cancelled_at',
                'cancelled_by',
                'cancellation_reason',
            ]);
        });
    }
};
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating the Booking Controller

Let's create a comprehensive booking controller:

```bash
# Create booking controller
php artisan make:controller BookingController --resource
```

Edit `app/Http/Controllers/BookingController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Service;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $query = Booking::with(['customer', 'services']);

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->status($request->status);
        }

        // Date filter
        if ($request->filled('date_from')) {
            $query->where('booking_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('booking_date', '<=', $request->date_to);
        }

        // Sort functionality
        $sortField = $request->get('sort', 'booking_date');
        $sortDirection = $request->get('direction', 'desc');

        $allowedSorts = ['booking_date', 'booking_time', 'total_amount', 'created_at'];
        if (in_array($sortField, $allowedSorts)) {
            if ($sortField === 'booking_date') {
                $query->orderBy('booking_date', $sortDirection)
                      ->orderBy('booking_time', $sortDirection);
            } else {
                $query->orderBy($sortField, $sortDirection);
            }
        }

        $bookings = $query->paginate(15)->withQueryString();

        return view('bookings.index', compact('bookings'));
    }

    public function create(Request $request)
    {
        $customers = Customer::active()->orderBy('first_name')->get();
        $services = Service::active()->with('serviceCategory')->get();

        // If customer_id is provided, pre-select the customer
        $selectedCustomer = null;
        if ($request->filled('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
        }

        return view('bookings.create', compact('customers', 'services', 'selectedCustomer'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'booking_date' => 'required|date|after_or_equal:today',
            'booking_time' => 'required|date_format:H:i',
            'services' => 'required|array|min:1',
            'services.*' => 'exists:services,id',
            'quantities' => 'required|array',
            'quantities.*' => 'integer|min:1|max:10',
            'notes' => 'nullable|string|max:1000',
            'special_instructions' => 'nullable|string|max:1000',
            'vehicle_info' => 'nullable|array',
            'vehicle_info.make' => 'nullable|string|max:100',
            'vehicle_info.model' => 'nullable|string|max:100',
            'vehicle_info.year' => 'nullable|integer|min:1900|max:' . (date('Y') + 1),
            'vehicle_info.color' => 'nullable|string|max:50',
            'vehicle_info.license_plate' => 'nullable|string|max:20',
        ]);

        // Create the booking
        $booking = Booking::create([
            'customer_id' => $validated['customer_id'],
            'user_id' => auth()->id(),
            'booking_date' => $validated['booking_date'],
            'booking_time' => Carbon::createFromFormat('Y-m-d H:i', $validated['booking_date'] . ' ' . $validated['booking_time']),
            'notes' => $validated['notes'],
            'special_instructions' => $validated['special_instructions'],
            'vehicle_info' => $validated['vehicle_info'] ?? null,
        ]);

        // Attach services with quantities and current prices
        foreach ($validated['services'] as $index => $serviceId) {
            $service = Service::find($serviceId);
            $quantity = $validated['quantities'][$index] ?? 1;

            $booking->services()->attach($serviceId, [
                'quantity' => $quantity,
                'price' => $service->price,
                'notes' => null,
            ]);
        }

        // Recalculate totals
        $booking->calculateTotalAmount();
        $booking->calculateEstimatedDuration();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking created successfully!');
    }

    public function show(Booking $booking)
    {
        $booking->load(['customer', 'services.serviceCategory', 'user']);
        return view('bookings.show', compact('booking'));
    }

    public function edit(Booking $booking)
    {
        if (!$booking->canBeRescheduled()) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'This booking cannot be edited.');
        }

        $customers = Customer::active()->orderBy('first_name')->get();
        $services = Service::active()->with('serviceCategory')->get();

        return view('bookings.edit', compact('booking', 'customers', 'services'));
    }

    public function update(Request $request, Booking $booking)
    {
        if (!$booking->canBeRescheduled()) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'This booking cannot be updated.');
        }

        $validated = $request->validate([
            'booking_date' => 'required|date|after_or_equal:today',
            'booking_time' => 'required|date_format:H:i',
            'services' => 'required|array|min:1',
            'services.*' => 'exists:services,id',
            'quantities' => 'required|array',
            'quantities.*' => 'integer|min:1|max:10',
            'notes' => 'nullable|string|max:1000',
            'special_instructions' => 'nullable|string|max:1000',
            'vehicle_info' => 'nullable|array',
        ]);

        // Update booking details
        $booking->update([
            'booking_date' => $validated['booking_date'],
            'booking_time' => Carbon::createFromFormat('Y-m-d H:i', $validated['booking_date'] . ' ' . $validated['booking_time']),
            'notes' => $validated['notes'],
            'special_instructions' => $validated['special_instructions'],
            'vehicle_info' => $validated['vehicle_info'] ?? null,
        ]);

        // Update services
        $booking->services()->detach();
        foreach ($validated['services'] as $index => $serviceId) {
            $service = Service::find($serviceId);
            $quantity = $validated['quantities'][$index] ?? 1;

            $booking->services()->attach($serviceId, [
                'quantity' => $quantity,
                'price' => $service->price,
                'notes' => null,
            ]);
        }

        // Recalculate totals
        $booking->calculateTotalAmount();
        $booking->calculateEstimatedDuration();

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Booking updated successfully!');
    }

    public function destroy(Booking $booking)
    {
        if (!$booking->canBeCancelled()) {
            return redirect()->route('bookings.show', $booking)
                ->with('error', 'This booking cannot be cancelled.');
        }

        $booking->cancel('Cancelled by staff', auth()->user()->name);

        return redirect()->route('bookings.index')
            ->with('success', 'Booking cancelled successfully!');
    }

    // Additional booking management methods
    public function confirm(Booking $booking)
    {
        if ($booking->confirm()) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking confirmed successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to confirm this booking.');
    }

    public function start(Booking $booking)
    {
        if ($booking->start()) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking started successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to start this booking.');
    }

    public function complete(Booking $booking)
    {
        if ($booking->complete()) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking completed successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to complete this booking.');
    }

    public function cancel(Request $request, Booking $booking)
    {
        $validated = $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        if ($booking->cancel($validated['cancellation_reason'], auth()->user()->name)) {
            return redirect()->route('bookings.show', $booking)
                ->with('success', 'Booking cancelled successfully!');
        }

        return redirect()->route('bookings.show', $booking)
            ->with('error', 'Unable to cancel this booking.');
    }
}
```

## 🛠 Step 4: Adding Booking Routes

Add the booking routes to `routes/web.php`:

```php
// Booking management routes - accessible by staff and admin
Route::middleware(['auth', 'role:staff,admin'])->group(function () {
    Route::resource('bookings', BookingController::class);
    Route::post('bookings/{booking}/confirm', [BookingController::class, 'confirm'])->name('bookings.confirm');
    Route::post('bookings/{booking}/start', [BookingController::class, 'start'])->name('bookings.start');
    Route::post('bookings/{booking}/complete', [BookingController::class, 'complete'])->name('bookings.complete');
    Route::post('bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
});

// Customer booking routes - accessible by customers
Route::middleware(['auth', 'role:customer,staff,admin'])->group(function () {
    Route::get('/my-bookings', [BookingController::class, 'myBookings'])->name('bookings.my');
    Route::get('/book-service', [BookingController::class, 'bookService'])->name('bookings.book');
    Route::post('/book-service', [BookingController::class, 'storeCustomerBooking'])->name('bookings.store-customer');
});
```

## 🛠 Step 5: Creating Booking Views

Now let's create all the necessary views for our booking system.

### 5.1 Booking Index View (Admin/Staff)

Create `resources/views/bookings/index.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Booking Management
            </h2>
            <a href="{{ route('bookings.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                New Booking
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form method="GET" action="{{ route('bookings.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" value="{{ request('search') }}"
                                   placeholder="Customer name, phone, email..."
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="">All Status</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date From</label>
                            <input type="date" name="date_from" value="{{ request('date_from') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bookings Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    @if($bookings->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Booking ID
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Customer
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Services
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Total
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($bookings as $booking)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                #{{ $booking->booking_number }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $booking->customer->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $booking->customer->phone }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $booking->booking_date->format('M d, Y') }}</div>
                                                <div class="text-sm text-gray-500">{{ $booking->booking_time->format('H:i') }}</div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-900">
                                                    @foreach($booking->services as $service)
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1 mb-1">
                                                            {{ $service->name }}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ${{ number_format($booking->total_amount, 2) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                                    @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                                    @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                                    @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                                    @elseif($booking->status == 'cancelled') bg-red-100 text-red-800
                                                    @endif">
                                                    {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('bookings.show', $booking) }}"
                                                       class="text-indigo-600 hover:text-indigo-900">View</a>
                                                    <a href="{{ route('bookings.edit', $booking) }}"
                                                       class="text-blue-600 hover:text-blue-900">Edit</a>

                                                    @if($booking->status == 'pending')
                                                        <form action="{{ route('bookings.confirm', $booking) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit" class="text-green-600 hover:text-green-900">Confirm</button>
                                                        </form>
                                                    @elseif($booking->status == 'confirmed')
                                                        <form action="{{ route('bookings.start', $booking) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit" class="text-purple-600 hover:text-purple-900">Start</button>
                                                        </form>
                                                    @elseif($booking->status == 'in_progress')
                                                        <form action="{{ route('bookings.complete', $booking) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit" class="text-green-600 hover:text-green-900">Complete</button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $bookings->links() }}
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings found</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating a new booking.</p>
                            <div class="mt-6">
                                <a href="{{ route('bookings.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                    New Booking
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 5.2 Booking Create View (Admin/Staff)

Create `resources/views/bookings/create.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Create New Booking
            </h2>
            <a href="{{ route('bookings.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Bookings
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form action="{{ route('bookings.store') }}" method="POST" id="booking-form">
                        @csrf

                        <!-- Customer Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->phone }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="vehicle_type" class="block text-sm font-medium text-gray-700">Vehicle Type</label>
                                    <select name="vehicle_type" id="vehicle_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select Vehicle Type</option>
                                        <option value="sedan" {{ old('vehicle_type') == 'sedan' ? 'selected' : '' }}>Sedan</option>
                                        <option value="suv" {{ old('vehicle_type') == 'suv' ? 'selected' : '' }}>SUV</option>
                                        <option value="truck" {{ old('vehicle_type') == 'truck' ? 'selected' : '' }}>Truck</option>
                                        <option value="motorcycle" {{ old('vehicle_type') == 'motorcycle' ? 'selected' : '' }}>Motorcycle</option>
                                    </select>
                                    @error('vehicle_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Date and Time Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Schedule</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="booking_date" class="block text-sm font-medium text-gray-700">Booking Date</label>
                                    <input type="date" name="booking_date" id="booking_date" required
                                           value="{{ old('booking_date') }}"
                                           min="{{ date('Y-m-d') }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('booking_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="booking_time" class="block text-sm font-medium text-gray-700">Booking Time</label>
                                    <select name="booking_time" id="booking_time" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select Time</option>
                                        @for($hour = 8; $hour <= 18; $hour++)
                                            @for($minute = 0; $minute < 60; $minute += 30)
                                                @php
                                                    $time = sprintf('%02d:%02d', $hour, $minute);
                                                @endphp
                                                <option value="{{ $time }}" {{ old('booking_time') == $time ? 'selected' : '' }}>
                                                    {{ $time }}
                                                </option>
                                            @endfor
                                        @endfor
                                    </select>
                                    @error('booking_time')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Service Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Services</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($services as $service)
                                    <div class="border rounded-lg p-4 hover:bg-gray-50">
                                        <label class="flex items-start space-x-3 cursor-pointer">
                                            <input type="checkbox" name="services[]" value="{{ $service->id }}"
                                                   data-price="{{ $service->price }}"
                                                   class="service-checkbox mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                                   {{ in_array($service->id, old('services', [])) ? 'checked' : '' }}>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-gray-900">{{ $service->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $service->description }}</div>
                                                <div class="text-sm font-medium text-green-600">${{ number_format($service->price, 2) }}</div>
                                                <div class="text-xs text-gray-400">Duration: {{ $service->duration }} minutes</div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('services')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Special Instructions -->
                        <div class="mb-6">
                            <label for="special_instructions" class="block text-sm font-medium text-gray-700">Special Instructions</label>
                            <textarea name="special_instructions" id="special_instructions" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                      placeholder="Any special requests or instructions...">{{ old('special_instructions') }}</textarea>
                            @error('special_instructions')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Total Amount Display -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-medium text-gray-900">Total Amount:</span>
                                <span id="total-amount" class="text-2xl font-bold text-green-600">$0.00</span>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3">
                            <a href="{{ route('bookings.index') }}"
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Booking
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for dynamic total calculation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const serviceCheckboxes = document.querySelectorAll('.service-checkbox');
            const totalAmountElement = document.getElementById('total-amount');

            function updateTotal() {
                let total = 0;
                serviceCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        total += parseFloat(checkbox.dataset.price);
                    }
                });
                totalAmountElement.textContent = '$' + total.toFixed(2);
            }

            serviceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateTotal);
            });

            // Initial calculation
            updateTotal();
        });
    </script>
</x-app-layout>
```

### 5.3 Booking Show View (Detail View)

Create `resources/views/bookings/show.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Booking Details - #{{ $booking->booking_number }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('bookings.edit', $booking) }}"
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit Booking
                </a>
                <a href="{{ route('bookings.index') }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Bookings
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Booking Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Information</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Booking Number</label>
                                    <p class="mt-1 text-sm text-gray-900">#{{ $booking->booking_number }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                        @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                        @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                        @elseif($booking->status == 'cancelled') bg-red-100 text-red-800
                                        @endif">
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Booking Date</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->booking_date->format('l, F j, Y') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Booking Time</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->booking_time->format('g:i A') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Vehicle Type</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ ucfirst($booking->vehicle_type) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Total Amount</label>
                                    <p class="mt-1 text-lg font-semibold text-green-600">${{ number_format($booking->total_amount, 2) }}</p>
                                </div>
                            </div>

                            @if($booking->special_instructions)
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-500">Special Instructions</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->special_instructions }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->customer->name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Phone</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->customer->phone }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->customer->email }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Address</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $booking->customer->address ?? 'Not provided' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Services -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Selected Services</h3>

                            <div class="space-y-4">
                                @foreach($booking->services as $service)
                                    <div class="flex justify-between items-center p-4 border rounded-lg">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900">{{ $service->name }}</h4>
                                            <p class="text-sm text-gray-500">{{ $service->description }}</p>
                                            <p class="text-xs text-gray-400">Duration: {{ $service->duration }} minutes</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-gray-900">${{ number_format($service->price, 2) }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-4 pt-4 border-t">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg font-medium text-gray-900">Total:</span>
                                    <span class="text-xl font-bold text-green-600">${{ number_format($booking->total_amount, 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>

                            <div class="space-y-3">
                                @if($booking->status == 'pending')
                                    <form action="{{ route('bookings.confirm', $booking) }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                                class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                            Confirm Booking
                                        </button>
                                    </form>
                                @elseif($booking->status == 'confirmed')
                                    <form action="{{ route('bookings.start', $booking) }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                                class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                            Start Service
                                        </button>
                                    </form>
                                @elseif($booking->status == 'in_progress')
                                    <form action="{{ route('bookings.complete', $booking) }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                                class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                            Complete Service
                                        </button>
                                    </form>
                                @endif

                                @if(in_array($booking->status, ['pending', 'confirmed']))
                                    <form action="{{ route('bookings.cancel', $booking) }}" method="POST"
                                          onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                                        @csrf
                                        <button type="submit"
                                                class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                            Cancel Booking
                                        </button>
                                    </form>
                                @endif

                                <a href="{{ route('bookings.edit', $booking) }}"
                                   class="block w-full text-center bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    Edit Booking
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Timeline -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                        <div class="p-6 bg-white border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Timeline</h3>

                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-900">Booking Created</p>
                                        <p class="text-xs text-gray-500">{{ $booking->created_at->format('M j, Y g:i A') }}</p>
                                    </div>
                                </div>

                                @if($booking->confirmed_at)
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full"></div>
                                        <div class="ml-3">
                                            <p class="text-sm text-gray-900">Booking Confirmed</p>
                                            <p class="text-xs text-gray-500">{{ $booking->confirmed_at->format('M j, Y g:i A') }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if($booking->started_at)
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <div class="ml-3">
                                            <p class="text-sm text-gray-900">Service Started</p>
                                            <p class="text-xs text-gray-500">{{ $booking->started_at->format('M j, Y g:i A') }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if($booking->completed_at)
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full"></div>
                                        <div class="ml-3">
                                            <p class="text-sm text-gray-900">Service Completed</p>
                                            <p class="text-xs text-gray-500">{{ $booking->completed_at->format('M j, Y g:i A') }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if($booking->cancelled_at)
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full"></div>
                                        <div class="ml-3">
                                            <p class="text-sm text-gray-900">Booking Cancelled</p>
                                            <p class="text-xs text-gray-500">{{ $booking->cancelled_at->format('M j, Y g:i A') }}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 5.4 Booking Edit View

Create `resources/views/bookings/edit.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Edit Booking - #{{ $booking->booking_number }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('bookings.show', $booking) }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Details
                </a>
                <a href="{{ route('bookings.index') }}"
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Bookings
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form action="{{ route('bookings.update', $booking) }}" method="POST" id="booking-form">
                        @csrf
                        @method('PUT')

                        <!-- Current Status Display -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Current Status:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                    @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                    @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                    @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                    @elseif($booking->status == 'cancelled') bg-red-100 text-red-800
                                    @endif">
                                    {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                </span>
                            </div>
                        </div>

                        <!-- Customer Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}"
                                                    {{ $booking->customer_id == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->phone }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="vehicle_type" class="block text-sm font-medium text-gray-700">Vehicle Type</label>
                                    <select name="vehicle_type" id="vehicle_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="sedan" {{ $booking->vehicle_type == 'sedan' ? 'selected' : '' }}>Sedan</option>
                                        <option value="suv" {{ $booking->vehicle_type == 'suv' ? 'selected' : '' }}>SUV</option>
                                        <option value="truck" {{ $booking->vehicle_type == 'truck' ? 'selected' : '' }}>Truck</option>
                                        <option value="motorcycle" {{ $booking->vehicle_type == 'motorcycle' ? 'selected' : '' }}>Motorcycle</option>
                                    </select>
                                    @error('vehicle_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Date and Time Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Schedule</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="booking_date" class="block text-sm font-medium text-gray-700">Booking Date</label>
                                    <input type="date" name="booking_date" id="booking_date" required
                                           value="{{ $booking->booking_date->format('Y-m-d') }}"
                                           min="{{ date('Y-m-d') }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('booking_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="booking_time" class="block text-sm font-medium text-gray-700">Booking Time</label>
                                    <select name="booking_time" id="booking_time" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        @for($hour = 8; $hour <= 18; $hour++)
                                            @for($minute = 0; $minute < 60; $minute += 30)
                                                @php
                                                    $time = sprintf('%02d:%02d', $hour, $minute);
                                                @endphp
                                                <option value="{{ $time }}"
                                                        {{ $booking->booking_time->format('H:i') == $time ? 'selected' : '' }}>
                                                    {{ $time }}
                                                </option>
                                            @endfor
                                        @endfor
                                    </select>
                                    @error('booking_time')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Service Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Services</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($services as $service)
                                    <div class="border rounded-lg p-4 hover:bg-gray-50">
                                        <label class="flex items-start space-x-3 cursor-pointer">
                                            <input type="checkbox" name="services[]" value="{{ $service->id }}"
                                                   data-price="{{ $service->price }}"
                                                   class="service-checkbox mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                                   {{ $booking->services->contains($service->id) ? 'checked' : '' }}>
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-gray-900">{{ $service->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $service->description }}</div>
                                                <div class="text-sm font-medium text-green-600">${{ number_format($service->price, 2) }}</div>
                                                <div class="text-xs text-gray-400">Duration: {{ $service->duration }} minutes</div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('services')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Special Instructions -->
                        <div class="mb-6">
                            <label for="special_instructions" class="block text-sm font-medium text-gray-700">Special Instructions</label>
                            <textarea name="special_instructions" id="special_instructions" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                      placeholder="Any special requests or instructions...">{{ $booking->special_instructions }}</textarea>
                            @error('special_instructions')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Total Amount Display -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-medium text-gray-900">Total Amount:</span>
                                <span id="total-amount" class="text-2xl font-bold text-green-600">${{ number_format($booking->total_amount, 2) }}</span>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3">
                            <a href="{{ route('bookings.show', $booking) }}"
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Booking
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for dynamic total calculation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const serviceCheckboxes = document.querySelectorAll('.service-checkbox');
            const totalAmountElement = document.getElementById('total-amount');

            function updateTotal() {
                let total = 0;
                serviceCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        total += parseFloat(checkbox.dataset.price);
                    }
                });
                totalAmountElement.textContent = '$' + total.toFixed(2);
            }

            serviceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateTotal);
            });

            // Initial calculation
            updateTotal();
        });
    </script>
</x-app-layout>
```

### 5.5 Customer Booking Interface

Create `resources/views/bookings/book-service.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Book a Service
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Schedule Your Car Wash Service</h3>
                        <p class="mt-1 text-sm text-gray-600">Choose your preferred services, date, and time for your car wash appointment.</p>
                    </div>

                    <form action="{{ route('bookings.store-customer') }}" method="POST" id="customer-booking-form">
                        @csrf

                        <!-- Vehicle Information -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Vehicle Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="vehicle_type" class="block text-sm font-medium text-gray-700">Vehicle Type</label>
                                    <select name="vehicle_type" id="vehicle_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select Vehicle Type</option>
                                        <option value="sedan" {{ old('vehicle_type') == 'sedan' ? 'selected' : '' }}>Sedan</option>
                                        <option value="suv" {{ old('vehicle_type') == 'suv' ? 'selected' : '' }}>SUV</option>
                                        <option value="truck" {{ old('vehicle_type') == 'truck' ? 'selected' : '' }}>Truck</option>
                                        <option value="motorcycle" {{ old('vehicle_type') == 'motorcycle' ? 'selected' : '' }}>Motorcycle</option>
                                    </select>
                                    @error('vehicle_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="vehicle_model" class="block text-sm font-medium text-gray-700">Vehicle Model (Optional)</label>
                                    <input type="text" name="vehicle_model" id="vehicle_model"
                                           value="{{ old('vehicle_model') }}"
                                           placeholder="e.g., Toyota Camry, Honda CR-V"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>
                            </div>
                        </div>

                        <!-- Date and Time Selection -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Preferred Schedule</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="booking_date" class="block text-sm font-medium text-gray-700">Preferred Date</label>
                                    <input type="date" name="booking_date" id="booking_date" required
                                           value="{{ old('booking_date') }}"
                                           min="{{ date('Y-m-d') }}"
                                           max="{{ date('Y-m-d', strtotime('+30 days')) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('booking_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="booking_time" class="block text-sm font-medium text-gray-700">Preferred Time</label>
                                    <select name="booking_time" id="booking_time" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select Time</option>
                                        @for($hour = 8; $hour <= 18; $hour++)
                                            @for($minute = 0; $minute < 60; $minute += 30)
                                                @php
                                                    $time = sprintf('%02d:%02d', $hour, $minute);
                                                    $displayTime = date('g:i A', strtotime($time));
                                                @endphp
                                                <option value="{{ $time }}" {{ old('booking_time') == $time ? 'selected' : '' }}>
                                                    {{ $displayTime }}
                                                </option>
                                            @endfor
                                        @endfor
                                    </select>
                                    @error('booking_time')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Service Selection -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">Select Services</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($services as $service)
                                    <div class="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                        <label class="flex items-start space-x-3 cursor-pointer">
                                            <input type="checkbox" name="services[]" value="{{ $service->id }}"
                                                   data-price="{{ $service->price }}"
                                                   data-duration="{{ $service->duration }}"
                                                   class="service-checkbox mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                                   {{ in_array($service->id, old('services', [])) ? 'checked' : '' }}>
                                            <div class="flex-1">
                                                <div class="flex justify-between items-start">
                                                    <div>
                                                        <h5 class="text-sm font-medium text-gray-900">{{ $service->name }}</h5>
                                                        <p class="text-sm text-gray-500 mt-1">{{ $service->description }}</p>
                                                    </div>
                                                    <div class="text-right ml-4">
                                                        <div class="text-lg font-semibold text-green-600">${{ number_format($service->price, 2) }}</div>
                                                        <div class="text-xs text-gray-400">{{ $service->duration }} min</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('services')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Special Instructions -->
                        <div class="mb-6">
                            <label for="special_instructions" class="block text-sm font-medium text-gray-700">Special Instructions (Optional)</label>
                            <textarea name="special_instructions" id="special_instructions" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                      placeholder="Any special requests, areas of focus, or additional notes...">{{ old('special_instructions') }}</textarea>
                            @error('special_instructions')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Booking Summary -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">Booking Summary</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Selected Services:</span>
                                    <span id="selected-services" class="text-sm text-gray-900">None selected</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Estimated Duration:</span>
                                    <span id="total-duration" class="text-sm text-gray-900">0 minutes</span>
                                </div>
                                <div class="flex justify-between items-center pt-2 border-t">
                                    <span class="text-lg font-medium text-gray-900">Total Amount:</span>
                                    <span id="total-amount" class="text-2xl font-bold text-green-600">$0.00</span>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-3">
                            <a href="{{ route('dashboard') }}"
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded">
                                Book Service
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for dynamic calculations -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const serviceCheckboxes = document.querySelectorAll('.service-checkbox');
            const totalAmountElement = document.getElementById('total-amount');
            const totalDurationElement = document.getElementById('total-duration');
            const selectedServicesElement = document.getElementById('selected-services');

            function updateSummary() {
                let total = 0;
                let duration = 0;
                let selectedServices = [];

                serviceCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        total += parseFloat(checkbox.dataset.price);
                        duration += parseInt(checkbox.dataset.duration);
                        selectedServices.push(checkbox.parentElement.querySelector('h5').textContent);
                    }
                });

                totalAmountElement.textContent = '$' + total.toFixed(2);
                totalDurationElement.textContent = duration + ' minutes';
                selectedServicesElement.textContent = selectedServices.length > 0 ? selectedServices.join(', ') : 'None selected';
            }

            serviceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSummary);
            });

            // Initial calculation
            updateSummary();
        });
    </script>
</x-app-layout>
```

### 5.6 Customer Booking History (My Bookings)

Create `resources/views/bookings/my-bookings.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                My Bookings
            </h2>
            <a href="{{ route('bookings.book') }}"
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Book New Service
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filter Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form method="GET" action="{{ route('bookings.my') }}" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="">All Status</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date From</label>
                            <input type="date" name="date_from" value="{{ request('date_from') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            @if($bookings->count() > 0)
                <!-- Bookings Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($bookings as $booking)
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <!-- Booking Header -->
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">#{{ $booking->booking_number }}</h3>
                                        <p class="text-sm text-gray-500">{{ $booking->booking_date->format('M j, Y') }} at {{ $booking->booking_time->format('g:i A') }}</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($booking->status == 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($booking->status == 'confirmed') bg-blue-100 text-blue-800
                                        @elseif($booking->status == 'in_progress') bg-purple-100 text-purple-800
                                        @elseif($booking->status == 'completed') bg-green-100 text-green-800
                                        @elseif($booking->status == 'cancelled') bg-red-100 text-red-800
                                        @endif">
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                </div>

                                <!-- Vehicle Info -->
                                <div class="mb-4">
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Vehicle:</span> {{ ucfirst($booking->vehicle_type) }}
                                        @if($booking->vehicle_model)
                                            - {{ $booking->vehicle_model }}
                                        @endif
                                    </p>
                                </div>

                                <!-- Services -->
                                <div class="mb-4">
                                    <p class="text-sm font-medium text-gray-700 mb-2">Services:</p>
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($booking->services as $service)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $service->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>

                                <!-- Total Amount -->
                                <div class="mb-4">
                                    <p class="text-lg font-semibold text-green-600">${{ number_format($booking->total_amount, 2) }}</p>
                                </div>

                                <!-- Special Instructions -->
                                @if($booking->special_instructions)
                                    <div class="mb-4">
                                        <p class="text-sm text-gray-600">
                                            <span class="font-medium">Notes:</span> {{ Str::limit($booking->special_instructions, 50) }}
                                        </p>
                                    </div>
                                @endif

                                <!-- Actions -->
                                <div class="flex justify-between items-center pt-4 border-t">
                                    <div class="text-xs text-gray-500">
                                        Booked {{ $booking->created_at->diffForHumans() }}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{{ route('bookings.show', $booking) }}"
                                           class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                            View Details
                                        </a>
                                        @if(in_array($booking->status, ['pending', 'confirmed']))
                                            <form action="{{ route('bookings.cancel', $booking) }}" method="POST" class="inline"
                                                  onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                                                @csrf
                                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                    Cancel
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $bookings->links() }}
                </div>
            @else
                <!-- Empty State -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No bookings found</h3>
                        <p class="mt-1 text-sm text-gray-500">
                            @if(request()->hasAny(['status', 'date_from']))
                                No bookings match your current filters. Try adjusting your search criteria.
                            @else
                                You haven't made any bookings yet. Book your first car wash service today!
                            @endif
                        </p>
                        <div class="mt-6">
                            <a href="{{ route('bookings.book') }}"
                               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Book New Service
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
```

## 🧪 Testing the Booking System

Now that we have implemented all the booking views, let's test the complete booking system:

### 1. Admin/Staff Booking Management
- **Visit `/bookings`** - View all bookings with search and filtering
- **Visit `/bookings/create`** - Create new bookings for customers
- **Test booking workflow**:
  - Create a pending booking
  - Confirm the booking
  - Start the service
  - Complete the service
- **Test booking editing** - Modify existing bookings
- **Test booking details** - View comprehensive booking information

### 2. Customer Booking Interface
- **Visit `/book-service`** as a customer - Self-service booking
- **Test service selection** - Multiple services with dynamic pricing
- **Test date/time selection** - Available time slots
- **Test booking summary** - Real-time calculations
- **Visit `/my-bookings`** - View personal booking history

### 3. Booking Validation Testing
- **Date validation** - Cannot book past dates
- **Time slot validation** - Business hours only
- **Service selection** - At least one service required
- **Customer selection** - Valid customer required
- **Conflict detection** - Prevent double bookings

### 4. Status Workflow Testing
- **Pending → Confirmed** - Staff confirmation
- **Confirmed → In Progress** - Service start
- **In Progress → Completed** - Service completion
- **Cancellation** - From pending or confirmed status

### 5. User Interface Testing
- **Responsive design** - Test on mobile and desktop
- **Dynamic calculations** - Total amount updates
- **Search and filtering** - Booking management
- **Status indicators** - Color-coded status badges
- **Timeline display** - Booking progress tracking

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Enhanced the Booking model with advanced features
✅ Created comprehensive booking CRUD operations
✅ Built booking status management system
✅ Implemented booking validation and conflict detection
✅ Added booking confirmation and management
✅ Created booking history and tracking
✅ Built booking cancellation and rescheduling

### Booking Features Implemented:
- **Booking Management**: Complete CRUD with status tracking
- **Service Integration**: Multiple services per booking
- **Time Management**: Date/time selection with validation
- **Status Workflow**: Pending → Confirmed → In Progress → Completed
- **Customer Integration**: Link bookings to customer records
- **Payment Tracking**: Payment status and method tracking
- **Vehicle Information**: Customer vehicle details storage

## 🚀 What's Next?

In the next chapter, we'll:
- Create comprehensive dashboards for different user roles
- Build reporting and analytics features
- Add booking statistics and charts
- Implement revenue tracking
- Create performance metrics

---

**Ready for dashboards?** Let's move on to [Chapter 7: Dashboard and Basic Reporting](./07-dashboard-reporting.md)!
