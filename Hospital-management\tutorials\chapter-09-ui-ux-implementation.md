# Chapter 9: UI/UX Implementation and Dashboard

## Overview
In this chapter, we'll implement a comprehensive user interface and user experience design for the Hospital Employee Management System. We'll create a modern, responsive dashboard with Indonesian healthcare context, data visualization, and intuitive navigation.

## Learning Objectives
- Create responsive dashboard with data visualization
- Implement modern UI components with shadcn/ui
- Build navigation and layout systems
- Create data visualization charts and graphs
- Implement Indonesian localization throughout UI
- Build responsive design for mobile and desktop

## Prerequisites
- Completed Chapter 1-8
- Understanding of modern UI/UX principles
- Familiarity with React and TypeScript

## Duration
120-150 minutes

---

## Step 1: Create Dashboard Layout

### 1.1 Create Main Layout Component

Create `resources/js/layouts/DashboardLayout.tsx`:

```tsx
import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { 
  Menu,
  Home,
  Users,
  Building2,
  Calendar,
  Search,
  BarChart3,
  Settings,
  LogOut,
  Bell,
  User,
  Shield,
  Clock,
  FileText,
  ChevronDown,
  Activity
} from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { user, logout, hasPermission } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      current: location.pathname === '/dashboard',
      permission: 'view dashboard',
    },
    {
      name: 'Karyawan',
      href: '/employees',
      icon: Users,
      current: location.pathname.startsWith('/employees'),
      permission: 'view employees',
      badge: 'Baru',
    },
    {
      name: 'Departemen',
      href: '/departments',
      icon: Building2,
      current: location.pathname.startsWith('/departments'),
      permission: 'view departments',
    },
    {
      name: 'Jadwal Shift',
      href: '/shifts',
      icon: Calendar,
      current: location.pathname.startsWith('/shifts'),
      permission: 'view shifts',
    },
    {
      name: 'Pencarian',
      href: '/search',
      icon: Search,
      current: location.pathname.startsWith('/search'),
      permission: 'view employees',
    },
    {
      name: 'Laporan',
      href: '/reports',
      icon: BarChart3,
      current: location.pathname.startsWith('/reports'),
      permission: 'view reports',
    },
    {
      name: 'Pengaturan',
      href: '/settings',
      icon: Settings,
      current: location.pathname.startsWith('/settings'),
      permission: 'manage settings',
    },
  ];

  const filteredNavigation = navigation.filter(item => 
    hasPermission(item.permission)
  );

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const Sidebar = ({ mobile = false }) => (
    <div className={`flex flex-col h-full ${mobile ? 'w-full' : 'w-64'}`}>
      {/* Logo */}
      <div className="flex items-center px-6 py-4 border-b">
        <Activity className="h-8 w-8 text-blue-600" />
        <span className="ml-2 text-xl font-bold text-gray-900">
          Hospital EMS
        </span>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {filteredNavigation.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                item.current
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
              onClick={() => mobile && setSidebarOpen(false)}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.name}
              {item.badge && (
                <Badge variant="secondary" className="ml-auto text-xs">
                  {item.badge}
                </Badge>
              )}
            </Link>
          );
        })}
      </nav>

      {/* User info */}
      <div className="border-t p-4">
        <div className="flex items-center">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user?.profile_photo_url} />
            <AvatarFallback>
              {getInitials(user?.name || 'User')}
            </AvatarFallback>
          </Avatar>
          <div className="ml-3 flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user?.employee?.position?.title || 'Staff'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="p-0 w-64">
          <Sidebar mobile />
        </SheetContent>
      </Sheet>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 overflow-y-auto">
          <Sidebar />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top navigation */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                {/* Mobile menu button */}
                <Sheet>
                  <SheetTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="lg:hidden"
                      onClick={() => setSidebarOpen(true)}
                    >
                      <Menu className="h-6 w-6" />
                    </Button>
                  </SheetTrigger>
                </Sheet>

                {/* Breadcrumb or page title */}
                <div className="ml-4 lg:ml-0">
                  <h1 className="text-lg font-semibold text-gray-900">
                    {navigation.find(item => item.current)?.name || 'Dashboard'}
                  </h1>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Notifications */}
                <Button variant="ghost" size="sm" className="relative">
                  <Bell className="h-5 w-5" />
                  <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                    3
                  </span>
                </Button>

                {/* User menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user?.profile_photo_url} />
                        <AvatarFallback>
                          {getInitials(user?.name || 'User')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="hidden md:block text-sm font-medium">
                        {user?.name}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">{user?.name}</p>
                        <p className="text-xs text-gray-500">{user?.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link to="/profile" className="flex items-center">
                        <User className="mr-2 h-4 w-4" />
                        Profil Saya
                      </Link>
                    </DropdownMenuItem>
                    {hasPermission('manage settings') && (
                      <DropdownMenuItem asChild>
                        <Link to="/settings" className="flex items-center">
                          <Settings className="mr-2 h-4 w-4" />
                          Pengaturan
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      Keluar
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
```

---

## Step 2: Create Dashboard Components

### 2.1 Create Dashboard Overview

Create `resources/js/pages/Dashboard.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Building2, 
  Calendar, 
  TrendingUp, 
  Clock,
  UserCheck,
  UserX,
  Activity,
  AlertTriangle,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';

interface DashboardStats {
  employees: {
    total: number;
    active: number;
    inactive: number;
    new_this_month: number;
    medical_staff: number;
  };
  departments: {
    total: number;
    active: number;
    with_head: number;
    average_employees: number;
  };
  shifts: {
    today: number;
    this_week: number;
    completed_today: number;
    missed_today: number;
  };
  recent_activities: Array<{
    id: number;
    type: string;
    description: string;
    created_at: string;
    user: string;
  }>;
}

const Dashboard: React.FC = () => {
  const { user, hasPermission } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/dashboard/stats');
      setStats(response.data.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Gagal memuat data dashboard');
    } finally {
      setLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Selamat Pagi';
    if (hour < 15) return 'Selamat Siang';
    if (hour < 18) return 'Selamat Sore';
    return 'Selamat Malam';
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('id-ID').format(num);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'employee_created': return <UserCheck className="h-4 w-4 text-green-500" />;
      case 'employee_updated': return <Users className="h-4 w-4 text-blue-500" />;
      case 'shift_assigned': return <Calendar className="h-4 w-4 text-purple-500" />;
      case 'department_created': return <Building2 className="h-4 w-4 text-orange-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Terjadi Kesalahan</h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <Button onClick={fetchDashboardStats}>Coba Lagi</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          {getGreeting()}, {user?.name}!
        </h1>
        <p className="text-blue-100">
          Selamat datang di Sistem Manajemen Karyawan Rumah Sakit
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Employees */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Karyawan</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.employees.total || 0)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +{stats?.employees.new_this_month || 0} bulan ini
              </span>
            </p>
          </CardContent>
        </Card>

        {/* Active Employees */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Karyawan Aktif</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.employees.active || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.employees.inactive || 0} tidak aktif
            </p>
          </CardContent>
        </Card>

        {/* Departments */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departemen</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.departments.total || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.departments.with_head || 0} memiliki kepala departemen
            </p>
          </CardContent>
        </Card>

        {/* Today's Shifts */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shift Hari Ini</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats?.shifts.today || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.shifts.completed_today || 0} selesai, {stats?.shifts.missed_today || 0} tidak hadir
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Aksi Cepat</CardTitle>
            <CardDescription>
              Akses fitur yang sering digunakan
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {hasPermission('create employees') && (
              <Button asChild className="w-full justify-start">
                <Link to="/employees/create">
                  <Users className="mr-2 h-4 w-4" />
                  Tambah Karyawan Baru
                </Link>
              </Button>
            )}
            {hasPermission('view shifts') && (
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to="/shifts">
                  <Calendar className="mr-2 h-4 w-4" />
                  Lihat Jadwal Shift
                </Link>
              </Button>
            )}
            {hasPermission('view reports') && (
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to="/reports">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Buat Laporan
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Aktivitas Terbaru</CardTitle>
            <CardDescription>
              Aktivitas sistem dalam 24 jam terakhir
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.recent_activities?.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">
                      oleh {activity.user} • {new Date(activity.created_at).toLocaleString('id-ID')}
                    </p>
                  </div>
                </div>
              )) || (
                <p className="text-sm text-gray-500 text-center py-4">
                  Tidak ada aktivitas terbaru
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
```
