# Chapter 7: Medical Records and History

## Building Secure Medical Record Management

In this chapter, we'll create a comprehensive medical records management system that handles patient diagnoses, treatments, lab results, and medical history with secure access controls and search capabilities.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create medical record management system
- Build patient medical history tracking
- Implement diagnosis and treatment recording
- Design secure medical record interfaces
- Set up lab results integration
- Create medical record search and reporting

## 🏥 Medical Records Requirements

### Core Features

1. **Medical Records**: Comprehensive patient medical documentation
2. **Diagnosis Tracking**: ICD-10 diagnosis codes and descriptions
3. **Treatment Plans**: Treatment protocols and follow-up care
4. **Lab Results**: Laboratory test results and interpretations
5. **Medical History**: Chronological patient medical timeline
6. **Secure Access**: Role-based access to sensitive medical data

## 🛠 Backend Implementation

### Step 1: Medical Records Migration

```bash
php artisan make:migration create_medical_records_table
php artisan make:migration create_diagnoses_table
php artisan make:migration create_treatments_table
php artisan make:migration create_lab_results_table
```

**database/migrations/xxxx_create_medical_records_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('medical_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            
            // Record details
            $table->string('record_number')->unique();
            $table->date('visit_date');
            $table->enum('visit_type', ['consultation', 'follow_up', 'emergency', 'procedure', 'checkup']);
            
            // Chief complaint and examination
            $table->text('chief_complaint');
            $table->text('history_of_present_illness')->nullable();
            $table->text('physical_examination')->nullable();
            
            // Vital signs
            $table->json('vital_signs')->nullable(); // Blood pressure, temperature, etc.
            
            // Assessment and plan
            $table->text('assessment')->nullable();
            $table->text('treatment_plan')->nullable();
            $table->text('notes')->nullable();
            
            // Follow-up
            $table->date('next_visit_date')->nullable();
            $table->text('follow_up_instructions')->nullable();
            
            // Status
            $table->enum('status', ['draft', 'completed', 'reviewed', 'archived'])->default('draft');
            $table->timestamp('completed_at')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['patient_id', 'visit_date']);
            $table->index(['doctor_id', 'visit_date']);
            $table->index(['clinic_id', 'visit_date']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('medical_records');
    }
};
```

### Step 2: Medical Record Model

**app/Models/MedicalRecord.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicalRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id', 'doctor_id', 'appointment_id', 'clinic_id',
        'record_number', 'visit_date', 'visit_type', 'chief_complaint',
        'history_of_present_illness', 'physical_examination', 'vital_signs',
        'assessment', 'treatment_plan', 'notes', 'next_visit_date',
        'follow_up_instructions', 'status'
    ];

    protected function casts(): array
    {
        return [
            'visit_date' => 'date',
            'next_visit_date' => 'date',
            'vital_signs' => 'array',
            'completed_at' => 'datetime',
            'reviewed_at' => 'datetime',
        ];
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function diagnoses()
    {
        return $this->hasMany(Diagnosis::class);
    }

    public function treatments()
    {
        return $this->hasMany(Treatment::class);
    }

    public function labResults()
    {
        return $this->hasMany(LabResult::class);
    }

    public function prescriptions()
    {
        return $this->hasMany(Prescription::class);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    // Helper methods
    public function complete()
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }

    public function review($reviewerId)
    {
        $this->update([
            'status' => 'reviewed',
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now()
        ]);
    }

    // Boot method for auto-generating record number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($record) {
            if (empty($record->record_number)) {
                $record->record_number = static::generateRecordNumber();
            }
        });
    }

    public static function generateRecordNumber()
    {
        $date = now()->format('Ymd');
        $lastRecord = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastRecord) {
            $lastNumber = substr($lastRecord->record_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return 'MR' . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

### Step 3: Diagnosis Model

**database/migrations/xxxx_create_diagnoses_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('diagnoses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('medical_record_id')->constrained()->onDelete('cascade');
            
            // Diagnosis details
            $table->string('icd10_code')->nullable(); // ICD-10 diagnosis code
            $table->string('diagnosis_name');
            $table->text('description')->nullable();
            $table->enum('type', ['primary', 'secondary', 'differential'])->default('primary');
            $table->enum('certainty', ['confirmed', 'suspected', 'rule_out'])->default('confirmed');
            
            // Additional information
            $table->date('onset_date')->nullable();
            $table->enum('severity', ['mild', 'moderate', 'severe'])->nullable();
            $table->text('notes')->nullable();
            
            $table->timestamps();
            
            $table->index(['medical_record_id', 'type']);
            $table->index('icd10_code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('diagnoses');
    }
};
```

**app/Models/Diagnosis.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Diagnosis extends Model
{
    use HasFactory;

    protected $fillable = [
        'medical_record_id', 'icd10_code', 'diagnosis_name', 'description',
        'type', 'certainty', 'onset_date', 'severity', 'notes'
    ];

    protected function casts(): array
    {
        return [
            'onset_date' => 'date',
        ];
    }

    public function medicalRecord()
    {
        return $this->belongsTo(MedicalRecord::class);
    }

    public function scopePrimary($query)
    {
        return $query->where('type', 'primary');
    }

    public function scopeConfirmed($query)
    {
        return $query->where('certainty', 'confirmed');
    }
}
```

### Step 4: Medical Records Controller

**app/Http/Controllers/Clinic/MedicalRecordController.php**:

```php
<?php

namespace App\Http\Controllers\Clinic;

use App\Http\Controllers\Controller;
use App\Models\MedicalRecord;
use App\Models\Patient;
use App\Models\Appointment;
use App\Models\Diagnosis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class MedicalRecordController extends Controller
{
    public function index(Request $request)
    {
        $query = MedicalRecord::with(['patient', 'doctor', 'clinic'])
            ->where('clinic_id', auth()->user()->clinic_id);

        // Filter by patient
        if ($request->filled('patient_id')) {
            $query->where('patient_id', $request->patient_id);
        }

        // Filter by doctor (if not admin)
        if (!auth()->user()->isAdmin() && auth()->user()->isDoctor()) {
            $query->where('doctor_id', auth()->id());
        } elseif ($request->filled('doctor_id')) {
            $query->where('doctor_id', $request->doctor_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('visit_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('visit_date', '<=', $request->date_to);
        }

        // Search by record number or patient name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('record_number', 'like', "%{$search}%")
                  ->orWhereHas('patient', function ($patientQuery) use ($search) {
                      $patientQuery->where('first_name', 'like', "%{$search}%")
                                  ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        $records = $query->orderBy('visit_date', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('clinic/medical-records/index', [
            'records' => $records,
            'filters' => $request->only(['patient_id', 'doctor_id', 'date_from', 'date_to', 'search'])
        ]);
    }

    public function create(Request $request)
    {
        $patients = Patient::where('clinic_id', auth()->user()->clinic_id)
            ->where('is_active', true)
            ->orderBy('first_name')
            ->get();

        $appointment = null;
        if ($request->appointment_id) {
            $appointment = Appointment::with('patient', 'doctor')
                ->findOrFail($request->appointment_id);
        }

        return Inertia::render('clinic/medical-records/create', [
            'patients' => $patients,
            'appointment' => $appointment
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'visit_date' => 'required|date',
            'visit_type' => 'required|in:consultation,follow_up,emergency,procedure,checkup',
            'chief_complaint' => 'required|string',
            'history_of_present_illness' => 'nullable|string',
            'physical_examination' => 'nullable|string',
            'vital_signs' => 'nullable|array',
            'assessment' => 'nullable|string',
            'treatment_plan' => 'nullable|string',
            'notes' => 'nullable|string',
            'next_visit_date' => 'nullable|date|after:visit_date',
            'follow_up_instructions' => 'nullable|string',
            
            // Diagnoses
            'diagnoses' => 'nullable|array',
            'diagnoses.*.icd10_code' => 'nullable|string',
            'diagnoses.*.diagnosis_name' => 'required|string',
            'diagnoses.*.description' => 'nullable|string',
            'diagnoses.*.type' => 'required|in:primary,secondary,differential',
            'diagnoses.*.certainty' => 'required|in:confirmed,suspected,rule_out',
        ]);

        DB::beginTransaction();
        
        try {
            // Create medical record
            $record = MedicalRecord::create([
                ...$validated,
                'doctor_id' => auth()->id(),
                'clinic_id' => auth()->user()->clinic_id,
                'status' => 'draft'
            ]);

            // Create diagnoses
            if (!empty($validated['diagnoses'])) {
                foreach ($validated['diagnoses'] as $diagnosisData) {
                    $record->diagnoses()->create($diagnosisData);
                }
            }

            // Update appointment status if linked
            if ($validated['appointment_id']) {
                $appointment = Appointment::find($validated['appointment_id']);
                if ($appointment && $appointment->status === 'in_progress') {
                    $appointment->complete();
                }
            }

            DB::commit();

            return redirect()->route('clinic.medical-records.show', $record)
                ->with('success', 'Medical record created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create medical record. Please try again.']);
        }
    }

    public function show(MedicalRecord $medicalRecord)
    {
        $this->authorize('view', $medicalRecord);
        
        $medicalRecord->load([
            'patient',
            'doctor',
            'appointment',
            'clinic',
            'diagnoses',
            'treatments',
            'labResults',
            'prescriptions'
        ]);

        return Inertia::render('clinic/medical-records/show', [
            'record' => $medicalRecord
        ]);
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built medical record management system** with comprehensive documentation
2. ✅ **Implemented diagnosis tracking** with ICD-10 code support
3. ✅ **Created medical record model** with relationships and workflows
4. ✅ **Designed secure access controls** for sensitive medical data
5. ✅ **Added medical record search** and filtering capabilities
6. ✅ **Built medical record controller** with full CRUD operations

### What We Have Now

- Complete medical records management system
- Diagnosis tracking with ICD-10 support
- Secure access controls for medical data
- Medical record search and reporting
- Integration with appointments and patient data

### Next Steps

In **Chapter 8: Prescription Management**, we'll:

- Build prescription management system
- Create medication database and interactions
- Implement prescription workflow and approvals
- Design pharmacy integration features

---

**Ready to continue?** Proceed to [Chapter 8: Prescription Management](./chapter-08-prescriptions.md) to build the prescription system.
