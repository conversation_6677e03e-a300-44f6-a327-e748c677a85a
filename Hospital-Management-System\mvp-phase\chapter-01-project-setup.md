# Chapter 01 - Project Setup dan Kon<PERSON><PERSON><PERSON><PERSON> Awal

## Tujuan Chapter
Pada chapter ini, kita akan:
- <PERSON><PERSON><PERSON>l dan mengkonfi<PERSON><PERSON><PERSON> Laravel 12
- Setup React starter kit resmi <PERSON>vel
- Konfigurasi database SQLite untuk development
- Setup struktur project untuk Hospital Management System
- Konfigurasi environment untuk development

## Prerequisites
- PHP 8.2 atau lebih tinggi
- Composer
- Node.js 18+ dan npm/yarn
- Git

## Langkah 1: Instalasi <PERSON>vel 12

### 1.1 Create Project Baru
```bash
composer create-project laravel/laravel hospital-management-system
cd hospital-management-system
```

### 1.2 Verifikasi Instalasi
```bash
php artisan --version
# Output: Laravel Framework 12.x.x
```

## Langkah 2: Setup React Starter Kit

### 2.1 Install Laravel Breeze dengan React
```bash
composer require laravel/breeze --dev
php artisan breeze:install react
```

### 2.2 Install Dependencies
```bash
npm install
npm run build
```

### 2.3 Verifikasi Setup React
```bash
npm run dev
```

## Langkah 3: Konfigurasi Database

### 3.1 Setup SQLite Database
Edit file `.env`:
```env
DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=
```

### 3.2 Create Database File
```bash
touch database/database.sqlite
```

### 3.3 Run Migrations
```bash
php artisan migrate
```

## Langkah 4: Konfigurasi Aplikasi

### 4.1 Update App Configuration
Edit `config/app.php`:
```php
'name' => env('APP_NAME', 'Hospital Management System'),
'locale' => 'id',
'fallback_locale' => 'en',
'timezone' => 'Asia/Jakarta',
```

### 4.2 Update Environment Variables
Edit `.env`:
```env
APP_NAME="Hospital Management System"
APP_URL=http://localhost:8000
APP_TIMEZONE=Asia/Jakarta
APP_LOCALE=id
```

## Langkah 5: Setup Struktur Project

### 5.1 Create Directories
```bash
mkdir -p app/Models/Hospital
mkdir -p app/Http/Controllers/Hospital
mkdir -p app/Http/Requests/Hospital
mkdir -p database/migrations/hospital
mkdir -p database/seeders/hospital
mkdir -p resources/js/Pages/Hospital
mkdir -p resources/js/Components/Hospital
```

### 5.2 Create Base Controller
```bash
php artisan make:controller Hospital/BaseController
```

Edit `app/Http/Controllers/Hospital/BaseController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BaseController extends Controller
{
    /**
     * Base controller untuk semua controller hospital
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
}
```

## Langkah 6: Setup Localization

### 6.1 Create Language Files
```bash
mkdir -p lang/id
```

Create `lang/id/app.php`:
```php
<?php

return [
    'name' => 'Sistem Manajemen Rumah Sakit',
    'welcome' => 'Selamat Datang',
    'dashboard' => 'Dashboard',
    'patients' => 'Pasien',
    'doctors' => 'Dokter',
    'appointments' => 'Janji Temu',
    'medical_records' => 'Rekam Medis',
    'staff' => 'Staff',
    'settings' => 'Pengaturan',
];
```

### 6.2 Update Service Provider
Edit `app/Providers/AppServiceProvider.php`:
```php
<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;

class AppServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        App::setLocale('id');
    }
}
```

## Langkah 7: Setup Development Tools

### 7.1 Install Development Dependencies
```bash
composer require --dev laravel/telescope
php artisan telescope:install
php artisan migrate
```

### 7.2 Install Testing Tools
```bash
composer require --dev pestphp/pest pestphp/pest-plugin-laravel
./vendor/bin/pest --init
```

## Langkah 8: Verifikasi Setup

### 8.1 Start Development Server
```bash
php artisan serve
```

### 8.2 Start Vite Development Server
```bash
npm run dev
```

### 8.3 Test Aplikasi
Buka browser dan akses `http://localhost:8000`

## Langkah 9: Git Setup

### 9.1 Initialize Git Repository
```bash
git init
git add .
git commit -m "Initial setup: Laravel 12 with React starter kit for Hospital Management System"
```

## Struktur Project yang Dihasilkan

```
hospital-management-system/
├── app/
│   ├── Http/Controllers/Hospital/
│   ├── Models/Hospital/
│   └── Http/Requests/Hospital/
├── database/
│   ├── migrations/hospital/
│   └── seeders/hospital/
├── resources/
│   ├── js/Pages/Hospital/
│   └── js/Components/Hospital/
├── lang/id/
└── database/database.sqlite
```

## Testing Setup

### Test Basic Functionality
```bash
php artisan test
```

## Troubleshooting

### Common Issues:
1. **Node.js Version**: Pastikan menggunakan Node.js 18+
2. **PHP Extensions**: Pastikan extension SQLite enabled
3. **Permissions**: Set proper permissions untuk storage dan bootstrap/cache

## Next Steps

Pada chapter selanjutnya, kita akan:
- Merancang database schema untuk hospital management
- Membuat migrations untuk semua entities
- Setup relationships antar models

## Checklist Completion

- [ ] Laravel 12 terinstall dengan benar
- [ ] React starter kit berfungsi
- [ ] Database SQLite terkonfigurasi
- [ ] Struktur project hospital terbuat
- [ ] Localization Indonesia setup
- [ ] Development tools terinstall
- [ ] Git repository initialized
- [ ] Aplikasi dapat diakses di browser

**Estimasi Waktu**: 30-45 menit

**Difficulty Level**: Beginner

**Prerequisites Check**: Pastikan semua tools terinstall sebelum melanjutkan ke chapter berikutnya.
