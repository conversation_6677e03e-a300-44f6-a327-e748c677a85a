# Chapter 1: Environment Setup and Laravel Installation

Welcome to the first chapter of our Car Wash Management System tutorial! In this chapter, we'll set up your development environment and create a new Laravel 11 project from scratch.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Have a complete development environment set up
- Understand the Laravel project structure
- Have a running Laravel 11 application
- Know how to use basic Laravel Artisan commands

## 📋 What We'll Cover

1. Installing PHP 8.2+
2. Installing Composer
3. Installing Node.js and npm
4. Creating a new Laravel 11 project
5. Understanding the project structure
6. Running your first Laravel application
7. Setting up version control with Git

## 🛠 Step 1: Installing PHP 8.2+

Laravel 11 requires PHP 8.2 or higher. Let's install it based on your operating system.

### Windows

1. **Download PHP from the official website:**
   - Visit [https://windows.php.net/download/](https://windows.php.net/download/)
   - Download the latest PHP 8.2+ Thread Safe version
   - Extract to `C:\php`

2. **Add PHP to your PATH:**
   - Open System Properties → Advanced → Environment Variables
   - Add `C:\php` to your PATH variable

3. **Configure PHP:**
   ```bash
   # Copy php.ini-development to php.ini
   copy C:\php\php.ini-development C:\php\php.ini
   ```

4. **Enable required extensions in php.ini:**
   ```ini
   extension=curl
   extension=fileinfo
   extension=gd
   extension=mbstring
   extension=openssl
   extension=pdo_mysql
   extension=pdo_sqlite
   extension=zip
   ```

### macOS

Using Homebrew (recommended):

```bash
# Install Homebrew if you haven't already
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install PHP 8.2
brew install php@8.2

# Link PHP to make it available globally
brew link php@8.2 --force
```

### Linux (Ubuntu/Debian)

```bash
# Update package list
sudo apt update

# Install PHP 8.2 and required extensions
sudo apt install php8.2 php8.2-cli php8.2-common php8.2-curl php8.2-gd php8.2-mbstring php8.2-mysql php8.2-xml php8.2-zip

# Verify installation
php --version
```

**Verify PHP Installation:**
```bash
php --version
# Should output: PHP 8.2.x (cli) ...
```

## 🛠 Step 2: Installing Composer

Composer is PHP's dependency manager and is essential for Laravel development.

### Windows

1. **Download Composer:**
   - Visit [https://getcomposer.org/download/](https://getcomposer.org/download/)
   - Download and run `Composer-Setup.exe`
   - Follow the installation wizard

### macOS/Linux

```bash
# Download and install Composer globally
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Make it executable
sudo chmod +x /usr/local/bin/composer
```

**Verify Composer Installation:**
```bash
composer --version
# Should output: Composer version 2.x.x
```

## 🛠 Step 3: Installing Node.js and npm

We'll need Node.js for compiling frontend assets.

### All Operating Systems

1. **Visit [https://nodejs.org/](https://nodejs.org/)**
2. **Download the LTS version**
3. **Run the installer and follow the setup wizard**

**Verify Node.js Installation:**
```bash
node --version
# Should output: v18.x.x or higher

npm --version
# Should output: 9.x.x or higher
```

## 🛠 Step 4: Creating Your Laravel 11 Project

Now let's create our Car Wash Management System project!

### Method 1: Using Composer (Recommended)

```bash
# Navigate to your development directory
cd /path/to/your/projects

# Create a new Laravel project
composer create-project laravel/laravel car-wash-management

# Navigate into the project directory
cd car-wash-management
```

### Method 2: Using Laravel Installer

```bash
# Install Laravel installer globally
composer global require laravel/installer

# Create new project
laravel new car-wash-management

# Navigate into the project directory
cd car-wash-management
```

## 🛠 Step 5: Understanding the Project Structure

Let's explore the Laravel project structure:

```
car-wash-management/
├── app/                    # Application core files
│   ├── Http/
│   │   ├── Controllers/    # Controllers handle requests
│   │   ├── Middleware/     # Request filtering
│   │   └── Requests/       # Form request validation
│   ├── Models/             # Eloquent models (database)
│   └── Providers/          # Service providers
├── bootstrap/              # Framework bootstrap files
├── config/                 # Configuration files
├── database/
│   ├── migrations/         # Database migrations
│   ├── seeders/           # Database seeders
│   └── factories/         # Model factories for testing
├── public/                 # Web server document root
│   ├── index.php          # Application entry point
│   └── assets/            # Compiled CSS/JS files
├── resources/
│   ├── views/             # Blade templates
│   ├── css/               # CSS source files
│   └── js/                # JavaScript source files
├── routes/
│   ├── web.php            # Web routes
│   ├── api.php            # API routes
│   └── console.php        # Console commands
├── storage/               # File storage and logs
├── tests/                 # Application tests
├── vendor/                # Composer dependencies
├── .env                   # Environment configuration
├── artisan               # Laravel command-line tool
├── composer.json         # PHP dependencies
└── package.json          # Node.js dependencies
```

## 🛠 Step 6: Initial Configuration

### Configure Environment Variables

Laravel uses a `.env` file for environment-specific configuration. Let's set it up:

```bash
# Copy the example environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### Basic .env Configuration

Open the `.env` file and configure these basic settings:

```env
APP_NAME="Car Wash Management"
APP_ENV=local
APP_KEY=base64:your-generated-key-here
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=car_wash_management
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
```

## 🛠 Step 7: Running Your First Laravel Application

Let's start the development server and see Laravel in action!

```bash
# Start the Laravel development server
php artisan serve
```

You should see output like:
```
Starting Laravel development server: http://127.0.0.1:8000
[Thu Dec  7 10:00:00 2023] PHP 8.2.0 Development Server (http://127.0.0.1:8000) started
```

**Open your browser and visit:** `http://localhost:8000`

You should see the Laravel welcome page! 🎉

## 🛠 Step 8: Setting Up Version Control

Let's initialize Git for version control:

```bash
# Initialize Git repository
git init

# Add all files to staging
git add .

# Create initial commit
git commit -m "Initial Laravel 11 project setup"

# Optional: Add remote repository
# git remote add origin https://github.com/yourusername/car-wash-management.git
# git push -u origin main
```

## 🛠 Step 9: Installing Frontend Dependencies

Laravel comes with Vite for asset compilation. Let's set it up:

```bash
# Install Node.js dependencies
npm install

# Start the development server for assets
npm run dev
```

Keep this running in a separate terminal window during development.

## 🧪 Testing Your Setup

Let's verify everything is working correctly:

### 1. Check Laravel Version
```bash
php artisan --version
# Should output: Laravel Framework 11.x.x
```

### 2. Check Available Artisan Commands
```bash
php artisan list
```

### 3. Create a Test Route

Edit `routes/web.php` and add a test route:

```php
<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Add this test route
Route::get('/test', function () {
    return '<h1>Car Wash Management System</h1><p>Laravel is working perfectly!</p>';
});
```

Visit `http://localhost:8000/test` to see your test page.

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Installed PHP 8.2+ with required extensions  
✅ Installed Composer for dependency management  
✅ Installed Node.js and npm for frontend assets  
✅ Created a new Laravel 11 project  
✅ Understood the Laravel project structure  
✅ Configured basic environment settings  
✅ Started the Laravel development server  
✅ Set up version control with Git  
✅ Verified your installation with a test route  

## 🚀 What's Next?

In the next chapter, we'll:
- Set up and configure our database
- Create our first database migrations
- Learn about Laravel's database features
- Set up database seeding for test data

## 💡 Pro Tips

1. **Keep your development server running** with `php artisan serve`
2. **Keep asset compilation running** with `npm run dev`
3. **Use multiple terminal windows** for different processes
4. **Commit your changes frequently** to track your progress
5. **Read Laravel documentation** at [https://laravel.com/docs](https://laravel.com/docs)

## 🔧 Troubleshooting

### Common Issues and Solutions

**Issue: "php command not found"**
- Solution: Make sure PHP is added to your system PATH

**Issue: "composer command not found"**
- Solution: Reinstall Composer and ensure it's in your PATH

**Issue: Laravel welcome page not loading**
- Solution: Check if the development server is running with `php artisan serve`

**Issue: Permission errors on macOS/Linux**
- Solution: Run `sudo chmod -R 755 storage bootstrap/cache`

---

**Ready for the next chapter?** Let's move on to [Chapter 2: Database Setup and Configuration](./02-database-setup.md) where we'll set up our database and create our first migrations!
