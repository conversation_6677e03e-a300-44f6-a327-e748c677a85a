# Chapter 11: Reporting and Analytics

## Building Comprehensive Dashboard and Analytics

In this chapter, we'll create a comprehensive reporting and analytics system that provides insights into clinic operations, patient statistics, financial performance, and operational metrics.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Build comprehensive reporting dashboard
- Create patient statistics and analytics
- Implement appointment and revenue reports
- Design data visualization components
- Set up automated report generation
- Create performance metrics and KPIs

## 📊 Reporting Requirements

### Core Features

1. **Dashboard Analytics**: Real-time clinic performance metrics
2. **Patient Reports**: Patient demographics and health statistics
3. **Financial Reports**: Revenue, billing, and payment analytics
4. **Operational Reports**: Appointment efficiency and staff performance
5. **Clinical Reports**: Medical outcomes and treatment effectiveness
6. **Custom Reports**: Flexible reporting with filters and exports

## 🛠 Backend Implementation

### Step 1: Reporting Service

**app/Services/ReportingService.php**:

```php
<?php

namespace App\Services;

use App\Models\Patient;
use App\Models\Appointment;
use App\Models\Billing;
use App\Models\MedicalRecord;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportingService
{
    /**
     * Get dashboard overview statistics
     */
    public function getDashboardStats($clinicId, $period = 'month'): array
    {
        $startDate = $this->getStartDate($period);
        $endDate = now();

        return [
            'patients' => $this->getPatientStats($clinicId, $startDate, $endDate),
            'appointments' => $this->getAppointmentStats($clinicId, $startDate, $endDate),
            'revenue' => $this->getRevenueStats($clinicId, $startDate, $endDate),
            'staff' => $this->getStaffStats($clinicId),
            'trends' => $this->getTrendData($clinicId, $period),
        ];
    }

    /**
     * Get patient statistics
     */
    public function getPatientStats($clinicId, $startDate, $endDate): array
    {
        $totalPatients = Patient::where('clinic_id', $clinicId)->count();
        $newPatients = Patient::where('clinic_id', $clinicId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $activePatients = Patient::where('clinic_id', $clinicId)
            ->whereHas('appointments', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('appointment_date', [$startDate, $endDate]);
            })
            ->count();

        // Demographics
        $genderStats = Patient::where('clinic_id', $clinicId)
            ->select('gender', DB::raw('count(*) as count'))
            ->groupBy('gender')
            ->get()
            ->pluck('count', 'gender');

        $ageGroups = Patient::where('clinic_id', $clinicId)
            ->select(
                DB::raw('
                    CASE 
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 18 THEN "Under 18"
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 18 AND 35 THEN "18-35"
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 36 AND 55 THEN "36-55"
                        WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 56 AND 70 THEN "56-70"
                        ELSE "Over 70"
                    END as age_group
                '),
                DB::raw('count(*) as count')
            )
            ->groupBy('age_group')
            ->get()
            ->pluck('count', 'age_group');

        return [
            'total_patients' => $totalPatients,
            'new_patients' => $newPatients,
            'active_patients' => $activePatients,
            'gender_distribution' => $genderStats,
            'age_distribution' => $ageGroups,
            'bpjs_patients' => Patient::where('clinic_id', $clinicId)->where('has_bpjs', true)->count(),
        ];
    }

    /**
     * Get appointment statistics
     */
    public function getAppointmentStats($clinicId, $startDate, $endDate): array
    {
        $query = Appointment::where('clinic_id', $clinicId)
            ->whereBetween('appointment_date', [$startDate, $endDate]);

        $totalAppointments = $query->count();
        $completedAppointments = $query->clone()->where('status', 'completed')->count();
        $cancelledAppointments = $query->clone()->where('status', 'cancelled')->count();
        $noShowAppointments = $query->clone()->where('status', 'no_show')->count();

        // Appointment types
        $appointmentTypes = $query->clone()
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type');

        // Daily appointment trends
        $dailyTrends = $query->clone()
            ->select(
                DB::raw('DATE(appointment_date) as date'),
                DB::raw('count(*) as count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_appointments' => $totalAppointments,
            'completed_appointments' => $completedAppointments,
            'cancelled_appointments' => $cancelledAppointments,
            'no_show_appointments' => $noShowAppointments,
            'completion_rate' => $totalAppointments > 0 ? ($completedAppointments / $totalAppointments) * 100 : 0,
            'appointment_types' => $appointmentTypes,
            'daily_trends' => $dailyTrends,
        ];
    }

    /**
     * Get revenue statistics
     */
    public function getRevenueStats($clinicId, $startDate, $endDate): array
    {
        $query = Billing::where('clinic_id', $clinicId)
            ->whereBetween('invoice_date', [$startDate, $endDate]);

        $totalRevenue = $query->sum('total_amount');
        $paidRevenue = $query->clone()->sum('paid_amount');
        $outstandingRevenue = $query->clone()->sum('outstanding_amount');

        // Revenue by payment method
        $paymentMethods = DB::table('payments')
            ->join('billing', 'payments.billing_id', '=', 'billing.id')
            ->where('billing.clinic_id', $clinicId)
            ->whereBetween('payments.payment_date', [$startDate, $endDate])
            ->where('payments.status', 'completed')
            ->select('payments.payment_method', DB::raw('sum(payments.amount) as total'))
            ->groupBy('payments.payment_method')
            ->get()
            ->pluck('total', 'payment_method');

        // Monthly revenue trends
        $monthlyRevenue = $query->clone()
            ->select(
                DB::raw('YEAR(invoice_date) as year'),
                DB::raw('MONTH(invoice_date) as month'),
                DB::raw('sum(total_amount) as revenue'),
                DB::raw('sum(paid_amount) as paid')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'total_revenue' => $totalRevenue,
            'paid_revenue' => $paidRevenue,
            'outstanding_revenue' => $outstandingRevenue,
            'collection_rate' => $totalRevenue > 0 ? ($paidRevenue / $totalRevenue) * 100 : 0,
            'payment_methods' => $paymentMethods,
            'monthly_trends' => $monthlyRevenue,
        ];
    }

    /**
     * Get staff performance statistics
     */
    public function getStaffStats($clinicId): array
    {
        $totalStaff = User::where('clinic_id', $clinicId)
            ->whereIn('role', ['doctor', 'nurse', 'receptionist'])
            ->where('is_active', true)
            ->count();

        $doctors = User::where('clinic_id', $clinicId)
            ->where('role', 'doctor')
            ->where('is_active', true)
            ->count();

        // Doctor performance (appointments completed)
        $doctorPerformance = User::where('clinic_id', $clinicId)
            ->where('role', 'doctor')
            ->withCount(['doctorAppointments as completed_appointments' => function ($query) {
                $query->where('status', 'completed')
                    ->whereBetween('appointment_date', [now()->startOfMonth(), now()]);
            }])
            ->get()
            ->map(function ($doctor) {
                return [
                    'name' => $doctor->name,
                    'completed_appointments' => $doctor->completed_appointments,
                ];
            });

        return [
            'total_staff' => $totalStaff,
            'total_doctors' => $doctors,
            'doctor_performance' => $doctorPerformance,
        ];
    }

    /**
     * Get trend data for charts
     */
    public function getTrendData($clinicId, $period): array
    {
        $days = $period === 'week' ? 7 : ($period === 'month' ? 30 : 365);
        $startDate = now()->subDays($days);

        // Patient registration trends
        $patientTrends = Patient::where('clinic_id', $clinicId)
            ->whereBetween('created_at', [$startDate, now()])
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('count(*) as count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Appointment trends
        $appointmentTrends = Appointment::where('clinic_id', $clinicId)
            ->whereBetween('appointment_date', [$startDate, now()])
            ->select(
                DB::raw('DATE(appointment_date) as date'),
                DB::raw('count(*) as count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Revenue trends
        $revenueTrends = Billing::where('clinic_id', $clinicId)
            ->whereBetween('invoice_date', [$startDate, now()])
            ->select(
                DB::raw('DATE(invoice_date) as date'),
                DB::raw('sum(total_amount) as revenue')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'patients' => $patientTrends,
            'appointments' => $appointmentTrends,
            'revenue' => $revenueTrends,
        ];
    }

    /**
     * Generate custom report
     */
    public function generateCustomReport($clinicId, array $filters): array
    {
        $reportType = $filters['type'] ?? 'appointments';
        $startDate = Carbon::parse($filters['start_date'] ?? now()->startOfMonth());
        $endDate = Carbon::parse($filters['end_date'] ?? now());

        switch ($reportType) {
            case 'appointments':
                return $this->generateAppointmentReport($clinicId, $startDate, $endDate, $filters);
            case 'patients':
                return $this->generatePatientReport($clinicId, $startDate, $endDate, $filters);
            case 'revenue':
                return $this->generateRevenueReport($clinicId, $startDate, $endDate, $filters);
            case 'medical':
                return $this->generateMedicalReport($clinicId, $startDate, $endDate, $filters);
            default:
                throw new \InvalidArgumentException('Invalid report type');
        }
    }

    /**
     * Generate appointment report
     */
    private function generateAppointmentReport($clinicId, $startDate, $endDate, $filters): array
    {
        $query = Appointment::with(['patient', 'doctor', 'clinic'])
            ->where('clinic_id', $clinicId)
            ->whereBetween('appointment_date', [$startDate, $endDate]);

        // Apply filters
        if (!empty($filters['doctor_id'])) {
            $query->where('doctor_id', $filters['doctor_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        $appointments = $query->orderBy('appointment_date', 'desc')->get();

        return [
            'data' => $appointments,
            'summary' => [
                'total_appointments' => $appointments->count(),
                'completed' => $appointments->where('status', 'completed')->count(),
                'cancelled' => $appointments->where('status', 'cancelled')->count(),
                'no_show' => $appointments->where('status', 'no_show')->count(),
            ]
        ];
    }

    /**
     * Get start date based on period
     */
    private function getStartDate($period): Carbon
    {
        switch ($period) {
            case 'week':
                return now()->startOfWeek();
            case 'month':
                return now()->startOfMonth();
            case 'quarter':
                return now()->startOfQuarter();
            case 'year':
                return now()->startOfYear();
            default:
                return now()->startOfMonth();
        }
    }
}
```

### Step 2: Reports Controller

**app/Http/Controllers/Clinic/ReportsController.php**:

```php
<?php

namespace App\Http\Controllers\Clinic;

use App\Http\Controllers\Controller;
use App\Services\ReportingService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ReportsController extends Controller
{
    protected $reportingService;

    public function __construct(ReportingService $reportingService)
    {
        $this->reportingService = $reportingService;
    }

    public function dashboard(Request $request)
    {
        $period = $request->get('period', 'month');
        $stats = $this->reportingService->getDashboardStats(auth()->user()->clinic_id, $period);

        return Inertia::render('clinic/reports/dashboard', [
            'stats' => $stats,
            'period' => $period
        ]);
    }

    public function patients(Request $request)
    {
        $filters = $request->only(['start_date', 'end_date', 'gender', 'age_group']);
        
        $report = $this->reportingService->generateCustomReport(
            auth()->user()->clinic_id,
            array_merge($filters, ['type' => 'patients'])
        );

        return Inertia::render('clinic/reports/patients', [
            'report' => $report,
            'filters' => $filters
        ]);
    }

    public function appointments(Request $request)
    {
        $filters = $request->only(['start_date', 'end_date', 'doctor_id', 'status', 'type']);
        
        $report = $this->reportingService->generateCustomReport(
            auth()->user()->clinic_id,
            array_merge($filters, ['type' => 'appointments'])
        );

        return Inertia::render('clinic/reports/appointments', [
            'report' => $report,
            'filters' => $filters
        ]);
    }

    public function revenue(Request $request)
    {
        $filters = $request->only(['start_date', 'end_date', 'payment_method']);
        
        $report = $this->reportingService->generateCustomReport(
            auth()->user()->clinic_id,
            array_merge($filters, ['type' => 'revenue'])
        );

        return Inertia::render('clinic/reports/revenue', [
            'report' => $report,
            'filters' => $filters
        ]);
    }

    public function export(Request $request)
    {
        $request->validate([
            'type' => 'required|in:appointments,patients,revenue,medical',
            'format' => 'required|in:csv,pdf,excel',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        // Generate report data
        $report = $this->reportingService->generateCustomReport(
            auth()->user()->clinic_id,
            $request->all()
        );

        // Export based on format
        switch ($request->format) {
            case 'csv':
                return $this->exportToCsv($report, $request->type);
            case 'pdf':
                return $this->exportToPdf($report, $request->type);
            case 'excel':
                return $this->exportToExcel($report, $request->type);
        }
    }

    private function exportToCsv($report, $type)
    {
        $filename = "{$type}_report_" . now()->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($report, $type) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers based on report type
            $this->addCsvHeaders($file, $type);
            
            // Add data rows
            foreach ($report['data'] as $row) {
                $this->addCsvRow($file, $row, $type);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function addCsvHeaders($file, $type)
    {
        switch ($type) {
            case 'appointments':
                fputcsv($file, ['Date', 'Time', 'Patient', 'Doctor', 'Type', 'Status']);
                break;
            case 'patients':
                fputcsv($file, ['Name', 'Gender', 'Age', 'Phone', 'Email', 'Registration Date']);
                break;
            case 'revenue':
                fputcsv($file, ['Invoice Number', 'Date', 'Patient', 'Amount', 'Paid', 'Status']);
                break;
        }
    }

    private function addCsvRow($file, $row, $type)
    {
        switch ($type) {
            case 'appointments':
                fputcsv($file, [
                    $row->appointment_date->format('Y-m-d'),
                    $row->appointment_time->format('H:i'),
                    $row->patient->full_name,
                    $row->doctor->name,
                    $row->type,
                    $row->status
                ]);
                break;
            // Add other cases as needed
        }
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive reporting service** with dashboard analytics
2. ✅ **Implemented patient statistics** and demographic analysis
3. ✅ **Created financial reporting** with revenue and payment analytics
4. ✅ **Designed operational reports** for appointments and staff performance
5. ✅ **Added custom report generation** with flexible filters
6. ✅ **Built report export functionality** in multiple formats

### What We Have Now

- Comprehensive dashboard with real-time analytics
- Patient demographics and health statistics
- Financial performance and revenue tracking
- Operational efficiency metrics
- Custom report generation with exports

### Next Steps

In **Chapter 12: Multi-Clinic Support**, we'll:

- Implement multi-tenant architecture
- Create clinic management and switching
- Design cross-clinic data access controls
- Build centralized administration features

---

**Ready to continue?** Proceed to [Chapter 12: Multi-Clinic Support](./chapter-12-multi-clinic.md) to build multi-clinic capabilities.
