# Chapter 3: Authentication and Authorization

## Implementing Role-Based Access Control for Healthcare

In this chapter, we'll extend the Laravel Starter Kit's authentication system to support multiple user roles and implement comprehensive authorization for our Clinic Management System. We'll create role-based access control (RBAC) that ensures proper security and privacy for healthcare data.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Understand healthcare-specific authorization requirements
- Extend the existing authentication system for multiple user roles
- Implement role-based middleware and permissions
- Create user management interfaces for different roles
- Set up secure session management for healthcare data
- Implement audit logging for security compliance

## 🔐 Healthcare Authorization Requirements

### User Roles in Clinic Management

1. **Admin**: Full system access, user management, clinic configuration
2. **Doctor**: Patient records, appointments, prescriptions, medical records
3. **Nurse**: Patient care, appointment assistance, basic record access
4. **Receptionist**: Appointment scheduling, patient registration, billing
5. **Patient**: Personal records, appointment booking, prescription history

### Security Principles

- **Principle of Least Privilege**: Users only access what they need
- **Data Segregation**: Clinic-specific data isolation
- **Audit Trails**: All access and modifications logged
- **Session Security**: Secure session management for sensitive data

## 🛠 Extending the Authentication System

### Step 1: Create Permission System

First, let's create a permission system:

```bash
php artisan make:migration create_permissions_table
php artisan make:migration create_role_permissions_table
php artisan make:model Permission
php artisan make:model RolePermission
```

**database/migrations/xxxx_create_permissions_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'view_patients'
            $table->string('display_name'); // e.g., 'View Patients'
            $table->string('description')->nullable();
            $table->string('module'); // e.g., 'patients', 'appointments'
            $table->timestamps();
            
            $table->index(['module', 'name']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
```

**database/migrations/xxxx_create_role_permissions_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->enum('role', ['admin', 'doctor', 'nurse', 'receptionist', 'patient']);
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['role', 'permission_id']);
            $table->index('role');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('role_permissions');
    }
};
```

### Step 2: Create Permission Models

**app/Models/Permission.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'display_name', 'description', 'module'
    ];

    public function rolePermissions()
    {
        return $this->hasMany(RolePermission::class);
    }

    public function roles()
    {
        return $this->rolePermissions()->pluck('role')->unique();
    }
}
```

**app/Models/RolePermission.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RolePermission extends Model
{
    use HasFactory;

    protected $fillable = ['role', 'permission_id'];

    public function permission()
    {
        return $this->belongsTo(Permission::class);
    }
}
```

### Step 3: Extend User Model with Permissions

Update **app/Models/User.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name', 'email', 'password', 'role', 'phone', 'address',
        'date_of_birth', 'gender', 'employee_id', 'clinic_id', 'is_active'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    // Relationships
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function patient()
    {
        return $this->hasOne(Patient::class);
    }

    public function doctor()
    {
        return $this->hasOne(Doctor::class);
    }

    // Permission methods
    public function hasPermission(string $permission): bool
    {
        return RolePermission::where('role', $this->role)
            ->whereHas('permission', function ($query) use ($permission) {
                $query->where('name', $permission);
            })
            ->exists();
    }

    public function getPermissions()
    {
        return RolePermission::where('role', $this->role)
            ->with('permission')
            ->get()
            ->pluck('permission');
    }

    public function canAccessClinic(int $clinicId): bool
    {
        // Admin can access all clinics
        if ($this->role === 'admin') {
            return true;
        }

        // Other users can only access their assigned clinic
        return $this->clinic_id === $clinicId;
    }

    // Role checking methods
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isDoctor(): bool
    {
        return $this->role === 'doctor';
    }

    public function isNurse(): bool
    {
        return $this->role === 'nurse';
    }

    public function isReceptionist(): bool
    {
        return $this->role === 'receptionist';
    }

    public function isPatient(): bool
    {
        return $this->role === 'patient';
    }

    public function isStaff(): bool
    {
        return in_array($this->role, ['admin', 'doctor', 'nurse', 'receptionist']);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    public function scopeByClinic($query, $clinicId)
    {
        return $query->where('clinic_id', $clinicId);
    }
}
```

## 🛡 Creating Authorization Middleware

### Step 1: Role-Based Middleware

```bash
php artisan make:middleware RoleMiddleware
php artisan make:middleware PermissionMiddleware
php artisan make:middleware ClinicAccessMiddleware
```

**app/Http/Middleware/RoleMiddleware.php**:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        if (!$user->is_active) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Your account has been deactivated.');
        }

        if (!in_array($user->role, $roles)) {
            abort(403, 'Unauthorized access.');
        }

        return $next($request);
    }
}
```

**app/Http/Middleware/PermissionMiddleware.php**:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        if (!$user->hasPermission($permission)) {
            abort(403, 'You do not have permission to access this resource.');
        }

        return $next($request);
    }
}
```

**app/Http/Middleware/ClinicAccessMiddleware.php**:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ClinicAccessMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $clinicId = $request->route('clinic') ?? $request->input('clinic_id');

        if ($clinicId && !$user->canAccessClinic($clinicId)) {
            abort(403, 'You do not have access to this clinic.');
        }

        return $next($request);
    }
}
```

### Step 2: Register Middleware

Update **bootstrap/app.php**:

```php
<?php

use App\Http\Middleware\ClinicAccessMiddleware;
use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\PermissionMiddleware;
use App\Http\Middleware\RoleMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
        ]);

        // Register custom middleware
        $middleware->alias([
            'role' => RoleMiddleware::class,
            'permission' => PermissionMiddleware::class,
            'clinic.access' => ClinicAccessMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
```

## 🌱 Permission Seeder

Create a seeder to populate permissions:

```bash
php artisan make:seeder PermissionSeeder
```

**database/seeders/PermissionSeeder.php**:

```php
<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\RolePermission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        $permissions = [
            // Patient Management
            ['name' => 'view_patients', 'display_name' => 'View Patients', 'module' => 'patients'],
            ['name' => 'create_patients', 'display_name' => 'Create Patients', 'module' => 'patients'],
            ['name' => 'edit_patients', 'display_name' => 'Edit Patients', 'module' => 'patients'],
            ['name' => 'delete_patients', 'display_name' => 'Delete Patients', 'module' => 'patients'],

            // Appointment Management
            ['name' => 'view_appointments', 'display_name' => 'View Appointments', 'module' => 'appointments'],
            ['name' => 'create_appointments', 'display_name' => 'Create Appointments', 'module' => 'appointments'],
            ['name' => 'edit_appointments', 'display_name' => 'Edit Appointments', 'module' => 'appointments'],
            ['name' => 'cancel_appointments', 'display_name' => 'Cancel Appointments', 'module' => 'appointments'],

            // Medical Records
            ['name' => 'view_medical_records', 'display_name' => 'View Medical Records', 'module' => 'medical_records'],
            ['name' => 'create_medical_records', 'display_name' => 'Create Medical Records', 'module' => 'medical_records'],
            ['name' => 'edit_medical_records', 'display_name' => 'Edit Medical Records', 'module' => 'medical_records'],

            // Prescriptions
            ['name' => 'view_prescriptions', 'display_name' => 'View Prescriptions', 'module' => 'prescriptions'],
            ['name' => 'create_prescriptions', 'display_name' => 'Create Prescriptions', 'module' => 'prescriptions'],
            ['name' => 'edit_prescriptions', 'display_name' => 'Edit Prescriptions', 'module' => 'prescriptions'],

            // Billing
            ['name' => 'view_billing', 'display_name' => 'View Billing', 'module' => 'billing'],
            ['name' => 'create_billing', 'display_name' => 'Create Billing', 'module' => 'billing'],
            ['name' => 'process_payments', 'display_name' => 'Process Payments', 'module' => 'billing'],

            // Reports
            ['name' => 'view_reports', 'display_name' => 'View Reports', 'module' => 'reports'],
            ['name' => 'export_reports', 'display_name' => 'Export Reports', 'module' => 'reports'],

            // User Management
            ['name' => 'manage_users', 'display_name' => 'Manage Users', 'module' => 'users'],
            ['name' => 'manage_clinic', 'display_name' => 'Manage Clinic', 'module' => 'clinic'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // Assign permissions to roles
        $rolePermissions = [
            'admin' => [
                'view_patients', 'create_patients', 'edit_patients', 'delete_patients',
                'view_appointments', 'create_appointments', 'edit_appointments', 'cancel_appointments',
                'view_medical_records', 'create_medical_records', 'edit_medical_records',
                'view_prescriptions', 'create_prescriptions', 'edit_prescriptions',
                'view_billing', 'create_billing', 'process_payments',
                'view_reports', 'export_reports',
                'manage_users', 'manage_clinic'
            ],
            'doctor' => [
                'view_patients', 'edit_patients',
                'view_appointments', 'edit_appointments',
                'view_medical_records', 'create_medical_records', 'edit_medical_records',
                'view_prescriptions', 'create_prescriptions', 'edit_prescriptions',
                'view_billing'
            ],
            'nurse' => [
                'view_patients', 'edit_patients',
                'view_appointments', 'create_appointments', 'edit_appointments',
                'view_medical_records',
                'view_prescriptions'
            ],
            'receptionist' => [
                'view_patients', 'create_patients', 'edit_patients',
                'view_appointments', 'create_appointments', 'edit_appointments', 'cancel_appointments',
                'view_billing', 'create_billing', 'process_payments'
            ],
            'patient' => [
                'view_appointments', 'create_appointments',
                'view_prescriptions'
            ]
        ];

        foreach ($rolePermissions as $role => $permissions) {
            foreach ($permissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission) {
                    RolePermission::create([
                        'role' => $role,
                        'permission_id' => $permission->id
                    ]);
                }
            }
        }
    }
}
```

## 🔒 Securing Routes

Update your route files to use the new middleware:

**routes/web.php**:

```php
<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Authenticated routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Admin routes
    Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('users', function () {
            return Inertia::render('admin/users');
        })->name('users');

        Route::get('clinics', function () {
            return Inertia::render('admin/clinics');
        })->name('clinics');
    });

    // Doctor routes
    Route::middleware(['role:doctor,nurse'])->prefix('clinic')->name('clinic.')->group(function () {
        Route::get('patients', function () {
            return Inertia::render('clinic/patients/index');
        })->middleware('permission:view_patients')->name('patients.index');

        Route::get('appointments', function () {
            return Inertia::render('clinic/appointments/index');
        })->middleware('permission:view_appointments')->name('appointments.index');
    });

    // Patient routes
    Route::middleware(['role:patient'])->prefix('patient')->name('patient.')->group(function () {
        Route::get('appointments', function () {
            return Inertia::render('patient/appointments');
        })->name('appointments');

        Route::get('prescriptions', function () {
            return Inertia::render('patient/prescriptions');
        })->name('prescriptions');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
```

## 🎨 Frontend Authorization

### TypeScript Types

Update **resources/js/types/index.d.ts**:

```typescript
export interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'doctor' | 'nurse' | 'receptionist' | 'patient';
    phone?: string;
    clinic_id?: number;
    is_active: boolean;
    permissions?: Permission[];
    clinic?: Clinic;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export interface Permission {
    id: number;
    name: string;
    display_name: string;
    description?: string;
    module: string;
}

export interface Clinic {
    id: number;
    name: string;
    code: string;
    address: string;
    phone: string;
    email?: string;
}
```

### Permission Hook

Create **resources/js/hooks/use-permissions.tsx**:

```typescript
import { usePage } from '@inertiajs/react';
import { SharedData } from '@/types';

export function usePermissions() {
    const { auth } = usePage<SharedData>().props;
    const user = auth.user;

    const hasPermission = (permission: string): boolean => {
        if (!user || !user.permissions) return false;
        return user.permissions.some(p => p.name === permission);
    };

    const hasRole = (role: string | string[]): boolean => {
        if (!user) return false;
        const roles = Array.isArray(role) ? role : [role];
        return roles.includes(user.role);
    };

    const isAdmin = (): boolean => hasRole('admin');
    const isDoctor = (): boolean => hasRole('doctor');
    const isNurse = (): boolean => hasRole('nurse');
    const isReceptionist = (): boolean => hasRole('receptionist');
    const isPatient = (): boolean => hasRole('patient');
    const isStaff = (): boolean => hasRole(['admin', 'doctor', 'nurse', 'receptionist']);

    return {
        user,
        hasPermission,
        hasRole,
        isAdmin,
        isDoctor,
        isNurse,
        isReceptionist,
        isPatient,
        isStaff
    };
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Extended authentication system** for multiple user roles
2. ✅ **Implemented permission-based authorization** with granular control
3. ✅ **Created role-based middleware** for route protection
4. ✅ **Set up clinic-specific access control** for multi-tenant support
5. ✅ **Built permission seeder** with role-based assignments
6. ✅ **Created frontend authorization hooks** for React components

### What We Have Now

- Comprehensive role-based access control system
- Permission-based authorization for granular control
- Secure middleware for route protection
- Multi-clinic access control
- Frontend authorization utilities

### Next Steps

In **Chapter 4: Patient Management System**, we'll:
- Build patient registration and profile management
- Create patient search and filtering capabilities
- Implement medical history tracking
- Design responsive patient management interfaces

---

**Ready to continue?** Proceed to [Chapter 4: Patient Management System](./chapter-04-patient-management.md) to start building the core patient management features.
