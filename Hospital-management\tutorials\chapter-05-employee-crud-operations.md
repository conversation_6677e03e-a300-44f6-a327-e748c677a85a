# Chapter 5: Employee CRUD Operations

## Overview
In this chapter, we'll implement comprehensive CRUD (Create, Read, Update, Delete) operations for employee management. We'll build a robust system with advanced features including bulk operations, file uploads, real-time validation, search functionality, and Indonesian healthcare-specific business logic.

## Learning Objectives
- Create comprehensive employee management controllers
- Implement advanced search and filtering
- Build file upload functionality for profile photos and documents
- Create bulk operations for employee management
- Implement real-time validation and error handling
- Build responsive React components with shadcn/ui

## Prerequisites
- Completed Chapter 1 (Project Setup)
- Completed Chapter 2 (Database Design)
- Completed Chapter 3 (Eloquent Models)
- Completed Chapter 4 (Authentication & Authorization)
- Understanding of Laravel controllers and resources
- Familiarity with React hooks and form handling

## Duration
90-120 minutes

---

## Step 1: Create Employee Controller

### 1.1 Create Employee API Controller

```bash
php artisan make:controller Api/EmployeeController --api
```

Edit `app/Http/Controllers/Api/EmployeeController.php`:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmployeeRequest;
use App\Http\Requests\UpdateEmployeeRequest;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\Services\EmployeeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class EmployeeController extends Controller
{
    protected EmployeeService $employeeService;

    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    /**
     * Display a listing of employees with advanced filtering
     */
    public function index(Request $request): JsonResponse
    {
        $query = Employee::with([
            'user',
            'employeeType',
            'department',
            'position',
            'supervisor',
            'licenses' => function ($q) {
                $q->where('status', 'active');
            }
        ]);

        // Apply filters
        $this->applyFilters($query, $request);

        // Apply search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('employee_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%");
                  });
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $perPage = min($request->get('per_page', 15), 100);
        $employees = $query->paginate($perPage);

        return response()->json([
            'data' => EmployeeResource::collection($employees->items()),
            'meta' => [
                'current_page' => $employees->currentPage(),
                'last_page' => $employees->lastPage(),
                'per_page' => $employees->perPage(),
                'total' => $employees->total(),
                'from' => $employees->firstItem(),
                'to' => $employees->lastItem(),
            ],
            'links' => [
                'first' => $employees->url(1),
                'last' => $employees->url($employees->lastPage()),
                'prev' => $employees->previousPageUrl(),
                'next' => $employees->nextPageUrl(),
            ],
        ]);
    }

    /**
     * Store a newly created employee
     */
    public function store(StoreEmployeeRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            
            // Generate employee number
            $data['employee_number'] = $this->employeeService->generateEmployeeNumber();

            // Handle profile photo upload
            if ($request->hasFile('profile_photo')) {
                $data['profile_photo_path'] = $this->handleProfilePhotoUpload($request->file('profile_photo'));
            }

            $employee = Employee::create($data);

            // Create user account if email is provided
            if ($request->filled('email')) {
                $user = $employee->user()->create([
                    'name' => $employee->full_name,
                    'email' => $request->email,
                    'password' => bcrypt($request->get('password', 'password123')),
                ]);

                // Assign default role based on employee type
                $this->assignDefaultRole($user, $employee);
            }

            DB::commit();

            return response()->json([
                'message' => 'Karyawan berhasil ditambahkan',
                'data' => new EmployeeResource($employee->load([
                    'user', 'employeeType', 'department', 'position', 'supervisor'
                ])),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Clean up uploaded file if exists
            if (isset($data['profile_photo_path'])) {
                Storage::disk('public')->delete($data['profile_photo_path']);
            }

            return response()->json([
                'message' => 'Terjadi kesalahan saat menambahkan karyawan',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified employee
     */
    public function show(Employee $employee): JsonResponse
    {
        $employee->load([
            'user',
            'employeeType',
            'department',
            'position',
            'supervisor',
            'subordinates.position',
            'licenses',
            'employeeShifts' => function ($q) {
                $q->with('shift')->latest()->limit(10);
            },
            'leaveRequests' => function ($q) {
                $q->with('leaveType')->latest()->limit(5);
            }
        ]);

        return response()->json([
            'data' => new EmployeeResource($employee),
        ]);
    }

    /**
     * Update the specified employee
     */
    public function update(UpdateEmployeeRequest $request, Employee $employee): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();

            // Handle profile photo upload
            if ($request->hasFile('profile_photo')) {
                // Delete old photo
                if ($employee->profile_photo_path) {
                    Storage::disk('public')->delete($employee->profile_photo_path);
                }
                
                $data['profile_photo_path'] = $this->handleProfilePhotoUpload($request->file('profile_photo'));
            }

            $employee->update($data);

            // Update user account if exists
            if ($employee->user && $request->filled('email')) {
                $employee->user->update([
                    'name' => $employee->full_name,
                    'email' => $request->email,
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Data karyawan berhasil diperbarui',
                'data' => new EmployeeResource($employee->load([
                    'user', 'employeeType', 'department', 'position', 'supervisor'
                ])),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat memperbarui data karyawan',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified employee
     */
    public function destroy(Employee $employee): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Check if employee has subordinates
            if ($employee->subordinates()->exists()) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus karyawan yang memiliki bawahan. Silakan pindahkan bawahan terlebih dahulu.',
                ], 422);
            }

            // Soft delete employee
            $employee->delete();

            // Deactivate user account
            if ($employee->user) {
                $employee->user->update(['email_verified_at' => null]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Karyawan berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat menghapus karyawan',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk operations for employees
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate,export',
            'employee_ids' => 'required|array|min:1',
            'employee_ids.*' => 'exists:employees,id',
        ]);

        $employeeIds = $request->employee_ids;
        $action = $request->action;

        try {
            DB::beginTransaction();

            switch ($action) {
                case 'delete':
                    $result = $this->bulkDelete($employeeIds);
                    break;
                case 'activate':
                    $result = $this->bulkActivate($employeeIds);
                    break;
                case 'deactivate':
                    $result = $this->bulkDeactivate($employeeIds);
                    break;
                case 'export':
                    $result = $this->bulkExport($employeeIds);
                    break;
                default:
                    throw new \InvalidArgumentException('Invalid action');
            }

            DB::commit();

            return response()->json($result);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan saat melakukan operasi bulk',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get employee statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_employees' => Employee::count(),
            'active_employees' => Employee::where('employment_status', 'active')->count(),
            'medical_staff' => Employee::whereHas('employeeType', function ($q) {
                $q->where('is_medical_staff', true);
            })->count(),
            'employees_by_department' => Employee::select('department_id')
                ->with('department:id,name')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'department' => $item->department->name,
                        'count' => $item->count,
                    ];
                }),
            'employees_by_type' => Employee::select('employee_type_id')
                ->with('employeeType:id,name')
                ->groupBy('employee_type_id')
                ->selectRaw('employee_type_id, count(*) as count')
                ->get()
                ->map(function ($item) {
                    return [
                        'type' => $item->employeeType->name,
                        'count' => $item->count,
                    ];
                }),
            'recent_hires' => Employee::where('hire_date', '>=', now()->subDays(30))->count(),
            'expiring_licenses' => Employee::whereHas('licenses', function ($q) {
                $q->where('status', 'active')
                  ->where('expiry_date', '<=', now()->addDays(30));
            })->count(),
        ];

        return response()->json(['data' => $stats]);
    }

    /**
     * Apply filters to the query
     */
    private function applyFilters($query, Request $request): void
    {
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        if ($request->filled('employee_type_id')) {
            $query->where('employee_type_id', $request->employee_type_id);
        }

        if ($request->filled('position_id')) {
            $query->where('position_id', $request->position_id);
        }

        if ($request->filled('employment_status')) {
            $query->where('employment_status', $request->employment_status);
        }

        if ($request->filled('gender')) {
            $query->where('gender', $request->gender);
        }

        if ($request->filled('hire_date_from')) {
            $query->where('hire_date', '>=', $request->hire_date_from);
        }

        if ($request->filled('hire_date_to')) {
            $query->where('hire_date', '<=', $request->hire_date_to);
        }

        if ($request->filled('supervisor_id')) {
            $query->where('supervisor_id', $request->supervisor_id);
        }

        // Filter by medical staff
        if ($request->filled('is_medical_staff')) {
            $isMedical = filter_var($request->is_medical_staff, FILTER_VALIDATE_BOOLEAN);
            $query->whereHas('employeeType', function ($q) use ($isMedical) {
                $q->where('is_medical_staff', $isMedical);
            });
        }
    }

    /**
     * Handle profile photo upload
     */
    private function handleProfilePhotoUpload($file): string
    {
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs('employee-photos', $filename, 'public');
    }

    /**
     * Assign default role based on employee type
     */
    private function assignDefaultRole($user, Employee $employee): void
    {
        $roleMapping = [
            'DS' => 'Medical Staff', // Dokter Spesialis
            'DU' => 'Medical Staff', // Dokter Umum
            'PER' => 'Medical Staff', // Perawat
            'BID' => 'Medical Staff', // Bidan
            'ADM' => 'Administrative Staff', // Administrasi
        ];

        $role = $roleMapping[$employee->employeeType->code] ?? 'Staff';
        $user->assignRole($role);
    }

    /**
     * Bulk delete employees
     */
    private function bulkDelete(array $employeeIds): array
    {
        $employees = Employee::whereIn('id', $employeeIds)->get();

        // Check for employees with subordinates
        $employeesWithSubordinates = $employees->filter(function ($employee) {
            return $employee->subordinates()->exists();
        });

        if ($employeesWithSubordinates->isNotEmpty()) {
            return [
                'message' => 'Beberapa karyawan tidak dapat dihapus karena memiliki bawahan',
                'failed_employees' => $employeesWithSubordinates->pluck('full_name'),
            ];
        }

        Employee::whereIn('id', $employeeIds)->delete();

        return [
            'message' => count($employeeIds) . ' karyawan berhasil dihapus',
            'deleted_count' => count($employeeIds),
        ];
    }

    /**
     * Bulk activate employees
     */
    private function bulkActivate(array $employeeIds): array
    {
        Employee::whereIn('id', $employeeIds)
                ->update(['employment_status' => 'active']);

        return [
            'message' => count($employeeIds) . ' karyawan berhasil diaktifkan',
            'updated_count' => count($employeeIds),
        ];
    }

    /**
     * Bulk deactivate employees
     */
    private function bulkDeactivate(array $employeeIds): array
    {
        Employee::whereIn('id', $employeeIds)
                ->update(['employment_status' => 'inactive']);

        return [
            'message' => count($employeeIds) . ' karyawan berhasil dinonaktifkan',
            'updated_count' => count($employeeIds),
        ];
    }

    /**
     * Bulk export employees
     */
    private function bulkExport(array $employeeIds): array
    {
        // This would typically generate and return a download URL
        // For now, we'll return the employee data
        $employees = Employee::with(['department', 'position', 'employeeType'])
                            ->whereIn('id', $employeeIds)
                            ->get();

        return [
            'message' => 'Data karyawan siap untuk diekspor',
            'export_count' => count($employeeIds),
            'download_url' => '/api/employees/export/' . implode(',', $employeeIds),
        ];
    }
}
```

---

## Step 1.5: Create Request Validation Classes

### 1.5.1 Create Store Employee Request

```bash
php artisan make:request StoreEmployeeRequest
```

Edit `app/Http/Requests/StoreEmployeeRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create employees');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Personal Information
            'first_name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\.]+$/', // Only letters, spaces, and dots
            ],
            'last_name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\.]+$/',
            ],
            'date_of_birth' => [
                'required',
                'date',
                'before:today',
                'after:1950-01-01', // Reasonable age limit
            ],
            'gender' => [
                'required',
                Rule::in(['male', 'female']),
            ],
            'national_id' => [
                'required',
                'string',
                'size:16', // Indonesian NIK is 16 digits
                'regex:/^[0-9]{16}$/',
                'unique:employees,national_id',
            ],

            // Contact Information
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'unique:users,email',
            ],
            'phone_number' => [
                'required',
                'string',
                'regex:/^(\+62|62|0)[0-9]{9,13}$/', // Indonesian phone format
            ],
            'address' => [
                'required',
                'string',
                'max:500',
            ],
            'city' => [
                'required',
                'string',
                'max:100',
            ],
            'province' => [
                'required',
                'string',
                'max:100',
            ],
            'postal_code' => [
                'required',
                'string',
                'regex:/^[0-9]{5}$/', // Indonesian postal code format
            ],

            // Emergency Contact
            'emergency_contact_name' => [
                'required',
                'string',
                'max:200',
            ],
            'emergency_contact_phone' => [
                'required',
                'string',
                'regex:/^(\+62|62|0)[0-9]{9,13}$/',
            ],
            'emergency_contact_relationship' => [
                'required',
                'string',
                'max:100',
            ],

            // Employment Information
            'hire_date' => [
                'required',
                'date',
                'before_or_equal:today',
            ],
            'employment_status' => [
                'required',
                Rule::in(['active', 'inactive', 'terminated', 'resigned']),
            ],
            'department_id' => [
                'required',
                'exists:departments,id',
            ],
            'position_id' => [
                'required',
                'exists:positions,id',
            ],
            'employee_type_id' => [
                'required',
                'exists:employee_types,id',
            ],
            'supervisor_id' => [
                'nullable',
                'exists:employees,id',
                'different:id', // Cannot be supervisor of themselves
            ],

            // Salary Information
            'salary_grade' => [
                'nullable',
                'string',
                'max:10',
            ],
            'basic_salary' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999999.99',
            ],

            // File Upload
            'profile_photo' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048', // 2MB max
            ],

            // Additional Documents
            'documents' => [
                'nullable',
                'array',
                'max:5', // Maximum 5 documents
            ],
            'documents.*' => [
                'file',
                'mimes:pdf,doc,docx,jpg,jpeg,png',
                'max:5120', // 5MB max per file
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'Nama depan wajib diisi.',
            'first_name.regex' => 'Nama depan hanya boleh berisi huruf, spasi, dan titik.',
            'last_name.required' => 'Nama belakang wajib diisi.',
            'last_name.regex' => 'Nama belakang hanya boleh berisi huruf, spasi, dan titik.',
            'date_of_birth.required' => 'Tanggal lahir wajib diisi.',
            'date_of_birth.before' => 'Tanggal lahir harus sebelum hari ini.',
            'date_of_birth.after' => 'Tanggal lahir tidak valid.',
            'gender.required' => 'Jenis kelamin wajib dipilih.',
            'gender.in' => 'Jenis kelamin tidak valid.',
            'national_id.required' => 'NIK wajib diisi.',
            'national_id.size' => 'NIK harus terdiri dari 16 digit.',
            'national_id.regex' => 'Format NIK tidak valid.',
            'national_id.unique' => 'NIK sudah terdaftar.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'phone_number.required' => 'Nomor telepon wajib diisi.',
            'phone_number.regex' => 'Format nomor telepon tidak valid.',
            'address.required' => 'Alamat wajib diisi.',
            'city.required' => 'Kota wajib diisi.',
            'province.required' => 'Provinsi wajib diisi.',
            'postal_code.required' => 'Kode pos wajib diisi.',
            'postal_code.regex' => 'Format kode pos tidak valid.',
            'emergency_contact_name.required' => 'Nama kontak darurat wajib diisi.',
            'emergency_contact_phone.required' => 'Nomor kontak darurat wajib diisi.',
            'emergency_contact_phone.regex' => 'Format nomor kontak darurat tidak valid.',
            'emergency_contact_relationship.required' => 'Hubungan kontak darurat wajib diisi.',
            'hire_date.required' => 'Tanggal masuk kerja wajib diisi.',
            'hire_date.before_or_equal' => 'Tanggal masuk kerja tidak boleh di masa depan.',
            'employment_status.required' => 'Status kepegawaian wajib dipilih.',
            'employment_status.in' => 'Status kepegawaian tidak valid.',
            'department_id.required' => 'Departemen wajib dipilih.',
            'department_id.exists' => 'Departemen tidak ditemukan.',
            'position_id.required' => 'Posisi wajib dipilih.',
            'position_id.exists' => 'Posisi tidak ditemukan.',
            'employee_type_id.required' => 'Tipe karyawan wajib dipilih.',
            'employee_type_id.exists' => 'Tipe karyawan tidak ditemukan.',
            'supervisor_id.exists' => 'Supervisor tidak ditemukan.',
            'supervisor_id.different' => 'Karyawan tidak dapat menjadi supervisor diri sendiri.',
            'basic_salary.numeric' => 'Gaji pokok harus berupa angka.',
            'basic_salary.min' => 'Gaji pokok tidak boleh negatif.',
            'profile_photo.image' => 'File foto profil harus berupa gambar.',
            'profile_photo.mimes' => 'Foto profil harus berformat JPEG, PNG, atau JPG.',
            'profile_photo.max' => 'Ukuran foto profil maksimal 2MB.',
            'documents.max' => 'Maksimal 5 dokumen dapat diunggah.',
            'documents.*.file' => 'File dokumen tidak valid.',
            'documents.*.mimes' => 'Format dokumen harus PDF, DOC, DOCX, JPG, JPEG, atau PNG.',
            'documents.*.max' => 'Ukuran dokumen maksimal 5MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'nama depan',
            'last_name' => 'nama belakang',
            'date_of_birth' => 'tanggal lahir',
            'gender' => 'jenis kelamin',
            'national_id' => 'NIK',
            'email' => 'email',
            'phone_number' => 'nomor telepon',
            'address' => 'alamat',
            'city' => 'kota',
            'province' => 'provinsi',
            'postal_code' => 'kode pos',
            'emergency_contact_name' => 'nama kontak darurat',
            'emergency_contact_phone' => 'nomor kontak darurat',
            'emergency_contact_relationship' => 'hubungan kontak darurat',
            'hire_date' => 'tanggal masuk kerja',
            'employment_status' => 'status kepegawaian',
            'department_id' => 'departemen',
            'position_id' => 'posisi',
            'employee_type_id' => 'tipe karyawan',
            'supervisor_id' => 'supervisor',
            'salary_grade' => 'grade gaji',
            'basic_salary' => 'gaji pokok',
            'profile_photo' => 'foto profil',
            'documents' => 'dokumen',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(
            response()->json([
                'message' => 'Data yang diberikan tidak valid.',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
```

### 1.5.2 Create Update Employee Request

```bash
php artisan make:request UpdateEmployeeRequest
```

Edit `app/Http/Requests/UpdateEmployeeRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $employee = $this->route('employee');

        // Super Admin can update any employee
        if ($this->user()->hasRole('Super Admin')) {
            return true;
        }

        // Department Head can update employees in their department
        if ($this->user()->hasRole('Department Head')) {
            return $this->user()->employee->department_id === $employee->department_id;
        }

        // HR can update employees
        if ($this->user()->can('update employees')) {
            return true;
        }

        // Employees can update their own basic information
        return $this->user()->employee->id === $employee->id;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $employee = $this->route('employee');

        return [
            // Personal Information
            'first_name' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\.]+$/',
            ],
            'last_name' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\.]+$/',
            ],
            'date_of_birth' => [
                'sometimes',
                'required',
                'date',
                'before:today',
                'after:1950-01-01',
            ],
            'gender' => [
                'sometimes',
                'required',
                Rule::in(['male', 'female']),
            ],
            'national_id' => [
                'sometimes',
                'required',
                'string',
                'size:16',
                'regex:/^[0-9]{16}$/',
                Rule::unique('employees', 'national_id')->ignore($employee->id),
            ],

            // Contact Information
            'email' => [
                'sometimes',
                'required',
                'email:rfc,dns',
                'max:255',
                Rule::unique('users', 'email')->ignore($employee->user_id ?? null),
            ],
            'phone_number' => [
                'sometimes',
                'required',
                'string',
                'regex:/^(\+62|62|0)[0-9]{9,13}$/',
            ],
            'address' => [
                'sometimes',
                'required',
                'string',
                'max:500',
            ],
            'city' => [
                'sometimes',
                'required',
                'string',
                'max:100',
            ],
            'province' => [
                'sometimes',
                'required',
                'string',
                'max:100',
            ],
            'postal_code' => [
                'sometimes',
                'required',
                'string',
                'regex:/^[0-9]{5}$/',
            ],

            // Emergency Contact
            'emergency_contact_name' => [
                'sometimes',
                'required',
                'string',
                'max:200',
            ],
            'emergency_contact_phone' => [
                'sometimes',
                'required',
                'string',
                'regex:/^(\+62|62|0)[0-9]{9,13}$/',
            ],
            'emergency_contact_relationship' => [
                'sometimes',
                'required',
                'string',
                'max:100',
            ],

            // Employment Information (restricted to authorized users)
            'hire_date' => [
                'sometimes',
                'required',
                'date',
                'before_or_equal:today',
                $this->authorizeEmploymentChanges(),
            ],
            'employment_status' => [
                'sometimes',
                'required',
                Rule::in(['active', 'inactive', 'terminated', 'resigned']),
                $this->authorizeEmploymentChanges(),
            ],
            'department_id' => [
                'sometimes',
                'required',
                'exists:departments,id',
                $this->authorizeEmploymentChanges(),
            ],
            'position_id' => [
                'sometimes',
                'required',
                'exists:positions,id',
                $this->authorizeEmploymentChanges(),
            ],
            'employee_type_id' => [
                'sometimes',
                'required',
                'exists:employee_types,id',
                $this->authorizeEmploymentChanges(),
            ],
            'supervisor_id' => [
                'sometimes',
                'nullable',
                'exists:employees,id',
                'different:id',
                $this->authorizeEmploymentChanges(),
            ],

            // Salary Information (restricted to authorized users)
            'salary_grade' => [
                'sometimes',
                'nullable',
                'string',
                'max:10',
                $this->authorizeSalaryChanges(),
            ],
            'basic_salary' => [
                'sometimes',
                'nullable',
                'numeric',
                'min:0',
                'max:999999999.99',
                $this->authorizeSalaryChanges(),
            ],

            // File Upload
            'profile_photo' => [
                'sometimes',
                'nullable',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],

            // Additional Documents
            'documents' => [
                'sometimes',
                'nullable',
                'array',
                'max:5',
            ],
            'documents.*' => [
                'file',
                'mimes:pdf,doc,docx,jpg,jpeg,png',
                'max:5120',
            ],
        ];
    }

    /**
     * Check if user can modify employment information
     */
    private function authorizeEmploymentChanges(): string
    {
        if (!$this->user()->can('update employee employment')) {
            return 'prohibited';
        }

        return 'sometimes';
    }

    /**
     * Check if user can modify salary information
     */
    private function authorizeSalaryChanges(): string
    {
        if (!$this->user()->can('update employee salary')) {
            return 'prohibited';
        }

        return 'sometimes';
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'Nama depan wajib diisi.',
            'first_name.regex' => 'Nama depan hanya boleh berisi huruf, spasi, dan titik.',
            'last_name.required' => 'Nama belakang wajib diisi.',
            'last_name.regex' => 'Nama belakang hanya boleh berisi huruf, spasi, dan titik.',
            'date_of_birth.required' => 'Tanggal lahir wajib diisi.',
            'date_of_birth.before' => 'Tanggal lahir harus sebelum hari ini.',
            'date_of_birth.after' => 'Tanggal lahir tidak valid.',
            'gender.required' => 'Jenis kelamin wajib dipilih.',
            'gender.in' => 'Jenis kelamin tidak valid.',
            'national_id.required' => 'NIK wajib diisi.',
            'national_id.size' => 'NIK harus terdiri dari 16 digit.',
            'national_id.regex' => 'Format NIK tidak valid.',
            'national_id.unique' => 'NIK sudah terdaftar.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'phone_number.required' => 'Nomor telepon wajib diisi.',
            'phone_number.regex' => 'Format nomor telepon tidak valid.',
            'address.required' => 'Alamat wajib diisi.',
            'city.required' => 'Kota wajib diisi.',
            'province.required' => 'Provinsi wajib diisi.',
            'postal_code.required' => 'Kode pos wajib diisi.',
            'postal_code.regex' => 'Format kode pos tidak valid.',
            'emergency_contact_name.required' => 'Nama kontak darurat wajib diisi.',
            'emergency_contact_phone.required' => 'Nomor kontak darurat wajib diisi.',
            'emergency_contact_phone.regex' => 'Format nomor kontak darurat tidak valid.',
            'emergency_contact_relationship.required' => 'Hubungan kontak darurat wajib diisi.',
            'hire_date.required' => 'Tanggal masuk kerja wajib diisi.',
            'hire_date.before_or_equal' => 'Tanggal masuk kerja tidak boleh di masa depan.',
            'employment_status.required' => 'Status kepegawaian wajib dipilih.',
            'employment_status.in' => 'Status kepegawaian tidak valid.',
            'employment_status.prohibited' => 'Anda tidak memiliki izin untuk mengubah status kepegawaian.',
            'department_id.required' => 'Departemen wajib dipilih.',
            'department_id.exists' => 'Departemen tidak ditemukan.',
            'department_id.prohibited' => 'Anda tidak memiliki izin untuk mengubah departemen.',
            'position_id.required' => 'Posisi wajib dipilih.',
            'position_id.exists' => 'Posisi tidak ditemukan.',
            'position_id.prohibited' => 'Anda tidak memiliki izin untuk mengubah posisi.',
            'employee_type_id.required' => 'Tipe karyawan wajib dipilih.',
            'employee_type_id.exists' => 'Tipe karyawan tidak ditemukan.',
            'employee_type_id.prohibited' => 'Anda tidak memiliki izin untuk mengubah tipe karyawan.',
            'supervisor_id.exists' => 'Supervisor tidak ditemukan.',
            'supervisor_id.different' => 'Karyawan tidak dapat menjadi supervisor diri sendiri.',
            'supervisor_id.prohibited' => 'Anda tidak memiliki izin untuk mengubah supervisor.',
            'basic_salary.numeric' => 'Gaji pokok harus berupa angka.',
            'basic_salary.min' => 'Gaji pokok tidak boleh negatif.',
            'basic_salary.prohibited' => 'Anda tidak memiliki izin untuk mengubah gaji.',
            'salary_grade.prohibited' => 'Anda tidak memiliki izin untuk mengubah grade gaji.',
            'profile_photo.image' => 'File foto profil harus berupa gambar.',
            'profile_photo.mimes' => 'Foto profil harus berformat JPEG, PNG, atau JPG.',
            'profile_photo.max' => 'Ukuran foto profil maksimal 2MB.',
            'documents.max' => 'Maksimal 5 dokumen dapat diunggah.',
            'documents.*.file' => 'File dokumen tidak valid.',
            'documents.*.mimes' => 'Format dokumen harus PDF, DOC, DOCX, JPG, JPEG, atau PNG.',
            'documents.*.max' => 'Ukuran dokumen maksimal 5MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'nama depan',
            'last_name' => 'nama belakang',
            'date_of_birth' => 'tanggal lahir',
            'gender' => 'jenis kelamin',
            'national_id' => 'NIK',
            'email' => 'email',
            'phone_number' => 'nomor telepon',
            'address' => 'alamat',
            'city' => 'kota',
            'province' => 'provinsi',
            'postal_code' => 'kode pos',
            'emergency_contact_name' => 'nama kontak darurat',
            'emergency_contact_phone' => 'nomor kontak darurat',
            'emergency_contact_relationship' => 'hubungan kontak darurat',
            'hire_date' => 'tanggal masuk kerja',
            'employment_status' => 'status kepegawaian',
            'department_id' => 'departemen',
            'position_id' => 'posisi',
            'employee_type_id' => 'tipe karyawan',
            'supervisor_id' => 'supervisor',
            'salary_grade' => 'grade gaji',
            'basic_salary' => 'gaji pokok',
            'profile_photo' => 'foto profil',
            'documents' => 'dokumen',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Http\Exceptions\HttpResponseException(
            response()->json([
                'message' => 'Data yang diberikan tidak valid.',
                'errors' => $validator->errors(),
            ], 422)
        );
    }
}
```

---

## Step 2: Create Employee Resource

### 2.1 Create Employee API Resource

```bash
php artisan make:resource EmployeeResource
```

Edit `app/Http/Resources/EmployeeResource.php`:

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'employee_number' => $this->employee_number,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'date_of_birth' => $this->date_of_birth?->format('Y-m-d'),
            'age' => $this->age,
            'gender' => $this->gender,
            'indonesian_gender' => $this->indonesian_gender,
            'phone_number' => $this->phone_number,
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'address' => $this->address,
            'city' => $this->city,
            'province' => $this->province,
            'postal_code' => $this->postal_code,
            'hire_date' => $this->hire_date?->format('Y-m-d'),
            'years_of_service' => $this->years_of_service,
            'employment_status' => $this->employment_status,
            'indonesian_employment_status' => $this->indonesian_employment_status,
            'salary_grade' => $this->salary_grade,
            'basic_salary' => $this->basic_salary,
            'formatted_salary' => $this->formatted_salary,
            'profile_photo_path' => $this->profile_photo_path,
            'profile_photo_url' => $this->profile_photo_path
                ? asset('storage/' . $this->profile_photo_path)
                : null,

            // Relationships
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                    'email_verified_at' => $this->user->email_verified_at,
                    'roles' => $this->user->getRoleNames(),
                ];
            }),

            'employee_type' => $this->whenLoaded('employeeType', function () {
                return [
                    'id' => $this->employeeType->id,
                    'name' => $this->employeeType->name,
                    'code' => $this->employeeType->code,
                    'indonesian_name' => $this->employeeType->indonesian_name,
                    'is_medical_staff' => $this->employeeType->is_medical_staff,
                    'requires_license' => $this->employeeType->requires_license,
                ];
            }),

            'department' => $this->whenLoaded('department', function () {
                return [
                    'id' => $this->department->id,
                    'name' => $this->department->name,
                    'code' => $this->department->code,
                    'location' => $this->department->location,
                    'full_path' => $this->department->full_path,
                ];
            }),

            'position' => $this->whenLoaded('position', function () {
                return [
                    'id' => $this->position->id,
                    'title' => $this->position->title,
                    'level' => $this->position->level,
                    'indonesian_level' => $this->position->indonesian_level,
                    'salary_range' => $this->position->salary_range,
                ];
            }),

            'supervisor' => $this->whenLoaded('supervisor', function () {
                return $this->supervisor ? [
                    'id' => $this->supervisor->id,
                    'employee_number' => $this->supervisor->employee_number,
                    'full_name' => $this->supervisor->full_name,
                    'position' => $this->supervisor->position?->title,
                ] : null;
            }),

            'subordinates' => $this->whenLoaded('subordinates', function () {
                return $this->subordinates->map(function ($subordinate) {
                    return [
                        'id' => $subordinate->id,
                        'employee_number' => $subordinate->employee_number,
                        'full_name' => $subordinate->full_name,
                        'position' => $subordinate->position?->title,
                        'employment_status' => $subordinate->employment_status,
                    ];
                });
            }),

            'licenses' => $this->whenLoaded('licenses', function () {
                return $this->licenses->map(function ($license) {
                    return [
                        'id' => $license->id,
                        'license_type' => $license->license_type,
                        'indonesian_license_type' => $license->indonesian_license_type,
                        'license_number' => $license->license_number,
                        'issuing_authority' => $license->issuing_authority,
                        'issue_date' => $license->issue_date?->format('Y-m-d'),
                        'expiry_date' => $license->expiry_date?->format('Y-m-d'),
                        'status' => $license->status,
                        'indonesian_status' => $license->indonesian_status,
                        'days_until_expiry' => $license->days_until_expiry,
                        'is_expired' => $license->isExpired(),
                        'is_expiring_soon' => $license->isExpiringSoon(),
                    ];
                });
            }),

            'recent_shifts' => $this->whenLoaded('employeeShifts', function () {
                return $this->employeeShifts->map(function ($employeeShift) {
                    return [
                        'id' => $employeeShift->id,
                        'date' => $employeeShift->date?->format('Y-m-d'),
                        'shift_name' => $employeeShift->shift?->name,
                        'shift_time_range' => $employeeShift->shift?->time_range,
                        'status' => $employeeShift->status,
                        'indonesian_status' => $employeeShift->indonesian_status,
                        'check_in_time' => $employeeShift->check_in_time?->format('H:i'),
                        'check_out_time' => $employeeShift->check_out_time?->format('H:i'),
                        'total_working_hours' => $employeeShift->total_working_hours,
                    ];
                });
            }),

            'recent_leave_requests' => $this->whenLoaded('leaveRequests', function () {
                return $this->leaveRequests->map(function ($leaveRequest) {
                    return [
                        'id' => $leaveRequest->id,
                        'leave_type' => $leaveRequest->leaveType?->indonesian_name,
                        'start_date' => $leaveRequest->start_date?->format('Y-m-d'),
                        'end_date' => $leaveRequest->end_date?->format('Y-m-d'),
                        'total_days' => $leaveRequest->total_days,
                        'status' => $leaveRequest->status,
                        'indonesian_status' => $leaveRequest->indonesian_status,
                        'reason' => $leaveRequest->reason,
                    ];
                });
            }),

            // Computed attributes
            'is_active' => $this->isActive(),
            'is_medical_staff' => $this->isMedicalStaff(),
            'is_supervisor' => $this->isSupervisor(),
            'requires_license' => $this->requiresLicense(),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
```

---

## Step 3: Create Form Request Classes

### 3.1 Create Store Employee Request

```bash
php artisan make:request StoreEmployeeRequest
```

Edit `app/Http/Requests/StoreEmployeeRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEmployeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create employees');
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'gender' => ['required', Rule::in(['male', 'female'])],
            'phone_number' => ['required', 'string', 'max:20', 'unique:employees,phone_number'],
            'emergency_contact_name' => ['required', 'string', 'max:255'],
            'emergency_contact_phone' => ['required', 'string', 'max:20'],
            'address' => ['required', 'string'],
            'city' => ['required', 'string', 'max:100'],
            'province' => ['required', 'string', 'max:100'],
            'postal_code' => ['required', 'string', 'max:10'],
            'hire_date' => ['required', 'date'],
            'employment_status' => ['required', Rule::in(['active', 'inactive', 'terminated', 'resigned'])],
            'salary_grade' => ['nullable', 'integer', 'min:1', 'max:20'],
            'basic_salary' => ['nullable', 'numeric', 'min:0'],
            'employee_type_id' => ['required', 'exists:employee_types,id'],
            'department_id' => ['required', 'exists:departments,id'],
            'position_id' => ['required', 'exists:positions,id'],
            'supervisor_id' => ['nullable', 'exists:employees,id'],
            'profile_photo' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],

            // User account fields (optional)
            'email' => ['nullable', 'email', 'unique:users,email'],
            'password' => ['nullable', 'string', 'min:8'],
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'Nama depan wajib diisi',
            'last_name.required' => 'Nama belakang wajib diisi',
            'date_of_birth.required' => 'Tanggal lahir wajib diisi',
            'date_of_birth.before' => 'Tanggal lahir harus sebelum hari ini',
            'gender.required' => 'Jenis kelamin wajib dipilih',
            'gender.in' => 'Jenis kelamin tidak valid',
            'phone_number.required' => 'Nomor telepon wajib diisi',
            'phone_number.unique' => 'Nomor telepon sudah terdaftar',
            'emergency_contact_name.required' => 'Nama kontak darurat wajib diisi',
            'emergency_contact_phone.required' => 'Nomor kontak darurat wajib diisi',
            'address.required' => 'Alamat wajib diisi',
            'city.required' => 'Kota wajib diisi',
            'province.required' => 'Provinsi wajib diisi',
            'postal_code.required' => 'Kode pos wajib diisi',
            'hire_date.required' => 'Tanggal bergabung wajib diisi',
            'employment_status.required' => 'Status kepegawaian wajib dipilih',
            'employment_status.in' => 'Status kepegawaian tidak valid',
            'employee_type_id.required' => 'Jenis karyawan wajib dipilih',
            'employee_type_id.exists' => 'Jenis karyawan tidak valid',
            'department_id.required' => 'Departemen wajib dipilih',
            'department_id.exists' => 'Departemen tidak valid',
            'position_id.required' => 'Posisi wajib dipilih',
            'position_id.exists' => 'Posisi tidak valid',
            'supervisor_id.exists' => 'Supervisor tidak valid',
            'profile_photo.image' => 'File harus berupa gambar',
            'profile_photo.mimes' => 'Format gambar harus JPEG, PNG, atau JPG',
            'profile_photo.max' => 'Ukuran gambar maksimal 2MB',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah terdaftar',
            'password.min' => 'Password minimal 8 karakter',
        ];
    }

    protected function prepareForValidation(): void
    {
        // Normalize phone numbers
        if ($this->phone_number) {
            $phone = preg_replace('/[^0-9]/', '', $this->phone_number);
            if (str_starts_with($phone, '0')) {
                $phone = '62' . substr($phone, 1);
            }
            $this->merge(['phone_number' => $phone]);
        }

        if ($this->emergency_contact_phone) {
            $phone = preg_replace('/[^0-9]/', '', $this->emergency_contact_phone);
            if (str_starts_with($phone, '0')) {
                $phone = '62' . substr($phone, 1);
            }
            $this->merge(['emergency_contact_phone' => $phone]);
        }
    }
}
```

### 3.2 Create Update Employee Request

```bash
php artisan make:request UpdateEmployeeRequest
```

Edit `app/Http/Requests/UpdateEmployeeRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmployeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('edit employees');
    }

    public function rules(): array
    {
        $employeeId = $this->route('employee')->id;

        return [
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'gender' => ['required', Rule::in(['male', 'female'])],
            'phone_number' => [
                'required',
                'string',
                'max:20',
                Rule::unique('employees', 'phone_number')->ignore($employeeId)
            ],
            'emergency_contact_name' => ['required', 'string', 'max:255'],
            'emergency_contact_phone' => ['required', 'string', 'max:20'],
            'address' => ['required', 'string'],
            'city' => ['required', 'string', 'max:100'],
            'province' => ['required', 'string', 'max:100'],
            'postal_code' => ['required', 'string', 'max:10'],
            'hire_date' => ['required', 'date'],
            'employment_status' => ['required', Rule::in(['active', 'inactive', 'terminated', 'resigned'])],
            'salary_grade' => ['nullable', 'integer', 'min:1', 'max:20'],
            'basic_salary' => ['nullable', 'numeric', 'min:0'],
            'employee_type_id' => ['required', 'exists:employee_types,id'],
            'department_id' => ['required', 'exists:departments,id'],
            'position_id' => ['required', 'exists:positions,id'],
            'supervisor_id' => [
                'nullable',
                'exists:employees,id',
                Rule::notIn([$employeeId]) // Prevent self-supervision
            ],
            'profile_photo' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],

            // User account fields (optional)
            'email' => [
                'nullable',
                'email',
                Rule::unique('users', 'email')->ignore($this->route('employee')->user_id ?? null)
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'Nama depan wajib diisi',
            'last_name.required' => 'Nama belakang wajib diisi',
            'date_of_birth.required' => 'Tanggal lahir wajib diisi',
            'date_of_birth.before' => 'Tanggal lahir harus sebelum hari ini',
            'gender.required' => 'Jenis kelamin wajib dipilih',
            'gender.in' => 'Jenis kelamin tidak valid',
            'phone_number.required' => 'Nomor telepon wajib diisi',
            'phone_number.unique' => 'Nomor telepon sudah terdaftar',
            'emergency_contact_name.required' => 'Nama kontak darurat wajib diisi',
            'emergency_contact_phone.required' => 'Nomor kontak darurat wajib diisi',
            'address.required' => 'Alamat wajib diisi',
            'city.required' => 'Kota wajib diisi',
            'province.required' => 'Provinsi wajib diisi',
            'postal_code.required' => 'Kode pos wajib diisi',
            'hire_date.required' => 'Tanggal bergabung wajib diisi',
            'employment_status.required' => 'Status kepegawaian wajib dipilih',
            'employment_status.in' => 'Status kepegawaian tidak valid',
            'employee_type_id.required' => 'Jenis karyawan wajib dipilih',
            'employee_type_id.exists' => 'Jenis karyawan tidak valid',
            'department_id.required' => 'Departemen wajib dipilih',
            'department_id.exists' => 'Departemen tidak valid',
            'position_id.required' => 'Posisi wajib dipilih',
            'position_id.exists' => 'Posisi tidak valid',
            'supervisor_id.exists' => 'Supervisor tidak valid',
            'supervisor_id.not_in' => 'Karyawan tidak dapat menjadi supervisor untuk dirinya sendiri',
            'profile_photo.image' => 'File harus berupa gambar',
            'profile_photo.mimes' => 'Format gambar harus JPEG, PNG, atau JPG',
            'profile_photo.max' => 'Ukuran gambar maksimal 2MB',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah terdaftar',
        ];
    }

    protected function prepareForValidation(): void
    {
        // Normalize phone numbers
        if ($this->phone_number) {
            $phone = preg_replace('/[^0-9]/', '', $this->phone_number);
            if (str_starts_with($phone, '0')) {
                $phone = '62' . substr($phone, 1);
            }
            $this->merge(['phone_number' => $phone]);
        }

        if ($this->emergency_contact_phone) {
            $phone = preg_replace('/[^0-9]/', '', $this->emergency_contact_phone);
            if (str_starts_with($phone, '0')) {
                $phone = '62' . substr($phone, 1);
            }
            $this->merge(['emergency_contact_phone' => $phone]);
        }
    }
}
```

---

## Step 4: Update API Routes

### 4.1 Update Employee Routes

Edit `routes/api.php` to add employee routes:

```php
// Employee routes with department access control
Route::middleware(['auth:sanctum', 'department.access'])->group(function () {
    // Employee CRUD operations
    Route::apiResource('employees', EmployeeController::class);

    // Employee bulk operations
    Route::post('/employees/bulk-action', [EmployeeController::class, 'bulkAction'])
         ->middleware('permission:manage employees');

    // Employee statistics
    Route::get('/employees-statistics', [EmployeeController::class, 'statistics'])
         ->middleware('permission:view employees');

    // Employee export
    Route::get('/employees/export/{ids}', [EmployeeController::class, 'export'])
         ->middleware('permission:export employees');
});
```

---

## Step 4: Create Employee Service Layer

### 4.1 Create Employee Service

```bash
php artisan make:service EmployeeService
```

Create `app/Services/EmployeeService.php`:

```php
<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\User;
use App\Models\Department;
use App\Models\Position;
use App\Models\EmployeeType;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class EmployeeService
{
    /**
     * Create a new employee with user account
     */
    public function createEmployee(array $data): Employee
    {
        return DB::transaction(function () use ($data) {
            // Generate employee number
            $data['employee_number'] = $this->generateEmployeeNumber($data['department_id']);

            // Handle profile photo upload
            if (isset($data['profile_photo']) && $data['profile_photo'] instanceof UploadedFile) {
                $data['profile_photo_path'] = $this->handleProfilePhotoUpload($data['profile_photo']);
                unset($data['profile_photo']);
            }

            // Normalize phone numbers
            $data['phone_number'] = $this->normalizeIndonesianPhoneNumber($data['phone_number']);
            $data['emergency_contact_phone'] = $this->normalizeIndonesianPhoneNumber($data['emergency_contact_phone']);

            // Create user account if email is provided
            $user = null;
            if (isset($data['email'])) {
                $user = $this->createUserAccount($data);
                $data['user_id'] = $user->id;
                unset($data['email']);
            }

            // Create employee
            $employee = Employee::create($data);

            // Assign default role to user
            if ($user) {
                $this->assignDefaultRole($user, $employee);
            }

            // Handle document uploads
            if (isset($data['documents'])) {
                $this->handleDocumentUploads($employee, $data['documents']);
            }

            // Log employee creation
            activity()
                ->performedOn($employee)
                ->causedBy(auth()->user())
                ->withProperties(['action' => 'created'])
                ->log('Employee created: ' . $employee->full_name);

            return $employee->load(['user', 'department', 'position', 'employeeType']);
        });
    }

    /**
     * Update employee information
     */
    public function updateEmployee(Employee $employee, array $data): Employee
    {
        return DB::transaction(function () use ($employee, $data) {
            // Handle profile photo upload
            if (isset($data['profile_photo']) && $data['profile_photo'] instanceof UploadedFile) {
                // Delete old photo
                if ($employee->profile_photo_path) {
                    Storage::disk('public')->delete($employee->profile_photo_path);
                }
                $data['profile_photo_path'] = $this->handleProfilePhotoUpload($data['profile_photo']);
                unset($data['profile_photo']);
            }

            // Normalize phone numbers
            if (isset($data['phone_number'])) {
                $data['phone_number'] = $this->normalizeIndonesianPhoneNumber($data['phone_number']);
            }
            if (isset($data['emergency_contact_phone'])) {
                $data['emergency_contact_phone'] = $this->normalizeIndonesianPhoneNumber($data['emergency_contact_phone']);
            }

            // Update user account if email changed
            if (isset($data['email']) && $employee->user) {
                $employee->user->update([
                    'name' => ($data['first_name'] ?? $employee->first_name) . ' ' . ($data['last_name'] ?? $employee->last_name),
                    'email' => $data['email'],
                ]);
                unset($data['email']);
            }

            // Track changes for audit
            $originalData = $employee->toArray();

            // Update employee
            $employee->update($data);

            // Handle document uploads
            if (isset($data['documents'])) {
                $this->handleDocumentUploads($employee, $data['documents']);
            }

            // Log employee update
            $changes = array_diff_assoc($employee->toArray(), $originalData);
            if (!empty($changes)) {
                activity()
                    ->performedOn($employee)
                    ->causedBy(auth()->user())
                    ->withProperties([
                        'action' => 'updated',
                        'changes' => $changes,
                        'original' => $originalData
                    ])
                    ->log('Employee updated: ' . $employee->full_name);
            }

            return $employee->load(['user', 'department', 'position', 'employeeType']);
        });
    }

    /**
     * Soft delete employee
     */
    public function deleteEmployee(Employee $employee): bool
    {
        return DB::transaction(function () use ($employee) {
            // Check if employee has subordinates
            if ($employee->subordinates()->exists()) {
                throw new \Exception('Cannot delete employee with subordinates. Please reassign subordinates first.');
            }

            // Deactivate user account
            if ($employee->user) {
                $employee->user->update([
                    'email_verified_at' => null,
                    'password' => Hash::make(Str::random(32)), // Invalidate password
                ]);
            }

            // Log employee deletion
            activity()
                ->performedOn($employee)
                ->causedBy(auth()->user())
                ->withProperties(['action' => 'deleted'])
                ->log('Employee deleted: ' . $employee->full_name);

            // Soft delete employee
            return $employee->delete();
        });
    }

    /**
     * Generate unique employee number
     */
    private function generateEmployeeNumber(int $departmentId): string
    {
        $department = Department::find($departmentId);
        $departmentCode = $department ? $department->code : 'GEN';

        $year = date('Y');
        $month = date('m');

        // Get last employee number for this department and month
        $lastEmployee = Employee::where('employee_number', 'like', "{$departmentCode}{$year}{$month}%")
                               ->orderBy('employee_number', 'desc')
                               ->first();

        if ($lastEmployee) {
            $lastNumber = (int) substr($lastEmployee->employee_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $departmentCode . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Create user account for employee
     */
    private function createUserAccount(array $data): User
    {
        $userData = [
            'name' => $data['first_name'] . ' ' . $data['last_name'],
            'email' => $data['email'],
            'password' => Hash::make('password123'), // Default password
            'email_verified_at' => now(),
        ];

        return User::create($userData);
    }

    /**
     * Assign default role based on employee type and position
     */
    private function assignDefaultRole(User $user, Employee $employee): void
    {
        $employeeType = $employee->employeeType;
        $position = $employee->position;

        // Determine role based on employee type and position
        if ($employeeType && $employeeType->is_medical_staff) {
            if (str_contains(strtolower($position->title ?? ''), 'kepala') ||
                str_contains(strtolower($position->title ?? ''), 'direktur')) {
                $user->assignRole('Department Head');
            } else {
                $user->assignRole('Medical Staff');
            }
        } elseif ($employeeType && $employeeType->name === 'Administrative') {
            if (str_contains(strtolower($position->title ?? ''), 'manager') ||
                str_contains(strtolower($position->title ?? ''), 'kepala')) {
                $user->assignRole('Department Head');
            } else {
                $user->assignRole('Staff');
            }
        } else {
            $user->assignRole('Staff');
        }
    }

    /**
     * Handle profile photo upload
     */
    private function handleProfilePhotoUpload(UploadedFile $file): string
    {
        // Validate file
        $this->validateProfilePhoto($file);

        // Generate unique filename
        $filename = 'profile_photos/' . Str::uuid() . '.' . $file->getClientOriginalExtension();

        // Store file
        $path = $file->storeAs('public', $filename);

        return str_replace('public/', '', $path);
    }

    /**
     * Validate profile photo
     */
    private function validateProfilePhoto(UploadedFile $file): void
    {
        // Check file size (max 2MB)
        if ($file->getSize() > 2048 * 1024) {
            throw new \Exception('Profile photo size must be less than 2MB.');
        }

        // Check file type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \Exception('Profile photo must be JPEG, PNG, or JPG format.');
        }

        // Check image dimensions
        $imageInfo = getimagesize($file->getPathname());
        if ($imageInfo === false) {
            throw new \Exception('Invalid image file.');
        }

        [$width, $height] = $imageInfo;
        if ($width < 100 || $height < 100) {
            throw new \Exception('Profile photo must be at least 100x100 pixels.');
        }

        if ($width > 2000 || $height > 2000) {
            throw new \Exception('Profile photo must be less than 2000x2000 pixels.');
        }
    }

    /**
     * Handle document uploads
     */
    private function handleDocumentUploads(Employee $employee, array $documents): void
    {
        foreach ($documents as $document) {
            if ($document instanceof UploadedFile) {
                $this->validateDocument($document);

                $filename = 'employee_documents/' . $employee->id . '/' . Str::uuid() . '.' . $document->getClientOriginalExtension();
                $path = $document->storeAs('public', $filename);

                // Store document information (you might want to create a documents table)
                // For now, we'll just log it
                activity()
                    ->performedOn($employee)
                    ->causedBy(auth()->user())
                    ->withProperties([
                        'action' => 'document_uploaded',
                        'filename' => $document->getClientOriginalName(),
                        'path' => $path
                    ])
                    ->log('Document uploaded for employee: ' . $employee->full_name);
            }
        }
    }

    /**
     * Validate document upload
     */
    private function validateDocument(UploadedFile $file): void
    {
        // Check file size (max 5MB)
        if ($file->getSize() > 5120 * 1024) {
            throw new \Exception('Document size must be less than 5MB.');
        }

        // Check file type
        $allowedMimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/jpg'
        ];

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \Exception('Document must be PDF, DOC, DOCX, JPEG, PNG, or JPG format.');
        }
    }

    /**
     * Normalize Indonesian phone number
     */
    private function normalizeIndonesianPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Handle different Indonesian phone number formats
        if (str_starts_with($cleaned, '0')) {
            // Convert 08xx to 628xx
            return '62' . substr($cleaned, 1);
        } elseif (str_starts_with($cleaned, '8')) {
            // Convert 8xx to 628xx
            return '62' . $cleaned;
        } elseif (str_starts_with($cleaned, '62')) {
            // Already in correct format
            return $cleaned;
        }

        // If none of the above, assume it's already correct or invalid
        return $cleaned;
    }

    /**
     * Get employee statistics
     */
    public function getEmployeeStatistics(): array
    {
        return [
            'total_employees' => Employee::count(),
            'active_employees' => Employee::where('employment_status', 'active')->count(),
            'inactive_employees' => Employee::where('employment_status', 'inactive')->count(),
            'medical_staff' => Employee::whereHas('employeeType', function ($query) {
                $query->where('is_medical_staff', true);
            })->count(),
            'administrative_staff' => Employee::whereHas('employeeType', function ($query) {
                $query->where('is_medical_staff', false);
            })->count(),
            'employees_by_department' => Employee::select('department_id')
                ->with('department:id,name')
                ->groupBy('department_id')
                ->selectRaw('department_id, count(*) as count')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->department->name ?? 'Unknown' => $item->count];
                }),
            'recent_hires' => Employee::where('hire_date', '>=', Carbon::now()->subDays(30))->count(),
            'upcoming_birthdays' => Employee::whereRaw('DATE_FORMAT(date_of_birth, "%m-%d") BETWEEN ? AND ?', [
                Carbon::now()->format('m-d'),
                Carbon::now()->addDays(7)->format('m-d')
            ])->count(),
        ];
    }

    /**
     * Search employees with advanced filters
     */
    public function searchEmployees(array $filters): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = Employee::with(['user', 'department', 'position', 'employeeType', 'supervisor']);

        // Apply search term
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('employee_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%");
                  });
            });
        }

        // Apply department filter
        if (!empty($filters['department_id'])) {
            $query->where('department_id', $filters['department_id']);
        }

        // Apply employment status filter
        if (!empty($filters['employment_status'])) {
            $query->where('employment_status', $filters['employment_status']);
        }

        // Apply employee type filter
        if (!empty($filters['employee_type_id'])) {
            $query->where('employee_type_id', $filters['employee_type_id']);
        }

        // Apply position filter
        if (!empty($filters['position_id'])) {
            $query->where('position_id', $filters['position_id']);
        }

        // Apply supervisor filter
        if (!empty($filters['supervisor_id'])) {
            $query->where('supervisor_id', $filters['supervisor_id']);
        }

        // Apply date range filters
        if (!empty($filters['hire_date_from'])) {
            $query->where('hire_date', '>=', $filters['hire_date_from']);
        }

        if (!empty($filters['hire_date_to'])) {
            $query->where('hire_date', '<=', $filters['hire_date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        // Return paginated results
        $perPage = min($filters['per_page'] ?? 15, 100);
        return $query->paginate($perPage);
    }

    /**
     * Bulk update employees
     */
    public function bulkUpdateEmployees(array $employeeIds, array $data): int
    {
        return DB::transaction(function () use ($employeeIds, $data) {
            $updatedCount = Employee::whereIn('id', $employeeIds)->update($data);

            // Log bulk update
            activity()
                ->causedBy(auth()->user())
                ->withProperties([
                    'action' => 'bulk_update',
                    'employee_ids' => $employeeIds,
                    'data' => $data,
                    'updated_count' => $updatedCount
                ])
                ->log('Bulk update performed on ' . $updatedCount . ' employees');

            return $updatedCount;
        });
    }

    /**
     * Export employees data
     */
    public function exportEmployees(array $employeeIds = []): array
    {
        $query = Employee::with(['user', 'department', 'position', 'employeeType', 'supervisor']);

        if (!empty($employeeIds)) {
            $query->whereIn('id', $employeeIds);
        }

        $employees = $query->get();

        return $employees->map(function ($employee) {
            return [
                'Employee Number' => $employee->employee_number,
                'Full Name' => $employee->full_name,
                'Email' => $employee->user?->email,
                'Phone Number' => $employee->phone_number,
                'Department' => $employee->department?->name,
                'Position' => $employee->position?->title,
                'Employee Type' => $employee->employeeType?->name,
                'Employment Status' => $employee->indonesian_employment_status,
                'Hire Date' => $employee->hire_date?->format('d/m/Y'),
                'Years of Service' => $employee->years_of_service,
                'Supervisor' => $employee->supervisor?->full_name,
                'Address' => $employee->address,
                'City' => $employee->city,
                'Province' => $employee->province,
                'Emergency Contact' => $employee->emergency_contact_name,
                'Emergency Phone' => $employee->emergency_contact_phone,
            ];
        })->toArray();
    }
}
```

---

## Step 5: Create React Components

### 5.1 Create Employee List Component

Create `resources/js/components/employees/EmployeeList.tsx`:

```tsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Plus,
  Filter,
  Download,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Users,
  UserCheck,
  UserX,
  Loader2
} from 'lucide-react';
import axios from 'axios';

interface Employee {
  id: number;
  employee_number: string;
  full_name: string;
  employee_type: {
    name: string;
    indonesian_name: string;
    is_medical_staff: boolean;
  };
  department: {
    name: string;
    location: string;
  };
  position: {
    title: string;
    level: string;
  };
  employment_status: string;
  indonesian_employment_status: string;
  hire_date: string;
  years_of_service: number;
  profile_photo_url?: string;
  phone_number: string;
  is_active: boolean;
  is_medical_staff: boolean;
}

interface EmployeeFilters {
  search: string;
  department_id: string;
  employee_type_id: string;
  employment_status: string;
  is_medical_staff: string;
}

const EmployeeList: React.FC = () => {
  const { hasPermission } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [filters, setFilters] = useState<EmployeeFilters>({
    search: '',
    department_id: '',
    employee_type_id: '',
    employment_status: '',
    is_medical_staff: '',
  });
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
  });
  const [departments, setDepartments] = useState([]);
  const [employeeTypes, setEmployeeTypes] = useState([]);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  useEffect(() => {
    fetchEmployees();
    fetchFilterOptions();
  }, [filters, pagination.current_page]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.current_page.toString(),
        per_page: pagination.per_page.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        ),
      });

      const response = await axios.get(`/api/employees?${params}`);
      setEmployees(response.data.data);
      setPagination(response.data.meta);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Gagal memuat data karyawan');
    } finally {
      setLoading(false);
    }
  };

  const fetchFilterOptions = async () => {
    try {
      const [deptResponse, typeResponse] = await Promise.all([
        axios.get('/api/departments'),
        axios.get('/api/employee-types'),
      ]);
      setDepartments(deptResponse.data.data);
      setEmployeeTypes(typeResponse.data.data);
    } catch (err) {
      console.error('Failed to fetch filter options:', err);
    }
  };

  const handleFilterChange = (key: keyof EmployeeFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, current_page: 1 }));
  };

  const handleSelectEmployee = (employeeId: number, checked: boolean) => {
    if (checked) {
      setSelectedEmployees(prev => [...prev, employeeId]);
    } else {
      setSelectedEmployees(prev => prev.filter(id => id !== employeeId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmployees(employees.map(emp => emp.id));
    } else {
      setSelectedEmployees([]);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedEmployees.length === 0) {
      alert('Pilih karyawan terlebih dahulu');
      return;
    }

    if (!confirm(`Apakah Anda yakin ingin ${action} ${selectedEmployees.length} karyawan?`)) {
      return;
    }

    try {
      setBulkActionLoading(true);
      await axios.post('/api/employees/bulk-action', {
        action,
        employee_ids: selectedEmployees,
      });

      setSelectedEmployees([]);
      fetchEmployees();
      alert(`Berhasil ${action} ${selectedEmployees.length} karyawan`);
    } catch (err: any) {
      alert(err.response?.data?.message || `Gagal ${action} karyawan`);
    } finally {
      setBulkActionLoading(false);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'inactive': return 'secondary';
      case 'terminated': return 'destructive';
      case 'resigned': return 'outline';
      default: return 'secondary';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  if (loading && employees.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Memuat data karyawan...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Manajemen Karyawan
              </CardTitle>
              <CardDescription>
                Kelola data karyawan rumah sakit
              </CardDescription>
            </div>
            {hasPermission('create employees') && (
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Karyawan
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari karyawan..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.department_id}
              onValueChange={(value) => handleFilterChange('department_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Semua Departemen" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Departemen</SelectItem>
                {departments.map((dept: any) => (
                  <SelectItem key={dept.id} value={dept.id.toString()}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.employee_type_id}
              onValueChange={(value) => handleFilterChange('employee_type_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Semua Jenis" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Jenis</SelectItem>
                {employeeTypes.map((type: any) => (
                  <SelectItem key={type.id} value={type.id.toString()}>
                    {type.indonesian_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.employment_status}
              onValueChange={(value) => handleFilterChange('employment_status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Semua Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Status</SelectItem>
                <SelectItem value="active">Aktif</SelectItem>
                <SelectItem value="inactive">Tidak Aktif</SelectItem>
                <SelectItem value="terminated">Diberhentikan</SelectItem>
                <SelectItem value="resigned">Mengundurkan Diri</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.is_medical_staff}
              onValueChange={(value) => handleFilterChange('is_medical_staff', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Semua Staff" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Staff</SelectItem>
                <SelectItem value="true">Tenaga Medis</SelectItem>
                <SelectItem value="false">Non-Medis</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedEmployees.length > 0 && hasPermission('manage employees') && (
            <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium">
                {selectedEmployees.length} karyawan dipilih
              </span>
              <div className="flex gap-2 ml-auto">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('activate')}
                  disabled={bulkActionLoading}
                >
                  <UserCheck className="h-4 w-4 mr-1" />
                  Aktifkan
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('deactivate')}
                  disabled={bulkActionLoading}
                >
                  <UserX className="h-4 w-4 mr-1" />
                  Nonaktifkan
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('export')}
                  disabled={bulkActionLoading}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Ekspor
                </Button>
                {hasPermission('delete employees') && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleBulkAction('delete')}
                    disabled={bulkActionLoading}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Hapus
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Employee Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedEmployees.length === employees.length && employees.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Karyawan</TableHead>
                  <TableHead>Jenis</TableHead>
                  <TableHead>Departemen</TableHead>
                  <TableHead>Posisi</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Masa Kerja</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {employees.map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedEmployees.includes(employee.id)}
                        onCheckedChange={(checked) =>
                          handleSelectEmployee(employee.id, checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={employee.profile_photo_url} />
                          <AvatarFallback>
                            {getInitials(employee.full_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{employee.full_name}</div>
                          <div className="text-sm text-gray-500">
                            {employee.employee_number}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {employee.employee_type.indonesian_name}
                        {employee.is_medical_staff && (
                          <Badge variant="secondary" className="text-xs">
                            Medis
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{employee.department.name}</div>
                        <div className="text-sm text-gray-500">
                          {employee.department.location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{employee.position.title}</TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(employee.employment_status)}>
                        {employee.indonesian_employment_status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {employee.years_of_service} tahun
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            Lihat Detail
                          </DropdownMenuItem>
                          {hasPermission('edit employees') && (
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {hasPermission('delete employees') && (
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Hapus
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.last_page > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Menampilkan {pagination.from || 0} - {pagination.to || 0} dari {pagination.total} karyawan
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({
                    ...prev,
                    current_page: Math.max(1, prev.current_page - 1)
                  }))}
                  disabled={pagination.current_page === 1 || loading}
                >
                  Sebelumnya
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({
                    ...prev,
                    current_page: Math.min(prev.last_page, prev.current_page + 1)
                  }))}
                  disabled={pagination.current_page === pagination.last_page || loading}
                >
                  Selanjutnya
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeList;
```

---

## Step 6: Testing Employee CRUD Operations

### 6.1 Create Employee Controller Tests

```bash
php artisan make:test EmployeeControllerTest
```

Edit `tests/Feature/EmployeeControllerTest.php`:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\EmployeeType;
use App\Models\Position;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EmployeeControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(\Database\Seeders\RolePermissionSeeder::class);
        Storage::fake('public');
    }

    public function test_can_list_employees(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('view employees');

        Employee::factory()->count(5)->create();

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'employee_number',
                            'full_name',
                            'employee_type',
                            'department',
                            'position',
                            'employment_status',
                        ]
                    ],
                    'meta',
                    'links',
                ]);
    }

    public function test_can_create_employee(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('create employees');

        $department = Department::factory()->create();
        $employeeType = EmployeeType::factory()->create();
        $position = Position::factory()->create();

        $employeeData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone_number' => '628123456789',
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '628987654321',
            'address' => 'Jl. Test No. 123',
            'city' => 'Jakarta',
            'province' => 'DKI Jakarta',
            'postal_code' => '12345',
            'hire_date' => '2024-01-01',
            'employment_status' => 'active',
            'employee_type_id' => $employeeType->id,
            'department_id' => $department->id,
            'position_id' => $position->id,
            'email' => '<EMAIL>',
        ];

        $response = $this->actingAs($user, 'sanctum')
                        ->postJson('/api/employees', $employeeData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'employee_number',
                        'full_name',
                    ]
                ]);

        $this->assertDatabaseHas('employees', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '628123456789',
        ]);
    }

    public function test_can_upload_profile_photo(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('create employees');

        $department = Department::factory()->create();
        $employeeType = EmployeeType::factory()->create();
        $position = Position::factory()->create();

        $file = UploadedFile::fake()->image('profile.jpg', 300, 300);

        $employeeData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone_number' => '628123456789',
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '628987654321',
            'address' => 'Jl. Test No. 123',
            'city' => 'Jakarta',
            'province' => 'DKI Jakarta',
            'postal_code' => '12345',
            'hire_date' => '2024-01-01',
            'employment_status' => 'active',
            'employee_type_id' => $employeeType->id,
            'department_id' => $department->id,
            'position_id' => $position->id,
            'profile_photo' => $file,
        ];

        $response = $this->actingAs($user, 'sanctum')
                        ->postJson('/api/employees', $employeeData);

        $response->assertStatus(201);

        $employee = Employee::latest()->first();
        $this->assertNotNull($employee->profile_photo_path);
        Storage::disk('public')->assertExists($employee->profile_photo_path);
    }

    public function test_can_update_employee(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('edit employees');

        $employee = Employee::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $updateData = [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'date_of_birth' => $employee->date_of_birth->format('Y-m-d'),
            'gender' => $employee->gender,
            'phone_number' => $employee->phone_number,
            'emergency_contact_name' => $employee->emergency_contact_name,
            'emergency_contact_phone' => $employee->emergency_contact_phone,
            'address' => $employee->address,
            'city' => $employee->city,
            'province' => $employee->province,
            'postal_code' => $employee->postal_code,
            'hire_date' => $employee->hire_date->format('Y-m-d'),
            'employment_status' => $employee->employment_status,
            'employee_type_id' => $employee->employee_type_id,
            'department_id' => $employee->department_id,
            'position_id' => $employee->position_id,
        ];

        $response = $this->actingAs($user, 'sanctum')
                        ->putJson("/api/employees/{$employee->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'data' => [
                        'id',
                        'full_name',
                    ]
                ]);

        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'first_name' => 'Jane',
            'last_name' => 'Smith',
        ]);
    }

    public function test_can_delete_employee(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('delete employees');

        $employee = Employee::factory()->create();

        $response = $this->actingAs($user, 'sanctum')
                        ->deleteJson("/api/employees/{$employee->id}");

        $response->assertStatus(200)
                ->assertJson(['message' => 'Karyawan berhasil dihapus']);

        $this->assertSoftDeleted('employees', ['id' => $employee->id]);
    }

    public function test_cannot_delete_employee_with_subordinates(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('delete employees');

        $supervisor = Employee::factory()->create();
        $subordinate = Employee::factory()->create(['supervisor_id' => $supervisor->id]);

        $response = $this->actingAs($user, 'sanctum')
                        ->deleteJson("/api/employees/{$supervisor->id}");

        $response->assertStatus(422)
                ->assertJsonFragment([
                    'message' => 'Tidak dapat menghapus karyawan yang memiliki bawahan. Silakan pindahkan bawahan terlebih dahulu.'
                ]);

        $this->assertDatabaseHas('employees', ['id' => $supervisor->id]);
    }

    public function test_can_perform_bulk_actions(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('manage employees');

        $employees = Employee::factory()->count(3)->create(['employment_status' => 'inactive']);
        $employeeIds = $employees->pluck('id')->toArray();

        $response = $this->actingAs($user, 'sanctum')
                        ->postJson('/api/employees/bulk-action', [
                            'action' => 'activate',
                            'employee_ids' => $employeeIds,
                        ]);

        $response->assertStatus(200)
                ->assertJsonFragment(['updated_count' => 3]);

        foreach ($employees as $employee) {
            $this->assertDatabaseHas('employees', [
                'id' => $employee->id,
                'employment_status' => 'active',
            ]);
        }
    }

    public function test_can_search_employees(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('view employees');

        Employee::factory()->create(['first_name' => 'John', 'last_name' => 'Doe']);
        Employee::factory()->create(['first_name' => 'Jane', 'last_name' => 'Smith']);

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees?search=John');

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('John Doe', $response->json('data.0.full_name'));
    }

    public function test_can_filter_employees_by_department(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('view employees');

        $department1 = Department::factory()->create();
        $department2 = Department::factory()->create();

        Employee::factory()->create(['department_id' => $department1->id]);
        Employee::factory()->create(['department_id' => $department2->id]);

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson("/api/employees?department_id={$department1->id}");

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($department1->id, $response->json('data.0.department.id'));
    }

    public function test_unauthorized_user_cannot_access_employees(): void
    {
        $user = User::factory()->create();
        // User has no permissions

        $response = $this->actingAs($user, 'sanctum')
                        ->getJson('/api/employees');

        $response->assertStatus(403);
    }

    public function test_validates_required_fields_when_creating_employee(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('create employees');

        $response = $this->actingAs($user, 'sanctum')
                        ->postJson('/api/employees', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'first_name',
                    'last_name',
                    'date_of_birth',
                    'gender',
                    'phone_number',
                    'emergency_contact_name',
                    'emergency_contact_phone',
                    'address',
                    'city',
                    'province',
                    'postal_code',
                    'hire_date',
                    'employment_status',
                    'employee_type_id',
                    'department_id',
                    'position_id',
                ]);
    }

    public function test_validates_unique_phone_number(): void
    {
        $user = User::factory()->create();
        $user->givePermissionTo('create employees');

        $existingEmployee = Employee::factory()->create(['phone_number' => '628123456789']);

        $department = Department::factory()->create();
        $employeeType = EmployeeType::factory()->create();
        $position = Position::factory()->create();

        $employeeData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone_number' => '628123456789', // Same as existing
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '628987654321',
            'address' => 'Jl. Test No. 123',
            'city' => 'Jakarta',
            'province' => 'DKI Jakarta',
            'postal_code' => '12345',
            'hire_date' => '2024-01-01',
            'employment_status' => 'active',
            'employee_type_id' => $employeeType->id,
            'department_id' => $department->id,
            'position_id' => $position->id,
        ];

        $response = $this->actingAs($user, 'sanctum')
                        ->postJson('/api/employees', $employeeData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['phone_number']);
    }
}
```

---

## Chapter Summary

In this chapter, you've successfully implemented:

✅ **Comprehensive Employee Controller**
- Full CRUD operations with advanced features
- Bulk operations for efficient management
- Advanced search and filtering capabilities
- File upload handling for profile photos
- Indonesian healthcare-specific business logic

✅ **Employee API Resource**
- Comprehensive data transformation
- Relationship loading optimization
- Indonesian localization support
- Computed attributes for business logic

✅ **Form Request Validation**
- Detailed validation rules with Indonesian messages
- Phone number normalization
- Unique constraint handling
- File upload validation

✅ **React Employee Management Interface**
- Advanced search and filtering UI
- Bulk action capabilities
- Responsive table with pagination
- Permission-based UI rendering
- Indonesian language interface

✅ **Comprehensive Testing**
- Controller functionality tests
- Validation tests
- Permission-based access tests
- File upload tests
- Bulk operation tests

### Key Features Implemented

**Backend Features:**
- Advanced employee search and filtering
- Bulk operations (activate, deactivate, delete, export)
- Profile photo upload and management
- Employee statistics and reporting
- Department-based access control
- Indonesian phone number normalization

**Frontend Features:**
- Responsive employee list with advanced filters
- Bulk selection and actions
- Real-time search functionality
- Permission-based UI components
- Indonesian language interface
- Professional table design with shadcn/ui

**Security & Validation:**
- Comprehensive input validation
- File upload security
- Permission-based access control
- SQL injection prevention
- XSS protection

### What's Next?

In Chapter 6, we'll implement department management with hierarchical structures, location management, and organizational chart visualization.

### Key Commands to Remember

```bash
# Create controller and resources
php artisan make:controller Api/EmployeeController --api
php artisan make:resource EmployeeResource
php artisan make:request StoreEmployeeRequest
php artisan make:request UpdateEmployeeRequest

# Run tests
php artisan test --filter=EmployeeControllerTest

# Create storage link for file uploads
php artisan storage:link
```

---

## Additional Resources

- [Laravel File Storage](https://laravel.com/docs/12.x/filesystem)
- [Laravel API Resources](https://laravel.com/docs/12.x/eloquent-resources)
- [Laravel Validation](https://laravel.com/docs/12.x/validation)
- [React Table Components](https://ui.shadcn.com/docs/components/table)

Ready for Chapter 6? Let's build department management!
