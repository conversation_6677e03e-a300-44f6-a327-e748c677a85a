# Chapter 2: Database Design and Models

## Designing a Comprehensive Healthcare Database Schema

In this chapter, we'll design and implement a robust database schema for our Clinic Management System. We'll create all necessary tables, relationships, and Eloquent models to handle patients, doctors, appointments, medical records, prescriptions, billing, and BPJS integration.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Understand healthcare database design principles
- Create comprehensive database migrations for all clinic entities
- Build Eloquent models with proper relationships
- Implement data validation and business rules
- Set up database seeders for testing data
- Understand HIPAA-compliant data storage practices

## 🏥 Healthcare Database Design Principles

### Key Considerations for Medical Data

1. **Data Integrity**: Medical data must be accurate and consistent
2. **Audit Trails**: Track all changes for compliance and security
3. **Privacy & Security**: Protect patient information (HIPAA compliance)
4. **Scalability**: Support multiple clinics and large patient volumes
5. **Interoperability**: Support integration with external systems (BPJS, labs)

### Entity Relationship Overview

```mermaid
erDiagram
    USERS ||--o{ PATIENTS : "can be"
    USERS ||--o{ DOCTORS : "can be"
    USERS ||--o{ STAFF : "can be"
    
    PATIENTS ||--o{ APPOINTMENTS : "books"
    DOCTORS ||--o{ APPOINTMENTS : "attends"
    
    APPOINTMENTS ||--o{ MEDICAL_RECORDS : "generates"
    PATIENTS ||--o{ MEDICAL_RECORDS : "owns"
    DOCTORS ||--o{ MEDICAL_RECORDS : "creates"
    
    MEDICAL_RECORDS ||--o{ PRESCRIPTIONS : "contains"
    DOCTORS ||--o{ PRESCRIPTIONS : "writes"
    PATIENTS ||--o{ PRESCRIPTIONS : "receives"
    
    PATIENTS ||--o{ BILLING : "owes"
    APPOINTMENTS ||--o{ BILLING : "generates"
    
    PATIENTS ||--o{ BPJS_DATA : "has"
    CLINICS ||--o{ USERS : "employs"
    CLINICS ||--o{ DEPARTMENTS : "contains"
    DEPARTMENTS ||--o{ DOCTORS : "assigns"
```

## 📊 Database Schema Design

### Core Entities

1. **Users** (Extended from starter kit)
2. **Clinics** (Multi-clinic support)
3. **Departments** (Organizational structure)
4. **Patients** (Patient information)
5. **Doctors** (Doctor profiles and specializations)
6. **Appointments** (Scheduling system)
7. **Medical Records** (Patient medical history)
8. **Prescriptions** (Medication management)
9. **Billing** (Financial transactions)
10. **BPJS Data** (Insurance integration)

## 🛠 Creating Database Migrations

Let's create all the necessary migrations:

```bash
# Create migrations for all entities
php artisan make:migration add_clinic_fields_to_users_table
php artisan make:migration create_clinics_table
php artisan make:migration create_departments_table
php artisan make:migration create_patients_table
php artisan make:migration create_doctors_table
php artisan make:migration create_appointments_table
php artisan make:migration create_medical_records_table
php artisan make:migration create_prescriptions_table
php artisan make:migration create_prescription_items_table
php artisan make:migration create_billing_table
php artisan make:migration create_billing_items_table
php artisan make:migration create_bpjs_data_table
php artisan make:migration create_audit_logs_table
```

### 1. Extending Users Table

**database/migrations/xxxx_add_clinic_fields_to_users_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'doctor', 'nurse', 'receptionist', 'patient'])
                  ->default('patient')->after('email');
            $table->string('phone')->nullable()->after('role');
            $table->text('address')->nullable()->after('phone');
            $table->date('date_of_birth')->nullable()->after('address');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('date_of_birth');
            $table->string('employee_id')->nullable()->unique()->after('gender');
            $table->foreignId('clinic_id')->nullable()->constrained()->after('employee_id');
            $table->boolean('is_active')->default(true)->after('clinic_id');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
            
            $table->index(['role', 'clinic_id']);
            $table->index(['is_active', 'role']);
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['clinic_id']);
            $table->dropIndex(['role', 'clinic_id']);
            $table->dropIndex(['is_active', 'role']);
            $table->dropColumn([
                'role', 'phone', 'address', 'date_of_birth', 'gender',
                'employee_id', 'clinic_id', 'is_active', 'last_login_at'
            ]);
        });
    }
};
```

### 2. Clinics Table

**database/migrations/xxxx_create_clinics_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('clinics', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique(); // Clinic identifier
            $table->text('address');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->string('license_number')->unique();
            $table->string('tax_id')->nullable();
            
            // BPJS Integration fields
            $table->string('bpjs_provider_code')->nullable();
            $table->enum('bpjs_provider_type', ['FKTP', 'FKRTL'])->nullable();
            
            // Operating hours
            $table->json('operating_hours'); // Store as JSON
            
            // Settings
            $table->json('settings')->nullable(); // Clinic-specific settings
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['is_active', 'code']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('clinics');
    }
};
```

### 3. Departments Table

**database/migrations/xxxx_create_departments_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->string('head_doctor_id')->nullable(); // Will be foreign key to users
            $table->json('services')->nullable(); // Services offered by department
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['clinic_id', 'is_active']);
            $table->unique(['clinic_id', 'code']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
```

### 4. Patients Table

**database/migrations/xxxx_create_patients_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('patients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            
            // Patient identification
            $table->string('patient_number')->unique(); // Auto-generated
            $table->string('national_id')->unique(); // NIK/KTP
            $table->string('first_name');
            $table->string('last_name');
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female']);
            $table->string('phone');
            $table->string('email')->nullable();
            $table->text('address');
            
            // Emergency contact
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone');
            $table->string('emergency_contact_relationship');
            
            // Medical information
            $table->enum('blood_type', ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])->nullable();
            $table->json('allergies')->nullable(); // Store as JSON array
            $table->json('chronic_conditions')->nullable(); // Store as JSON array
            $table->text('medical_notes')->nullable();
            
            // Insurance information
            $table->boolean('has_bpjs')->default(false);
            $table->string('bpjs_number')->nullable();
            $table->enum('bpjs_class', ['1', '2', '3'])->nullable();
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['clinic_id', 'is_active']);
            $table->index(['national_id', 'clinic_id']);
            $table->index('patient_number');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('patients');
    }
};
```

### 5. Doctors Table

**database/migrations/xxxx_create_doctors_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            
            // Professional information
            $table->string('license_number')->unique();
            $table->string('specialization');
            $table->json('sub_specializations')->nullable(); // Additional specializations
            $table->text('qualifications'); // Education and certifications
            $table->integer('years_of_experience')->default(0);
            
            // Practice information
            $table->decimal('consultation_fee', 10, 2)->default(0);
            $table->integer('consultation_duration')->default(30); // minutes
            $table->json('available_days'); // Days of the week
            $table->time('start_time')->default('08:00:00');
            $table->time('end_time')->default('17:00:00');
            
            // Settings
            $table->boolean('accepts_online_booking')->default(true);
            $table->integer('max_patients_per_day')->default(20);
            $table->json('settings')->nullable(); // Doctor-specific settings
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['clinic_id', 'department_id', 'is_active']);
            $table->index('specialization');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
```

## 📝 Creating Eloquent Models

Now let's create the corresponding Eloquent models with proper relationships and validation.

### Base Model with Audit Trail

First, let's create a base model for audit trails:

```bash
php artisan make:model BaseModel
```

**app/Models/BaseModel.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

abstract class BaseModel extends Model
{
    use SoftDeletes;

    protected $dates = ['deleted_at'];

    protected static function boot()
    {
        parent::boot();

        // Automatically set created_by and updated_by
        static::creating(function ($model) {
            if (auth()->check()) {
                $model->created_by = auth()->id();
                $model->updated_by = auth()->id();
            }
        });

        static::updating(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->id();
            }
        });
    }
}
```

### User Model (Extended)

**app/Models/User.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name', 'email', 'password', 'role', 'phone', 'address',
        'date_of_birth', 'gender', 'employee_id', 'clinic_id', 'is_active'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    // Relationships
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function patient()
    {
        return $this->hasOne(Patient::class);
    }

    public function doctor()
    {
        return $this->hasOne(Doctor::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    // Helper methods
    public function isDoctor()
    {
        return $this->role === 'doctor';
    }

    public function isPatient()
    {
        return $this->role === 'patient';
    }

    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function getFullNameAttribute()
    {
        return $this->name;
    }
}
```

### Clinic Model

```bash
php artisan make:model Clinic
```

**app/Models/Clinic.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Clinic extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'code', 'address', 'phone', 'email', 'license_number',
        'tax_id', 'bpjs_provider_code', 'bpjs_provider_type',
        'operating_hours', 'settings', 'is_active'
    ];

    protected function casts(): array
    {
        return [
            'operating_hours' => 'array',
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function departments()
    {
        return $this->hasMany(Department::class);
    }

    public function patients()
    {
        return $this->hasMany(Patient::class);
    }

    public function doctors()
    {
        return $this->hasMany(Doctor::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
```

### Patient Model

```bash
php artisan make:model Patient
```

**app/Models/Patient.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Patient extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'clinic_id', 'patient_number', 'national_id',
        'first_name', 'last_name', 'date_of_birth', 'gender',
        'phone', 'email', 'address', 'emergency_contact_name',
        'emergency_contact_phone', 'emergency_contact_relationship',
        'blood_type', 'allergies', 'chronic_conditions', 'medical_notes',
        'has_bpjs', 'bpjs_number', 'bpjs_class', 'is_active'
    ];

    protected function casts(): array
    {
        return [
            'date_of_birth' => 'date',
            'allergies' => 'array',
            'chronic_conditions' => 'array',
            'has_bpjs' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class);
    }

    public function prescriptions()
    {
        return $this->hasMany(Prescription::class);
    }

    public function billings()
    {
        return $this->hasMany(Billing::class);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getAgeAttribute()
    {
        return $this->date_of_birth->age;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeWithBpjs($query)
    {
        return $query->where('has_bpjs', true);
    }
}
```

## 🔄 Model Relationships Summary

### Key Relationships Implemented

1. **User ↔ Clinic**: Many-to-One (users belong to clinics)
2. **User ↔ Patient**: One-to-One (user can be a patient)
3. **User ↔ Doctor**: One-to-One (user can be a doctor)
4. **Clinic ↔ Departments**: One-to-Many
5. **Patient ↔ Appointments**: One-to-Many
6. **Doctor ↔ Appointments**: One-to-Many
7. **Patient ↔ Medical Records**: One-to-Many
8. **Doctor ↔ Medical Records**: One-to-Many

## 🌱 Database Seeders

Create seeders for testing data:

```bash
php artisan make:seeder ClinicSeeder
php artisan make:seeder DepartmentSeeder
php artisan make:seeder UserSeeder
php artisan make:seeder PatientSeeder
```

**database/seeders/ClinicSeeder.php**:

```php
<?php

namespace Database\Seeders;

use App\Models\Clinic;
use Illuminate\Database\Seeder;

class ClinicSeeder extends Seeder
{
    public function run(): void
    {
        Clinic::create([
            'name' => 'Klinik Sehat Sentosa',
            'code' => 'KSS001',
            'address' => 'Jl. Kesehatan No. 123, Jakarta Selatan',
            'phone' => '021-12345678',
            'email' => '<EMAIL>',
            'license_number' => 'LIC-2024-001',
            'bpjs_provider_code' => '0089S002',
            'bpjs_provider_type' => 'FKTP',
            'operating_hours' => [
                'monday' => ['08:00', '17:00'],
                'tuesday' => ['08:00', '17:00'],
                'wednesday' => ['08:00', '17:00'],
                'thursday' => ['08:00', '17:00'],
                'friday' => ['08:00', '17:00'],
                'saturday' => ['08:00', '12:00'],
                'sunday' => null
            ],
            'settings' => [
                'appointment_duration' => 30,
                'max_advance_booking_days' => 30,
                'allow_online_booking' => true
            ]
        ]);
    }
}
```

## 🧪 Testing the Database Schema

Run the migrations and seeders:

```bash
# Run migrations
php artisan migrate

# Run seeders
php artisan db:seed --class=ClinicSeeder
php artisan db:seed --class=DepartmentSeeder
php artisan db:seed --class=UserSeeder

# Or run all seeders
php artisan db:seed
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Designed comprehensive database schema** for clinic management
2. ✅ **Created database migrations** for all core entities
3. ✅ **Built Eloquent models** with proper relationships
4. ✅ **Implemented data validation** and business rules
5. ✅ **Set up database seeders** for testing data
6. ✅ **Established audit trail** and security practices

### What We Have Now

- Complete database schema for clinic management
- Eloquent models with relationships and validation
- Seeders for testing data
- Foundation for healthcare data management

### Next Steps

In **Chapter 3: Authentication and Authorization**, we'll:
- Extend the authentication system for multiple user roles
- Implement role-based access control
- Create middleware for permission management
- Build user management interfaces

---

**Ready to continue?** Proceed to [Chapter 3: Authentication and Authorization](./chapter-03-authentication.md) to implement secure access control for our clinic management system.
