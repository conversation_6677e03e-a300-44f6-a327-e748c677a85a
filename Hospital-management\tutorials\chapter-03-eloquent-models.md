# Chapter 3: Eloquent Models and Relationships

## Overview
In this chapter, we'll create comprehensive Eloquent models for our Hospital Employee Management System. We'll implement complex relationships, model attributes, accessors, mutators, and Indonesian healthcare-specific validations. These models will serve as the foundation for all business logic in our application.

## Learning Objectives
- Create Eloquent models for all database entities
- Implement complex relationships including self-referencing
- Add model attributes, accessors, and mutators
- Implement Indonesian healthcare-specific validations
- Understand model factories for testing

## Prerequisites
- Completed Chapter 1 (Project Setup)
- Completed Chapter 2 (Database Design)
- Understanding of Eloquent ORM concepts
- Familiarity with PHP classes and relationships

## Duration
60-75 minutes

---

## Step 1: Create Core Models

### 1.1 Create EmployeeType Model

```bash
php artisan make:model EmployeeType
```

Edit `app/Models/EmployeeType.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmployeeType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'requires_license',
        'minimum_education_level',
        'is_medical_staff',
    ];

    protected $casts = [
        'requires_license' => 'boolean',
        'is_medical_staff' => 'boolean',
    ];

    /**
     * Get employees of this type
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get active employees of this type
     */
    public function activeEmployees(): HasMany
    {
        return $this->employees()->where('employment_status', 'active');
    }

    /**
     * Check if this employee type is medical staff
     */
    public function isMedicalStaff(): bool
    {
        return $this->is_medical_staff;
    }

    /**
     * Check if this employee type requires professional license
     */
    public function requiresLicense(): bool
    {
        return $this->requires_license;
    }

    /**
     * Get Indonesian name for the employee type
     */
    public function getIndonesianNameAttribute(): string
    {
        $translations = [
            'DS' => 'Dokter Spesialis',
            'DU' => 'Dokter Umum',
            'PER' => 'Perawat',
            'BID' => 'Bidan',
            'ADM' => 'Tenaga Administrasi',
        ];

        return $translations[$this->code] ?? $this->name;
    }
}
```

### 1.2 Create Department Model

```bash
php artisan make:model Department
```

Edit `app/Models/Department.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'parent_department_id',
        'department_head_id',
        'location',
        'phone_extension',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the parent department
     */
    public function parentDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'parent_department_id');
    }

    /**
     * Get child departments
     */
    public function childDepartments(): HasMany
    {
        return $this->hasMany(Department::class, 'parent_department_id');
    }

    /**
     * Get all employees in this department
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get active employees in this department
     */
    public function activeEmployees(): HasMany
    {
        return $this->employees()->where('employment_status', 'active');
    }

    /**
     * Get the department head
     */
    public function departmentHead(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'department_head_id');
    }

    /**
     * Get employee shifts for this department
     */
    public function employeeShifts(): HasMany
    {
        return $this->hasMany(EmployeeShift::class);
    }

    /**
     * Check if department is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Get full department hierarchy path
     */
    public function getFullPathAttribute(): string
    {
        $path = collect([$this->name]);
        $parent = $this->parentDepartment;

        while ($parent) {
            $path->prepend($parent->name);
            $parent = $parent->parentDepartment;
        }

        return $path->implode(' > ');
    }

    /**
     * Scope for active departments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for root departments (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_department_id');
    }
}
```

### 1.3 Create Position Model

```bash
php artisan make:model Position
```

Edit `app/Models/Position.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Position extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'level',
        'description',
        'responsibilities',
        'requirements',
        'min_salary',
        'max_salary',
    ];

    protected $casts = [
        'min_salary' => 'decimal:2',
        'max_salary' => 'decimal:2',
    ];

    /**
     * Get employees with this position
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get active employees with this position
     */
    public function activeEmployees(): HasMany
    {
        return $this->employees()->where('employment_status', 'active');
    }

    /**
     * Check if position is executive level
     */
    public function isExecutive(): bool
    {
        return $this->level === 'executive';
    }

    /**
     * Check if position is management level
     */
    public function isManagement(): bool
    {
        return in_array($this->level, ['executive', 'manager']);
    }

    /**
     * Get salary range as formatted string
     */
    public function getSalaryRangeAttribute(): string
    {
        if (!$this->min_salary || !$this->max_salary) {
            return 'Tidak ditentukan';
        }

        return 'Rp ' . number_format($this->min_salary, 0, ',', '.') . 
               ' - Rp ' . number_format($this->max_salary, 0, ',', '.');
    }

    /**
     * Get Indonesian level name
     */
    public function getIndonesianLevelAttribute(): string
    {
        $levels = [
            'executive' => 'Eksekutif',
            'manager' => 'Manajer',
            'supervisor' => 'Supervisor',
            'staff' => 'Staff',
        ];

        return $levels[$this->level] ?? $this->level;
    }
}
```

---

## Step 2: Create Employee Model with Complex Relationships

### 2.1 Create Employee Model

```bash
php artisan make:model Employee
```

Edit `app/Models/Employee.php` (Part 1 - Basic Structure):

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Employee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'employee_number',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'phone_number',
        'emergency_contact_name',
        'emergency_contact_phone',
        'address',
        'city',
        'province',
        'postal_code',
        'hire_date',
        'employment_status',
        'employee_type_id',
        'department_id',
        'position_id',
        'supervisor_id',
        'salary_grade',
        'basic_salary',
        'profile_photo_path',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'hire_date' => 'date',
        'basic_salary' => 'decimal:2',
    ];

    protected $appends = [
        'full_name',
        'age',
        'years_of_service',
    ];

    /**
     * Get the user account
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the employee type
     */
    public function employeeType(): BelongsTo
    {
        return $this->belongsTo(EmployeeType::class);
    }

    /**
     * Get the department
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the position
     */
    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    /**
     * Get the supervisor (self-referencing)
     */
    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'supervisor_id');
    }

    /**
     * Get subordinates (employees supervised by this employee)
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'supervisor_id');
    }

    /**
     * Get active subordinates
     */
    public function activeSubordinates(): HasMany
    {
        return $this->subordinates()->where('employment_status', 'active');
    }

    /**
     * Get employee licenses
     */
    public function licenses(): HasMany
    {
        return $this->hasMany(EmployeeLicense::class);
    }

    /**
     * Get active licenses
     */
    public function activeLicenses(): HasMany
    {
        return $this->licenses()->where('status', 'active');
    }

    /**
     * Get employee shifts
     */
    public function employeeShifts(): HasMany
    {
        return $this->hasMany(EmployeeShift::class);
    }

    /**
     * Get shifts through pivot table
     */
    public function shifts(): BelongsToMany
    {
        return $this->belongsToMany(Shift::class, 'employee_shifts')
                    ->withPivot(['date', 'status', 'check_in_time', 'check_out_time'])
                    ->withTimestamps();
    }

    /**
     * Get leave requests
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * Get approved leave requests
     */
    public function approvedLeaveRequests(): HasMany
    {
        return $this->leaveRequests()->where('status', 'approved');
    }

    /**
     * Get leave requests approved by this employee
     */
    public function approvedLeaves(): HasMany
    {
        return $this->hasMany(LeaveRequest::class, 'approved_by');
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get age attribute
     */
    public function getAgeAttribute(): int
    {
        return $this->date_of_birth->age;
    }

    /**
     * Get years of service attribute
     */
    public function getYearsOfServiceAttribute(): float
    {
        return $this->hire_date->diffInYears(now());
    }

    /**
     * Get formatted salary
     */
    public function getFormattedSalaryAttribute(): string
    {
        if (!$this->basic_salary) {
            return 'Tidak ditentukan';
        }

        return 'Rp ' . number_format($this->basic_salary, 0, ',', '.');
    }

    /**
     * Get Indonesian gender
     */
    public function getIndonesianGenderAttribute(): string
    {
        return $this->gender === 'male' ? 'Laki-laki' : 'Perempuan';
    }

    /**
     * Get Indonesian employment status
     */
    public function getIndonesianEmploymentStatusAttribute(): string
    {
        $statuses = [
            'active' => 'Aktif',
            'inactive' => 'Tidak Aktif',
            'terminated' => 'Diberhentikan',
            'suspended' => 'Diskors',
        ];

        return $statuses[$this->employment_status] ?? $this->employment_status;
    }

    /**
     * Check if employee is active
     */
    public function isActive(): bool
    {
        return $this->employment_status === 'active';
    }

    /**
     * Check if employee is medical staff
     */
    public function isMedicalStaff(): bool
    {
        return $this->employeeType->is_medical_staff;
    }

    /**
     * Check if employee has supervisor role
     */
    public function isSupervisor(): bool
    {
        return $this->subordinates()->exists();
    }

    /**
     * Check if employee requires license
     */
    public function requiresLicense(): bool
    {
        return $this->employeeType->requires_license;
    }

    /**
     * Scope for active employees
     */
    public function scopeActive($query)
    {
        return $query->where('employment_status', 'active');
    }

    /**
     * Scope for medical staff
     */
    public function scopeMedicalStaff($query)
    {
        return $query->whereHas('employeeType', function ($q) {
            $q->where('is_medical_staff', true);
        });
    }

    /**
     * Scope for employees in specific department
     */
    public function scopeInDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }
}

---

## Step 3: Create Supporting Models

### 3.1 Create EmployeeLicense Model

```bash
php artisan make:model EmployeeLicense
```

Edit `app/Models/EmployeeLicense.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class EmployeeLicense extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'license_type',
        'license_number',
        'issuing_authority',
        'issue_date',
        'expiry_date',
        'status',
        'document_path',
        'notes',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'expiry_date' => 'date',
    ];

    /**
     * Get the employee who owns this license
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Check if license is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if license is expired
     */
    public function isExpired(): bool
    {
        return $this->expiry_date < now();
    }

    /**
     * Check if license is expiring soon (within 30 days)
     */
    public function isExpiringSoon(): bool
    {
        return $this->expiry_date <= now()->addDays(30) && !$this->isExpired();
    }

    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiryAttribute(): int
    {
        return now()->diffInDays($this->expiry_date, false);
    }

    /**
     * Get Indonesian license type name
     */
    public function getIndonesianLicenseTypeAttribute(): string
    {
        $types = [
            'STR' => 'Surat Tanda Registrasi',
            'SIP' => 'Surat Izin Praktik',
            'Sertifikat Kompetensi' => 'Sertifikat Kompetensi',
        ];

        return $types[$this->license_type] ?? $this->license_type;
    }

    /**
     * Get Indonesian status
     */
    public function getIndonesianStatusAttribute(): string
    {
        $statuses = [
            'active' => 'Aktif',
            'expired' => 'Kedaluwarsa',
            'suspended' => 'Diskors',
            'revoked' => 'Dicabut',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Scope for active licenses
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for expired licenses
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * Scope for licenses expiring soon
     */
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('expiry_date', '>', now())
                    ->where('expiry_date', '<=', now()->addDays($days));
    }
}
```

### 3.2 Create Shift Model

```bash
php artisan make:model Shift
```

Edit `app/Models/Shift.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Shift extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'start_time',
        'end_time',
        'duration_hours',
        'is_overnight',
        'shift_type',
        'description',
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'duration_hours' => 'decimal:2',
        'is_overnight' => 'boolean',
    ];

    /**
     * Get employees assigned to this shift
     */
    public function employees(): BelongsToMany
    {
        return $this->belongsToMany(Employee::class, 'employee_shifts')
                    ->withPivot(['date', 'status', 'check_in_time', 'check_out_time'])
                    ->withTimestamps();
    }

    /**
     * Get employee shifts for this shift
     */
    public function employeeShifts(): HasMany
    {
        return $this->hasMany(EmployeeShift::class);
    }

    /**
     * Check if shift is overnight
     */
    public function isOvernight(): bool
    {
        return $this->is_overnight;
    }

    /**
     * Check if shift is regular type
     */
    public function isRegular(): bool
    {
        return $this->shift_type === 'regular';
    }

    /**
     * Check if shift is on-call type
     */
    public function isOnCall(): bool
    {
        return $this->shift_type === 'on_call';
    }

    /**
     * Get formatted time range
     */
    public function getTimeRangeAttribute(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }

    /**
     * Get Indonesian shift type
     */
    public function getIndonesianShiftTypeAttribute(): string
    {
        $types = [
            'regular' => 'Reguler',
            'on_call' => 'Jaga',
            'emergency' => 'Darurat',
        ];

        return $types[$this->shift_type] ?? $this->shift_type;
    }

    /**
     * Scope for regular shifts
     */
    public function scopeRegular($query)
    {
        return $query->where('shift_type', 'regular');
    }

    /**
     * Scope for on-call shifts
     */
    public function scopeOnCall($query)
    {
        return $query->where('shift_type', 'on_call');
    }
}
```

### 3.3 Create EmployeeShift Model

```bash
php artisan make:model EmployeeShift
```

Edit `app/Models/EmployeeShift.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmployeeShift extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'shift_id',
        'department_id',
        'date',
        'status',
        'check_in_time',
        'check_out_time',
        'break_start_time',
        'break_end_time',
        'overtime_hours',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'date' => 'date',
        'check_in_time' => 'datetime:H:i',
        'check_out_time' => 'datetime:H:i',
        'break_start_time' => 'datetime:H:i',
        'break_end_time' => 'datetime:H:i',
        'overtime_hours' => 'decimal:2',
    ];

    /**
     * Get the employee
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the shift
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the department
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the user who created this shift assignment
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Check if shift is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if employee was absent
     */
    public function isAbsent(): bool
    {
        return in_array($this->status, ['absent', 'sick_leave']);
    }

    /**
     * Get total working hours
     */
    public function getTotalWorkingHoursAttribute(): float
    {
        if (!$this->check_in_time || !$this->check_out_time) {
            return 0;
        }

        $workingMinutes = $this->check_in_time->diffInMinutes($this->check_out_time);

        // Subtract break time if available
        if ($this->break_start_time && $this->break_end_time) {
            $breakMinutes = $this->break_start_time->diffInMinutes($this->break_end_time);
            $workingMinutes -= $breakMinutes;
        }

        return round($workingMinutes / 60, 2);
    }

    /**
     * Get Indonesian status
     */
    public function getIndonesianStatusAttribute(): string
    {
        $statuses = [
            'scheduled' => 'Dijadwalkan',
            'confirmed' => 'Dikonfirmasi',
            'completed' => 'Selesai',
            'absent' => 'Tidak Hadir',
            'sick_leave' => 'Cuti Sakit',
            'cancelled' => 'Dibatalkan',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Scope for specific date
     */
    public function scopeForDate($query, $date)
    {
        return $query->whereDate('date', $date);
    }

    /**
     * Scope for date range
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope for specific status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for completed shifts
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
```

### 3.4 Create LeaveType Model

```bash
php artisan make:model LeaveType
```

Edit `app/Models/LeaveType.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LeaveType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'max_days_per_year',
        'requires_medical_certificate',
        'is_paid',
        'description',
    ];

    protected $casts = [
        'max_days_per_year' => 'integer',
        'requires_medical_certificate' => 'boolean',
        'is_paid' => 'boolean',
    ];

    /**
     * Get leave requests of this type
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * Check if leave type requires medical certificate
     */
    public function requiresMedicalCertificate(): bool
    {
        return $this->requires_medical_certificate;
    }

    /**
     * Check if leave type is paid
     */
    public function isPaid(): bool
    {
        return $this->is_paid;
    }

    /**
     * Get Indonesian name
     */
    public function getIndonesianNameAttribute(): string
    {
        $names = [
            'ANNUAL' => 'Cuti Tahunan',
            'SICK' => 'Cuti Sakit',
            'MATERNITY' => 'Cuti Melahirkan',
            'HAJJ' => 'Cuti Haji',
            'EMERGENCY' => 'Cuti Darurat',
        ];

        return $names[$this->code] ?? $this->name;
    }

    /**
     * Get maximum days display
     */
    public function getMaxDaysDisplayAttribute(): string
    {
        return $this->max_days_per_year ? $this->max_days_per_year . ' hari' : 'Tidak terbatas';
    }
}
```

### 3.5 Create LeaveRequest Model

```bash
php artisan make:model LeaveRequest
```

Edit `app/Models/LeaveRequest.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LeaveRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'start_date',
        'end_date',
        'total_days',
        'reason',
        'status',
        'approved_by',
        'approved_at',
        'rejection_reason',
        'medical_certificate_path',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'total_days' => 'integer',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the employee who requested leave
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the leave type
     */
    public function leaveType(): BelongsTo
    {
        return $this->belongsTo(LeaveType::class);
    }

    /**
     * Get the employee who approved the leave
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * Check if leave request is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if leave request is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if leave request is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Get Indonesian status
     */
    public function getIndonesianStatusAttribute(): string
    {
        $statuses = [
            'pending' => 'Menunggu Persetujuan',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            'cancelled' => 'Dibatalkan',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get duration in days
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for current year
     */
    public function scopeCurrentYear($query)
    {
        return $query->whereYear('start_date', now()->year);
    }
}
```

---

## Step 4: Model Factories for Testing

### 4.1 Create Employee Factory

```bash
php artisan make:factory EmployeeFactory
```

Edit `database/factories/EmployeeFactory.php`:

```php
<?php

namespace Database\Factories;

use App\Models\Department;
use App\Models\Employee;
use App\Models\EmployeeType;
use App\Models\Position;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeFactory extends Factory
{
    protected $model = Employee::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'employee_number' => 'EMP' . $this->faker->unique()->numberBetween(1000, 9999),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'date_of_birth' => $this->faker->dateTimeBetween('-60 years', '-22 years'),
            'gender' => $this->faker->randomElement(['male', 'female']),
            'phone_number' => $this->faker->phoneNumber(),
            'emergency_contact_name' => $this->faker->name(),
            'emergency_contact_phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'province' => $this->faker->randomElement([
                'DKI Jakarta', 'Jawa Barat', 'Jawa Tengah', 'Jawa Timur',
                'Sumatera Utara', 'Sumatera Barat', 'Bali'
            ]),
            'postal_code' => $this->faker->postcode(),
            'hire_date' => $this->faker->dateTimeBetween('-10 years', 'now'),
            'employment_status' => $this->faker->randomElement(['active', 'inactive']),
            'employee_type_id' => EmployeeType::factory(),
            'department_id' => Department::factory(),
            'position_id' => Position::factory(),
            'supervisor_id' => null, // Will be set separately
            'salary_grade' => $this->faker->randomElement(['I', 'II', 'III', 'IV', 'V']),
            'basic_salary' => $this->faker->numberBetween(4000000, 25000000),
        ];
    }

    /**
     * Indicate that the employee is a doctor
     */
    public function doctor(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'employee_type_id' => EmployeeType::where('code', 'DS')->first()?->id
                    ?? EmployeeType::factory()->create(['code' => 'DS', 'name' => 'Dokter Spesialis']),
                'basic_salary' => $this->faker->numberBetween(15000000, 50000000),
            ];
        });
    }

    /**
     * Indicate that the employee is a nurse
     */
    public function nurse(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'employee_type_id' => EmployeeType::where('code', 'PER')->first()?->id
                    ?? EmployeeType::factory()->create(['code' => 'PER', 'name' => 'Perawat']),
                'basic_salary' => $this->faker->numberBetween(4000000, 8000000),
            ];
        });
    }

    /**
     * Indicate that the employee is active
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'employment_status' => 'active',
            ];
        });
    }
}
```

### 4.2 Create EmployeeLicense Factory

```bash
php artisan make:factory EmployeeLicenseFactory
```

Edit `database/factories/EmployeeLicenseFactory.php`:

```php
<?php

namespace Database\Factories;

use App\Models\Employee;
use App\Models\EmployeeLicense;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeLicenseFactory extends Factory
{
    protected $model = EmployeeLicense::class;

    public function definition(): array
    {
        $issueDate = $this->faker->dateTimeBetween('-5 years', 'now');

        return [
            'employee_id' => Employee::factory(),
            'license_type' => $this->faker->randomElement(['STR', 'SIP', 'Sertifikat Kompetensi']),
            'license_number' => $this->faker->unique()->numerify('LIC-####-####'),
            'issuing_authority' => $this->faker->randomElement([
                'Kementerian Kesehatan RI',
                'Dinas Kesehatan Provinsi',
                'Organisasi Profesi'
            ]),
            'issue_date' => $issueDate,
            'expiry_date' => $this->faker->dateTimeBetween($issueDate, '+5 years'),
            'status' => $this->faker->randomElement(['active', 'expired']),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the license is active
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'expiry_date' => $this->faker->dateTimeBetween('now', '+2 years'),
            ];
        });
    }

    /**
     * Indicate that the license is expiring soon
     */
    public function expiringSoon(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'expiry_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            ];
        });
    }
}
```

---

## Step 5: Model Validation and Business Logic

### 5.1 Create Employee Service Class

```bash
php artisan make:class Services/EmployeeService
```

Create `app/Services/EmployeeService.php`:

```php
<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\EmployeeLicense;
use Illuminate\Support\Collection;

class EmployeeService
{
    /**
     * Generate unique employee number
     */
    public function generateEmployeeNumber(): string
    {
        $year = now()->year;
        $lastEmployee = Employee::whereYear('created_at', $year)
                              ->orderBy('employee_number', 'desc')
                              ->first();

        if (!$lastEmployee) {
            return $year . '001';
        }

        $lastNumber = (int) substr($lastEmployee->employee_number, -3);
        return $year . str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Check if employee can be supervisor
     */
    public function canBeSupervisor(Employee $employee): bool
    {
        return $employee->position->isManagement() && $employee->isActive();
    }

    /**
     * Get employees requiring license renewal
     */
    public function getEmployeesWithExpiringLicenses(int $days = 30): Collection
    {
        return Employee::whereHas('licenses', function ($query) use ($days) {
            $query->where('status', 'active')
                  ->where('expiry_date', '>', now())
                  ->where('expiry_date', '<=', now()->addDays($days));
        })->with(['licenses' => function ($query) use ($days) {
            $query->where('status', 'active')
                  ->where('expiry_date', '>', now())
                  ->where('expiry_date', '<=', now()->addDays($days));
        }])->get();
    }

    /**
     * Calculate employee hierarchy depth
     */
    public function getHierarchyDepth(Employee $employee): int
    {
        $depth = 0;
        $current = $employee;

        while ($current->supervisor) {
            $depth++;
            $current = $current->supervisor;

            // Prevent infinite loops
            if ($depth > 10) {
                break;
            }
        }

        return $depth;
    }

    /**
     * Get all subordinates recursively
     */
    public function getAllSubordinates(Employee $employee): Collection
    {
        $subordinates = collect();

        foreach ($employee->subordinates as $subordinate) {
            $subordinates->push($subordinate);
            $subordinates = $subordinates->merge($this->getAllSubordinates($subordinate));
        }

        return $subordinates;
    }

    /**
     * Validate supervisor assignment
     */
    public function validateSupervisorAssignment(Employee $employee, Employee $supervisor): array
    {
        $errors = [];

        // Check if supervisor is active
        if (!$supervisor->isActive()) {
            $errors[] = 'Supervisor harus dalam status aktif';
        }

        // Check if supervisor can be a supervisor
        if (!$this->canBeSupervisor($supervisor)) {
            $errors[] = 'Karyawan yang dipilih tidak dapat menjadi supervisor';
        }

        // Check for circular reference
        if ($this->wouldCreateCircularReference($employee, $supervisor)) {
            $errors[] = 'Penugasan supervisor akan menciptakan referensi melingkar';
        }

        // Check department compatibility
        if ($employee->department_id !== $supervisor->department_id) {
            $errors[] = 'Supervisor harus berada di departemen yang sama';
        }

        return $errors;
    }

    /**
     * Check if supervisor assignment would create circular reference
     */
    private function wouldCreateCircularReference(Employee $employee, Employee $supervisor): bool
    {
        $current = $supervisor;
        $visited = collect([$employee->id]);

        while ($current->supervisor) {
            if ($visited->contains($current->supervisor_id)) {
                return true;
            }

            $visited->push($current->supervisor_id);
            $current = $current->supervisor;

            // Prevent infinite loops
            if ($visited->count() > 10) {
                break;
            }
        }

        return false;
    }
}
```

---

## Step 6: Testing Models and Relationships

### 6.1 Create Model Tests

```bash
php artisan make:test EmployeeModelTest --unit
```

Edit `tests/Unit/EmployeeModelTest.php`:

```php
<?php

namespace Tests\Unit;

use App\Models\Department;
use App\Models\Employee;
use App\Models\EmployeeType;
use App\Models\Position;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmployeeModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_employee_belongs_to_user(): void
    {
        $user = User::factory()->create();
        $employee = Employee::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $employee->user);
        $this->assertEquals($user->id, $employee->user->id);
    }

    public function test_employee_belongs_to_employee_type(): void
    {
        $employeeType = EmployeeType::factory()->create();
        $employee = Employee::factory()->create(['employee_type_id' => $employeeType->id]);

        $this->assertInstanceOf(EmployeeType::class, $employee->employeeType);
        $this->assertEquals($employeeType->id, $employee->employeeType->id);
    }

    public function test_employee_belongs_to_department(): void
    {
        $department = Department::factory()->create();
        $employee = Employee::factory()->create(['department_id' => $department->id]);

        $this->assertInstanceOf(Department::class, $employee->department);
        $this->assertEquals($department->id, $employee->department->id);
    }

    public function test_employee_can_have_supervisor(): void
    {
        $supervisor = Employee::factory()->create();
        $employee = Employee::factory()->create(['supervisor_id' => $supervisor->id]);

        $this->assertInstanceOf(Employee::class, $employee->supervisor);
        $this->assertEquals($supervisor->id, $employee->supervisor->id);
    }

    public function test_employee_can_have_subordinates(): void
    {
        $supervisor = Employee::factory()->create();
        $subordinate1 = Employee::factory()->create(['supervisor_id' => $supervisor->id]);
        $subordinate2 = Employee::factory()->create(['supervisor_id' => $supervisor->id]);

        $this->assertCount(2, $supervisor->subordinates);
        $this->assertTrue($supervisor->subordinates->contains($subordinate1));
        $this->assertTrue($supervisor->subordinates->contains($subordinate2));
    }

    public function test_full_name_attribute(): void
    {
        $employee = Employee::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe'
        ]);

        $this->assertEquals('John Doe', $employee->full_name);
    }

    public function test_age_attribute(): void
    {
        $birthDate = now()->subYears(30);
        $employee = Employee::factory()->create(['date_of_birth' => $birthDate]);

        $this->assertEquals(30, $employee->age);
    }

    public function test_is_active_method(): void
    {
        $activeEmployee = Employee::factory()->create(['employment_status' => 'active']);
        $inactiveEmployee = Employee::factory()->create(['employment_status' => 'inactive']);

        $this->assertTrue($activeEmployee->isActive());
        $this->assertFalse($inactiveEmployee->isActive());
    }

    public function test_is_supervisor_method(): void
    {
        $supervisor = Employee::factory()->create();
        $employee = Employee::factory()->create();
        $subordinate = Employee::factory()->create(['supervisor_id' => $supervisor->id]);

        $this->assertTrue($supervisor->isSupervisor());
        $this->assertFalse($employee->isSupervisor());
    }

    public function test_active_scope(): void
    {
        Employee::factory()->create(['employment_status' => 'active']);
        Employee::factory()->create(['employment_status' => 'inactive']);
        Employee::factory()->create(['employment_status' => 'active']);

        $activeEmployees = Employee::active()->get();

        $this->assertCount(2, $activeEmployees);
        $activeEmployees->each(function ($employee) {
            $this->assertEquals('active', $employee->employment_status);
        });
    }
}
```

### 6.2 Test Relationships in Tinker

```bash
php artisan tinker
```

Run these commands to test relationships:

```php
// Test employee relationships
$employee = Employee::with(['department', 'employeeType', 'position', 'supervisor'])->first();
$employee->full_name;
$employee->department->name;
$employee->employeeType->indonesian_name;

// Test department hierarchy
$department = Department::with(['parentDepartment', 'childDepartments'])->first();
$department->full_path;

// Test supervisor-subordinate relationships
$supervisor = Employee::whereHas('subordinates')->first();
$supervisor->subordinates->count();
$supervisor->subordinates->pluck('full_name');

// Test license relationships
$employee = Employee::with('licenses')->first();
$employee->activeLicenses->count();
$employee->getLicensesExpiringSoon();

exit
```

---

## Step 7: Model Validation Rules

### 7.1 Create Form Request for Employee

```bash
php artisan make:request StoreEmployeeRequest
```

Edit `app/Http/Requests/StoreEmployeeRequest.php`:

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEmployeeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'gender' => ['required', Rule::in(['male', 'female'])],
            'phone_number' => ['required', 'string', 'max:20'],
            'emergency_contact_name' => ['required', 'string', 'max:255'],
            'emergency_contact_phone' => ['required', 'string', 'max:20'],
            'address' => ['required', 'string'],
            'city' => ['required', 'string', 'max:255'],
            'province' => ['required', 'string', 'max:255'],
            'postal_code' => ['required', 'string', 'max:10'],
            'hire_date' => ['required', 'date'],
            'employee_type_id' => ['required', 'exists:employee_types,id'],
            'department_id' => ['required', 'exists:departments,id'],
            'position_id' => ['required', 'exists:positions,id'],
            'supervisor_id' => ['nullable', 'exists:employees,id'],
            'basic_salary' => ['nullable', 'numeric', 'min:0'],
            'profile_photo' => ['nullable', 'image', 'max:2048'],
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'Nama depan wajib diisi',
            'last_name.required' => 'Nama belakang wajib diisi',
            'date_of_birth.required' => 'Tanggal lahir wajib diisi',
            'date_of_birth.before' => 'Tanggal lahir harus sebelum hari ini',
            'gender.required' => 'Jenis kelamin wajib dipilih',
            'phone_number.required' => 'Nomor telepon wajib diisi',
            'employee_type_id.required' => 'Jenis karyawan wajib dipilih',
            'department_id.required' => 'Departemen wajib dipilih',
            'position_id.required' => 'Posisi wajib dipilih',
            'supervisor_id.exists' => 'Supervisor yang dipilih tidak valid',
            'profile_photo.image' => 'File harus berupa gambar',
            'profile_photo.max' => 'Ukuran file maksimal 2MB',
        ];
    }
}
```

---

## Chapter Summary

In this chapter, you've successfully:

✅ Created comprehensive Eloquent models for all database entities
✅ Implemented complex relationships including self-referencing
✅ Added model attributes, accessors, and mutators
✅ Created Indonesian healthcare-specific business logic
✅ Implemented model factories for testing
✅ Added validation rules and form requests
✅ Created service classes for business logic
✅ Added comprehensive unit tests

### Models Created
- `EmployeeType` - Employee categories with Indonesian healthcare types
- `Department` - Hospital departments with hierarchy support
- `Position` - Job positions with salary ranges
- `Employee` - Main employee model with complex relationships
- `EmployeeLicense` - Professional licenses and certifications
- `Shift` - Shift patterns for hospital operations
- `EmployeeShift` - Employee shift assignments
- `LeaveType` - Types of leave available
- `LeaveRequest` - Employee leave requests

### Key Features Implemented
- Self-referencing relationships (supervisor-subordinate)
- Hierarchical department structure
- Indonesian localization for all text
- Complex business logic validation
- Comprehensive model factories
- Unit testing for relationships
- Service classes for business operations

### What's Next?
In Chapter 4, we'll implement a comprehensive authentication and authorization system with role-based access control specifically designed for hospital hierarchies and Indonesian healthcare requirements.

### Key Commands to Remember
```bash
# Create model
php artisan make:model ModelName

# Create model with factory
php artisan make:model ModelName -f

# Create form request
php artisan make:request RequestName

# Create service class
php artisan make:class Services/ServiceName

# Run unit tests
php artisan test --filter=ModelTest

# Test relationships in Tinker
php artisan tinker
```

---

## Additional Resources

- [Eloquent Relationships Documentation](https://laravel.com/docs/12.x/eloquent-relationships)
- [Model Factories Documentation](https://laravel.com/docs/12.x/eloquent-factories)
- [Form Request Validation](https://laravel.com/docs/12.x/validation#form-request-validation)
- [Eloquent Mutators & Accessors](https://laravel.com/docs/12.x/eloquent-mutators)

Ready for Chapter 4? Let's build the authentication and authorization system!
