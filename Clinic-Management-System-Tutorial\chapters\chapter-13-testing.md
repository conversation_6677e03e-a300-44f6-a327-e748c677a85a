# Chapter 13: Testing and Quality Assurance

## Building Comprehensive Test Suite

In this chapter, we'll create a comprehensive testing framework for our Clinic Management System, including unit tests, feature tests, API tests, and quality assurance practices to ensure reliability and maintainability.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create comprehensive test suite for all major functionality
- Implement unit and feature testing strategies
- Set up API testing and integration tests
- Build quality assurance practices and CI/CD pipeline
- Create test data factories and seeders
- Implement automated testing workflows

## 🧪 Testing Strategy

### Testing Pyramid

1. **Unit Tests**: Test individual components and methods
2. **Feature Tests**: Test complete user workflows
3. **Integration Tests**: Test component interactions
4. **API Tests**: Test API endpoints and responses
5. **Browser Tests**: Test user interface interactions

## 🛠 Testing Implementation

### Step 1: Test Configuration

**phpunit.xml** (Enhanced):

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
        <exclude>
            <directory>app/Console</directory>
            <file>app/Http/Kernel.php</file>
        </exclude>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

### Step 2: Model Factories

**database/factories/ClinicFactory.php**:

```php
<?php

namespace Database\Factories;

use App\Models\Clinic;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClinicFactory extends Factory
{
    protected $model = Clinic::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' Clinic',
            'code' => strtoupper($this->faker->lexify('???')) . $this->faker->numberBetween(100, 999),
            'address' => $this->faker->address,
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->unique()->safeEmail,
            'license_number' => 'LIC-' . $this->faker->numberBetween(100000, 999999),
            'operating_hours' => [
                'monday' => ['08:00', '17:00'],
                'tuesday' => ['08:00', '17:00'],
                'wednesday' => ['08:00', '17:00'],
                'thursday' => ['08:00', '17:00'],
                'friday' => ['08:00', '17:00'],
                'saturday' => ['08:00', '12:00'],
                'sunday' => null
            ],
            'settings' => [
                'appointment_duration' => 30,
                'max_advance_booking_days' => 30,
                'allow_online_booking' => true,
            ],
            'is_active' => true,
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function withBpjs(): static
    {
        return $this->state(fn (array $attributes) => [
            'bpjs_provider_code' => '0089S' . $this->faker->numberBetween(100, 999),
            'bpjs_provider_type' => $this->faker->randomElement(['FKTP', 'FKRTL']),
        ]);
    }
}
```

**database/factories/PatientFactory.php**:

```php
<?php

namespace Database\Factories;

use App\Models\Patient;
use App\Models\Clinic;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PatientFactory extends Factory
{
    protected $model = Patient::class;

    public function definition(): array
    {
        $firstName = $this->faker->firstName;
        $lastName = $this->faker->lastName;
        
        return [
            'clinic_id' => Clinic::factory(),
            'patient_number' => 'P' . date('Ymd') . $this->faker->numberBetween(1000, 9999),
            'national_id' => $this->faker->numerify('################'),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'date_of_birth' => $this->faker->dateTimeBetween('-80 years', '-1 year'),
            'gender' => $this->faker->randomElement(['male', 'female']),
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->optional()->safeEmail,
            'address' => $this->faker->address,
            'emergency_contact_name' => $this->faker->name,
            'emergency_contact_phone' => $this->faker->phoneNumber,
            'emergency_contact_relationship' => $this->faker->randomElement(['spouse', 'parent', 'sibling', 'child', 'friend']),
            'blood_type' => $this->faker->optional()->randomElement(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']),
            'allergies' => $this->faker->optional()->randomElements(['Penicillin', 'Aspirin', 'Nuts', 'Shellfish'], $this->faker->numberBetween(0, 2)),
            'chronic_conditions' => $this->faker->optional()->randomElements(['Diabetes', 'Hypertension', 'Asthma'], $this->faker->numberBetween(0, 2)),
            'has_bpjs' => $this->faker->boolean(60),
            'bpjs_number' => $this->faker->optional(0.6)->numerify('000#########'),
            'bpjs_class' => $this->faker->optional()->randomElement(['1', '2', '3']),
            'is_active' => true,
        ];
    }

    public function withUser(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'user_id' => User::factory()->patient()->create([
                    'name' => $attributes['first_name'] . ' ' . $attributes['last_name'],
                    'clinic_id' => $attributes['clinic_id'],
                ])->id,
            ];
        });
    }

    public function withBpjs(): static
    {
        return $this->state(fn (array $attributes) => [
            'has_bpjs' => true,
            'bpjs_number' => $this->faker->numerify('000#########'),
            'bpjs_class' => $this->faker->randomElement(['1', '2', '3']),
        ]);
    }
}
```

**database/factories/AppointmentFactory.php**:

```php
<?php

namespace Database\Factories;

use App\Models\Appointment;
use App\Models\Patient;
use App\Models\User;
use App\Models\Clinic;
use Illuminate\Database\Eloquent\Factories\Factory;

class AppointmentFactory extends Factory
{
    protected $model = Appointment::class;

    public function definition(): array
    {
        $appointmentDate = $this->faker->dateTimeBetween('now', '+30 days');
        $appointmentTime = $this->faker->time('H:i', '17:00');
        
        return [
            'patient_id' => Patient::factory(),
            'doctor_id' => User::factory()->doctor(),
            'clinic_id' => Clinic::factory(),
            'created_by' => User::factory()->receptionist(),
            'appointment_number' => 'APT' . date('Ymd') . $this->faker->numberBetween(1000, 9999),
            'appointment_date' => $appointmentDate,
            'appointment_time' => $appointmentTime,
            'duration' => $this->faker->randomElement([15, 30, 45, 60]),
            'type' => $this->faker->randomElement(['consultation', 'follow_up', 'emergency', 'procedure', 'checkup']),
            'priority' => $this->faker->randomElement(['low', 'normal', 'high', 'urgent']),
            'status' => $this->faker->randomElement(['scheduled', 'confirmed', 'completed', 'cancelled']),
            'reason' => $this->faker->sentence,
            'notes' => $this->faker->optional()->paragraph,
            'booking_source' => $this->faker->randomElement(['online', 'phone', 'walk_in', 'referral']),
            'uses_bpjs' => $this->faker->boolean(40),
        ];
    }

    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
            'appointment_date' => $this->faker->dateTimeBetween('now', '+30 days'),
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'appointment_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'completed_at' => $this->faker->dateTimeBetween($attributes['appointment_date'], 'now'),
        ]);
    }

    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'appointment_date' => today(),
            'appointment_time' => $this->faker->time('H:i', '17:00'),
        ]);
    }
}
```

### Step 3: Unit Tests

**tests/Unit/Models/PatientTest.php**:

```php
<?php

namespace Tests\Unit\Models;

use App\Models\Patient;
use App\Models\Clinic;
use App\Models\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PatientTest extends TestCase
{
    use RefreshDatabase;

    public function test_patient_belongs_to_clinic()
    {
        $clinic = Clinic::factory()->create();
        $patient = Patient::factory()->create(['clinic_id' => $clinic->id]);

        $this->assertInstanceOf(Clinic::class, $patient->clinic);
        $this->assertEquals($clinic->id, $patient->clinic->id);
    }

    public function test_patient_can_have_user_account()
    {
        $user = User::factory()->patient()->create();
        $patient = Patient::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $patient->user);
        $this->assertEquals($user->id, $patient->user->id);
    }

    public function test_patient_full_name_accessor()
    {
        $patient = Patient::factory()->make([
            'first_name' => 'John',
            'last_name' => 'Doe'
        ]);

        $this->assertEquals('John Doe', $patient->full_name);
    }

    public function test_patient_age_calculation()
    {
        $patient = Patient::factory()->make([
            'date_of_birth' => now()->subYears(30)->subMonths(6)
        ]);

        $this->assertEquals(30, $patient->age);
    }

    public function test_patient_active_scope()
    {
        Patient::factory()->create(['is_active' => true]);
        Patient::factory()->create(['is_active' => false]);

        $activePatients = Patient::active()->get();

        $this->assertCount(1, $activePatients);
        $this->assertTrue($activePatients->first()->is_active);
    }

    public function test_patient_with_bpjs_scope()
    {
        Patient::factory()->create(['has_bpjs' => true]);
        Patient::factory()->create(['has_bpjs' => false]);

        $bpjsPatients = Patient::withBpjs()->get();

        $this->assertCount(1, $bpjsPatients);
        $this->assertTrue($bpjsPatients->first()->has_bpjs);
    }
}
```

**tests/Unit/Services/AppointmentServiceTest.php**:

```php
<?php

namespace Tests\Unit\Services;

use App\Models\Appointment;
use App\Models\Doctor;
use App\Models\User;
use App\Models\Clinic;
use App\Services\AppointmentService;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class AppointmentServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AppointmentService $appointmentService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->appointmentService = new AppointmentService();
    }

    public function test_can_get_available_time_slots()
    {
        $clinic = Clinic::factory()->create();
        $doctor = Doctor::factory()->create(['clinic_id' => $clinic->id]);
        $date = today()->addDay();

        $slots = $this->appointmentService->getAvailableTimeSlots($doctor->id, $date);

        $this->assertNotEmpty($slots);
        $this->assertArrayHasKey('time', $slots->first());
        $this->assertArrayHasKey('available', $slots->first());
    }

    public function test_slot_availability_check()
    {
        $clinic = Clinic::factory()->create();
        $doctor = Doctor::factory()->create(['clinic_id' => $clinic->id]);
        $date = today()->addDay();
        $time = '10:00';

        // No existing appointments - should be available
        $isAvailable = $this->appointmentService->isSlotAvailable($doctor->user_id, $date, $time);
        $this->assertTrue($isAvailable);

        // Create appointment at the same time
        Appointment::factory()->create([
            'doctor_id' => $doctor->user_id,
            'appointment_date' => $date,
            'appointment_time' => $time,
            'status' => 'scheduled'
        ]);

        // Should not be available now
        $isAvailable = $this->appointmentService->isSlotAvailable($doctor->user_id, $date, $time);
        $this->assertFalse($isAvailable);
    }

    public function test_can_create_appointment()
    {
        $clinic = Clinic::factory()->create();
        $doctor = Doctor::factory()->create(['clinic_id' => $clinic->id]);
        $patient = Patient::factory()->create(['clinic_id' => $clinic->id]);

        $appointmentData = [
            'patient_id' => $patient->id,
            'doctor_id' => $doctor->user_id,
            'clinic_id' => $clinic->id,
            'appointment_date' => today()->addDay(),
            'appointment_time' => '10:00',
            'duration' => 30,
            'type' => 'consultation',
            'reason' => 'Regular checkup',
            'created_by' => User::factory()->create()->id,
        ];

        $appointment = $this->appointmentService->createAppointment($appointmentData);

        $this->assertInstanceOf(Appointment::class, $appointment);
        $this->assertEquals($patient->id, $appointment->patient_id);
        $this->assertEquals($doctor->user_id, $appointment->doctor_id);
    }

    public function test_cannot_create_appointment_in_occupied_slot()
    {
        $clinic = Clinic::factory()->create();
        $doctor = Doctor::factory()->create(['clinic_id' => $clinic->id]);
        $patient = Patient::factory()->create(['clinic_id' => $clinic->id]);

        // Create existing appointment
        Appointment::factory()->create([
            'doctor_id' => $doctor->user_id,
            'appointment_date' => today()->addDay(),
            'appointment_time' => '10:00',
            'status' => 'scheduled'
        ]);

        $appointmentData = [
            'patient_id' => $patient->id,
            'doctor_id' => $doctor->user_id,
            'clinic_id' => $clinic->id,
            'appointment_date' => today()->addDay(),
            'appointment_time' => '10:00',
            'duration' => 30,
            'type' => 'consultation',
            'reason' => 'Regular checkup',
            'created_by' => User::factory()->create()->id,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The selected time slot is not available.');

        $this->appointmentService->createAppointment($appointmentData);
    }
}
```

### Step 4: Feature Tests

**tests/Feature/PatientManagementTest.php**:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Clinic;
use App\Models\Patient;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PatientManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Clinic $clinic;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->clinic = Clinic::factory()->create();
        $this->user = User::factory()->receptionist()->create([
            'clinic_id' => $this->clinic->id
        ]);
    }

    public function test_can_view_patients_list()
    {
        Patient::factory()->count(3)->create(['clinic_id' => $this->clinic->id]);

        $response = $this->actingAs($this->user)
            ->get(route('clinic.patients.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('clinic/patients/index')
                 ->has('patients.data', 3)
        );
    }

    public function test_can_create_patient()
    {
        $patientData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'national_id' => '****************',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'phone' => '************',
            'email' => '<EMAIL>',
            'address' => '123 Main St',
            'emergency_contact_name' => 'Jane Doe',
            'emergency_contact_phone' => '************',
            'emergency_contact_relationship' => 'spouse',
            'has_bpjs' => false,
            'create_user_account' => false,
        ];

        $response = $this->actingAs($this->user)
            ->post(route('clinic.patients.store'), $patientData);

        $response->assertRedirect();
        $this->assertDatabaseHas('patients', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'national_id' => '****************',
            'clinic_id' => $this->clinic->id,
        ]);
    }

    public function test_can_search_patients()
    {
        Patient::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'clinic_id' => $this->clinic->id
        ]);
        
        Patient::factory()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'clinic_id' => $this->clinic->id
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('clinic.patients.index', ['search' => 'John']));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('patients.data', 1)
                 ->where('patients.data.0.first_name', 'John')
        );
    }

    public function test_cannot_access_patients_from_other_clinic()
    {
        $otherClinic = Clinic::factory()->create();
        $otherPatient = Patient::factory()->create(['clinic_id' => $otherClinic->id]);

        $response = $this->actingAs($this->user)
            ->get(route('clinic.patients.show', $otherPatient));

        $response->assertStatus(403);
    }
}
```

### Step 5: API Tests

**tests/Feature/Api/AppointmentApiTest.php**:

```php
<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Clinic;
use App\Models\Patient;
use App\Models\Doctor;
use App\Models\Appointment;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class AppointmentApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Clinic $clinic;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->clinic = Clinic::factory()->create();
        $this->user = User::factory()->doctor()->create([
            'clinic_id' => $this->clinic->id
        ]);
        
        Sanctum::actingAs($this->user);
    }

    public function test_can_get_available_slots()
    {
        $doctor = Doctor::factory()->create(['clinic_id' => $this->clinic->id]);
        
        $response = $this->getJson(route('clinic.appointments.available-slots', [
            'doctor_id' => $doctor->user_id,
            'date' => today()->addDay()->format('Y-m-d'),
            'duration' => 30
        ]));

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'slots' => [
                         '*' => ['time', 'display_time', 'available', 'datetime']
                     ]
                 ]);
    }

    public function test_can_update_appointment_status()
    {
        $appointment = Appointment::factory()->create([
            'doctor_id' => $this->user->id,
            'clinic_id' => $this->clinic->id,
            'status' => 'scheduled'
        ]);

        $response = $this->putJson(route('clinic.appointments.update-status', $appointment), [
            'status' => 'confirmed',
            'notes' => 'Patient confirmed via phone'
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Appointment status updated successfully.'
                 ]);

        $this->assertDatabaseHas('appointments', [
            'id' => $appointment->id,
            'status' => 'confirmed'
        ]);
    }

    public function test_cannot_update_appointment_from_other_clinic()
    {
        $otherClinic = Clinic::factory()->create();
        $appointment = Appointment::factory()->create([
            'clinic_id' => $otherClinic->id
        ]);

        $response = $this->putJson(route('clinic.appointments.update-status', $appointment), [
            'status' => 'confirmed'
        ]);

        $response->assertStatus(403);
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive test suite** with unit, feature, and API tests
2. ✅ **Created model factories** for generating test data
3. ✅ **Implemented testing strategies** for different application layers
4. ✅ **Set up test configuration** and database setup
5. ✅ **Added security testing** for multi-clinic access controls
6. ✅ **Created API testing framework** for endpoint validation

### What We Have Now

- Comprehensive test coverage for all major functionality
- Model factories for generating realistic test data
- Feature tests for complete user workflows
- API tests for endpoint validation
- Security tests for access controls

### Next Steps

In **Chapter 14: Deployment and Production**, we'll:

- Set up production environment configuration
- Implement deployment strategies and CI/CD
- Configure security and monitoring
- Create backup and maintenance procedures

---

**Ready to continue?** Proceed to [Chapter 14: Deployment and Production](./chapter-14-deployment.md) to complete the tutorial series.
