# Chapter 5: Doctor and Staff Management

## Building Comprehensive Staff Management System

In this chapter, we'll create a complete doctor and staff management system that handles professional profiles, specializations, schedules, departments, and availability management. This system will support the operational needs of healthcare facilities.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create doctor and staff profile management system
- Build department and specialization management
- Implement staff scheduling and availability tracking
- Design staff directory with search capabilities
- Create role-based dashboards for different staff types
- Set up staff performance and workload tracking

## 🏥 Staff Management Requirements

### Core Features

1. **Doctor Profiles**: Professional information, specializations, qualifications
2. **Staff Directory**: Searchable directory with filters and contact information
3. **Department Management**: Organizational structure and assignments
4. **Schedule Management**: Working hours, availability, and time-off requests
5. **Specialization Tracking**: Medical specialties and sub-specialties
6. **Performance Metrics**: Workload tracking and performance indicators

### User Stories

- **Admin**: "I need to manage all staff members and their assignments"
- **Department Head**: "I need to oversee my department's staff and schedules"
- **Doctor**: "I want to manage my schedule and view my patient load"
- **Receptionist**: "I need to find available doctors for appointments"

## 🛠 Backend Implementation

### Step 1: Complete Doctor Model and Migration

First, let's complete the doctor migration we started in Chapter 2:

**database/migrations/xxxx_create_doctors_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            
            // Professional information
            $table->string('license_number')->unique();
            $table->string('specialization');
            $table->json('sub_specializations')->nullable();
            $table->text('qualifications');
            $table->integer('years_of_experience')->default(0);
            $table->text('biography')->nullable();
            
            // Practice information
            $table->decimal('consultation_fee', 10, 2)->default(0);
            $table->integer('consultation_duration')->default(30); // minutes
            $table->json('available_days'); // Days of the week
            $table->time('start_time')->default('08:00:00');
            $table->time('end_time')->default('17:00:00');
            
            // Settings
            $table->boolean('accepts_online_booking')->default(true);
            $table->integer('max_patients_per_day')->default(20);
            $table->json('settings')->nullable();
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['clinic_id', 'department_id', 'is_active']);
            $table->index('specialization');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
```

### Step 2: Staff Schedules Migration

Create staff schedule management:

```bash
php artisan make:migration create_staff_schedules_table
php artisan make:migration create_staff_time_off_table
```

**database/migrations/xxxx_create_staff_schedules_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('staff_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            
            // Schedule details
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->time('start_time');
            $table->time('end_time');
            $table->time('break_start')->nullable();
            $table->time('break_end')->nullable();
            
            // Schedule type
            $table->enum('schedule_type', ['regular', 'temporary', 'on_call'])->default('regular');
            $table->date('effective_from');
            $table->date('effective_until')->nullable();
            
            // Additional info
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            
            $table->timestamps();
            
            $table->index(['user_id', 'day_of_week', 'is_active']);
            $table->index(['clinic_id', 'day_of_week']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('staff_schedules');
    }
};
```

**database/migrations/xxxx_create_staff_time_off_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('staff_time_off', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Time off details
            $table->enum('type', ['vacation', 'sick_leave', 'personal', 'conference', 'emergency']);
            $table->date('start_date');
            $table->date('end_date');
            $table->time('start_time')->nullable(); // For partial day off
            $table->time('end_time')->nullable();
            
            // Request details
            $table->text('reason');
            $table->enum('status', ['pending', 'approved', 'rejected', 'cancelled'])->default('pending');
            $table->text('admin_notes')->nullable();
            
            // Timestamps
            $table->timestamp('requested_at')->useCurrent();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['start_date', 'end_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('staff_time_off');
    }
};
```

### Step 3: Enhanced Models

**app/Models/Doctor.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Doctor extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'clinic_id', 'department_id', 'license_number',
        'specialization', 'sub_specializations', 'qualifications',
        'years_of_experience', 'biography', 'consultation_fee',
        'consultation_duration', 'available_days', 'start_time',
        'end_time', 'accepts_online_booking', 'max_patients_per_day',
        'settings', 'is_active'
    ];

    protected function casts(): array
    {
        return [
            'sub_specializations' => 'array',
            'available_days' => 'array',
            'consultation_fee' => 'decimal:2',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'accepts_online_booking' => 'boolean',
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function schedules()
    {
        return $this->hasMany(StaffSchedule::class, 'user_id', 'user_id');
    }

    public function timeOff()
    {
        return $this->hasMany(StaffTimeOff::class, 'user_id', 'user_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBySpecialization($query, $specialization)
    {
        return $query->where('specialization', $specialization);
    }

    public function scopeAvailableForBooking($query)
    {
        return $query->where('accepts_online_booking', true)->where('is_active', true);
    }

    // Helper methods
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    public function isAvailableOn($dayOfWeek)
    {
        return in_array(strtolower($dayOfWeek), array_map('strtolower', $this->available_days));
    }

    public function getTodaysAppointmentCount()
    {
        return $this->appointments()
            ->whereDate('appointment_date', today())
            ->where('status', '!=', 'cancelled')
            ->count();
    }

    public function canAcceptMoreAppointments()
    {
        return $this->getTodaysAppointmentCount() < $this->max_patients_per_day;
    }
}
```

**app/Models/StaffSchedule.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StaffSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'clinic_id', 'day_of_week', 'start_time', 'end_time',
        'break_start', 'break_end', 'schedule_type', 'effective_from',
        'effective_until', 'notes', 'is_active'
    ];

    protected function casts(): array
    {
        return [
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'break_start' => 'datetime:H:i',
            'break_end' => 'datetime:H:i',
            'effective_from' => 'date',
            'effective_until' => 'date',
            'is_active' => 'boolean',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    public function scopeCurrent($query)
    {
        return $query->where('effective_from', '<=', today())
            ->where(function ($q) {
                $q->whereNull('effective_until')
                  ->orWhere('effective_until', '>=', today());
            });
    }
}
```

### Step 4: Staff Controller

Create **app/Http/Controllers/Clinic/StaffController.php**:

```php
<?php

namespace App\Http\Controllers\Clinic;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Doctor;
use App\Models\Department;
use App\Models\StaffSchedule;
use App\Models\StaffTimeOff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class StaffController extends Controller
{
    public function index(Request $request)
    {
        $query = User::with(['clinic', 'doctor.department'])
            ->where('clinic_id', auth()->user()->clinic_id)
            ->whereIn('role', ['doctor', 'nurse', 'receptionist']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->whereHas('doctor', function ($q) use ($request) {
                $q->where('department_id', $request->department);
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $staff = $query->orderBy('name')
            ->paginate(20)
            ->withQueryString();

        $departments = Department::where('clinic_id', auth()->user()->clinic_id)
            ->where('is_active', true)
            ->get();

        return Inertia::render('clinic/staff/index', [
            'staff' => $staff,
            'departments' => $departments,
            'filters' => $request->only(['search', 'role', 'department', 'status'])
        ]);
    }

    public function create()
    {
        $departments = Department::where('clinic_id', auth()->user()->clinic_id)
            ->where('is_active', true)
            ->get();

        return Inertia::render('clinic/staff/create', [
            'departments' => $departments
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8|confirmed',
            'role' => 'required|in:doctor,nurse,receptionist',
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'employee_id' => 'required|string|unique:users,employee_id',
            
            // Doctor-specific fields
            'department_id' => 'required_if:role,doctor|exists:departments,id',
            'license_number' => 'required_if:role,doctor|string|unique:doctors,license_number',
            'specialization' => 'required_if:role,doctor|string',
            'sub_specializations' => 'nullable|array',
            'qualifications' => 'required_if:role,doctor|string',
            'years_of_experience' => 'required_if:role,doctor|integer|min:0',
            'consultation_fee' => 'required_if:role,doctor|numeric|min:0',
            'consultation_duration' => 'required_if:role,doctor|integer|min:15|max:120',
        ]);

        DB::beginTransaction();
        
        try {
            // Create user
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => $validated['role'],
                'phone' => $validated['phone'],
                'address' => $validated['address'],
                'date_of_birth' => $validated['date_of_birth'],
                'gender' => $validated['gender'],
                'employee_id' => $validated['employee_id'],
                'clinic_id' => auth()->user()->clinic_id,
            ]);

            // Create doctor profile if role is doctor
            if ($validated['role'] === 'doctor') {
                Doctor::create([
                    'user_id' => $user->id,
                    'clinic_id' => auth()->user()->clinic_id,
                    'department_id' => $validated['department_id'],
                    'license_number' => $validated['license_number'],
                    'specialization' => $validated['specialization'],
                    'sub_specializations' => $validated['sub_specializations'] ?? [],
                    'qualifications' => $validated['qualifications'],
                    'years_of_experience' => $validated['years_of_experience'],
                    'consultation_fee' => $validated['consultation_fee'],
                    'consultation_duration' => $validated['consultation_duration'],
                    'available_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                ]);
            }

            DB::commit();

            return redirect()->route('clinic.staff.show', $user)
                ->with('success', 'Staff member created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create staff member. Please try again.']);
        }
    }

    public function show(User $staff)
    {
        $this->authorize('view', $staff);
        
        $staff->load([
            'clinic',
            'doctor.department',
            'doctor.appointments' => function ($query) {
                $query->with('patient')->latest()->limit(10);
            }
        ]);

        // Get recent schedules
        $schedules = StaffSchedule::where('user_id', $staff->id)
            ->current()
            ->active()
            ->orderBy('day_of_week')
            ->get();

        // Get upcoming time off
        $timeOff = StaffTimeOff::where('user_id', $staff->id)
            ->where('start_date', '>=', today())
            ->where('status', 'approved')
            ->orderBy('start_date')
            ->limit(5)
            ->get();

        return Inertia::render('clinic/staff/show', [
            'staff' => $staff,
            'schedules' => $schedules,
            'timeOff' => $timeOff
        ]);
    }
}
```

## 🎨 Frontend Implementation

### Step 1: Staff Types

Create **resources/js/types/staff.d.ts**:

```typescript
export interface Doctor {
    id: number;
    user_id: number;
    clinic_id: number;
    department_id: number;
    license_number: string;
    specialization: string;
    sub_specializations: string[];
    qualifications: string;
    years_of_experience: number;
    biography?: string;
    consultation_fee: number;
    consultation_duration: number;
    available_days: string[];
    start_time: string;
    end_time: string;
    accepts_online_booking: boolean;
    max_patients_per_day: number;
    settings?: any;
    is_active: boolean;
    user?: User;
    department?: Department;
    appointments_count?: number;
    today_appointments?: number;
}

export interface StaffSchedule {
    id: number;
    user_id: number;
    clinic_id: number;
    day_of_week: string;
    start_time: string;
    end_time: string;
    break_start?: string;
    break_end?: string;
    schedule_type: 'regular' | 'temporary' | 'on_call';
    effective_from: string;
    effective_until?: string;
    notes?: string;
    is_active: boolean;
}

export interface StaffTimeOff {
    id: number;
    user_id: number;
    type: 'vacation' | 'sick_leave' | 'personal' | 'conference' | 'emergency';
    start_date: string;
    end_date: string;
    start_time?: string;
    end_time?: string;
    reason: string;
    status: 'pending' | 'approved' | 'rejected' | 'cancelled';
    admin_notes?: string;
    requested_at: string;
    approved_at?: string;
}

export interface Department {
    id: number;
    clinic_id: number;
    name: string;
    code: string;
    description?: string;
    head_doctor_id?: number;
    services?: string[];
    is_active: boolean;
    doctors_count?: number;
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive staff management system** with doctor profiles
2. ✅ **Implemented department and specialization management**
3. ✅ **Created staff scheduling and availability tracking**
4. ✅ **Designed staff directory with advanced search**
5. ✅ **Added time-off request management system**
6. ✅ **Built TypeScript interfaces for staff data**

### What We Have Now

- Complete staff management with doctor profiles and specializations
- Department organization and assignment system
- Staff scheduling and availability tracking
- Time-off request and approval workflow
- Comprehensive staff directory with search and filters

### Next Steps

In **Chapter 6: Appointment Scheduling System**, we'll:

- Build calendar-based appointment booking system
- Implement time slot management and conflict resolution
- Create automated notifications and reminders
- Design patient and staff appointment interfaces

---

**Ready to continue?** Proceed to [Chapter 6: Appointment Scheduling System](./chapter-06-appointments.md) to build the appointment management system.
