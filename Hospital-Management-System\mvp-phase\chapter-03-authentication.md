# Chapter 03 - Authentication dan Authorization System

## Tujuan Chapter
Pada chapter ini, kita akan:
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ze untuk hospital context
- Implementasi role-based access control (RBAC)
- Setup middleware untuk authorization
- Membuat registration system untuk staff hospital
- Konfigurasi dashboard berdasarkan role

## Langkah 1: Kustomisasi User Model

### 1.1 Update User Model
Edit `app/Models/User.php`:
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'password',
        'role',
        'phone',
        'is_active',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    // Accessor untuk full name
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    // Role checking methods
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isDoctor(): bool
    {
        return $this->role === 'doctor';
    }

    public function isNurse(): bool
    {
        return $this->role === 'nurse';
    }

    public function isReceptionist(): bool
    {
        return $this->role === 'receptionist';
    }

    public function isPharmacist(): bool
    {
        return $this->role === 'pharmacist';
    }

    public function isLabTechnician(): bool
    {
        return $this->role === 'lab_technician';
    }

    // Relationships
    public function doctor()
    {
        return $this->hasOne(Doctor::class);
    }

    public function staff()
    {
        return $this->hasOne(Staff::class);
    }

    // Scope untuk active users
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope berdasarkan role
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }
}
```

## Langkah 2: Create Role Middleware

### 2.1 Create Role Middleware
```bash
php artisan make:middleware CheckRole
```

Edit `app/Http/Middleware/CheckRole.php`:
```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        if (!$user->is_active) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Akun Anda tidak aktif. Silakan hubungi administrator.');
        }

        if (in_array($user->role, $roles)) {
            return $next($request);
        }

        abort(403, 'Anda tidak memiliki akses ke halaman ini.');
    }
}
```

### 2.2 Register Middleware
Edit `bootstrap/app.php`:
```php
<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\CheckRole;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => CheckRole::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
```

## Langkah 3: Kustomisasi Registration

### 3.1 Update Registration Controller
Edit `app/Http/Controllers/Auth/RegisteredUserController.php`:
```php
<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    public function create(): Response
    {
        $roles = [
            'admin' => 'Administrator',
            'doctor' => 'Dokter',
            'nurse' => 'Perawat',
            'receptionist' => 'Resepsionis',
            'pharmacist' => 'Apoteker',
            'lab_technician' => 'Teknisi Lab',
        ];

        return Inertia::render('Auth/Register', [
            'roles' => $roles
        ]);
    }

    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'phone' => 'required|string|max:15',
            'role' => 'required|string|in:admin,doctor,nurse,receptionist,pharmacist,lab_technician',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->first_name . ' ' . $request->last_name,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'role' => $request->role,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        event(new Registered($user));

        Auth::login($user);

        return redirect()->route('dashboard');
    }
}
```

### 3.2 Update Registration Form
Edit `resources/js/Pages/Auth/Register.jsx`:
```jsx
import { useEffect } from 'react';
import GuestLayout from '@/Layouts/GuestLayout';
import InputError from '@/Components/InputError';
import InputLabel from '@/Components/InputLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import { Head, Link, useForm } from '@inertiajs/react';

export default function Register({ roles }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        role: '',
        password: '',
        password_confirmation: '',
    });

    useEffect(() => {
        return () => {
            reset('password', 'password_confirmation');
        };
    }, []);

    const submit = (e) => {
        e.preventDefault();
        post(route('register'));
    };

    return (
        <GuestLayout>
            <Head title="Registrasi Staff" />

            <form onSubmit={submit}>
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <InputLabel htmlFor="first_name" value="Nama Depan" />
                        <TextInput
                            id="first_name"
                            name="first_name"
                            value={data.first_name}
                            className="mt-1 block w-full"
                            autoComplete="given-name"
                            isFocused={true}
                            onChange={(e) => setData('first_name', e.target.value)}
                            required
                        />
                        <InputError message={errors.first_name} className="mt-2" />
                    </div>

                    <div>
                        <InputLabel htmlFor="last_name" value="Nama Belakang" />
                        <TextInput
                            id="last_name"
                            name="last_name"
                            value={data.last_name}
                            className="mt-1 block w-full"
                            autoComplete="family-name"
                            onChange={(e) => setData('last_name', e.target.value)}
                            required
                        />
                        <InputError message={errors.last_name} className="mt-2" />
                    </div>
                </div>

                <div className="mt-4">
                    <InputLabel htmlFor="email" value="Email" />
                    <TextInput
                        id="email"
                        type="email"
                        name="email"
                        value={data.email}
                        className="mt-1 block w-full"
                        autoComplete="username"
                        onChange={(e) => setData('email', e.target.value)}
                        required
                    />
                    <InputError message={errors.email} className="mt-2" />
                </div>

                <div className="mt-4">
                    <InputLabel htmlFor="phone" value="Nomor Telepon" />
                    <TextInput
                        id="phone"
                        type="tel"
                        name="phone"
                        value={data.phone}
                        className="mt-1 block w-full"
                        onChange={(e) => setData('phone', e.target.value)}
                        required
                    />
                    <InputError message={errors.phone} className="mt-2" />
                </div>

                <div className="mt-4">
                    <InputLabel htmlFor="role" value="Role/Jabatan" />
                    <select
                        id="role"
                        name="role"
                        value={data.role}
                        className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                        onChange={(e) => setData('role', e.target.value)}
                        required
                    >
                        <option value="">Pilih Role</option>
                        {Object.entries(roles).map(([key, value]) => (
                            <option key={key} value={key}>{value}</option>
                        ))}
                    </select>
                    <InputError message={errors.role} className="mt-2" />
                </div>

                <div className="mt-4">
                    <InputLabel htmlFor="password" value="Password" />
                    <TextInput
                        id="password"
                        type="password"
                        name="password"
                        value={data.password}
                        className="mt-1 block w-full"
                        autoComplete="new-password"
                        onChange={(e) => setData('password', e.target.value)}
                        required
                    />
                    <InputError message={errors.password} className="mt-2" />
                </div>

                <div className="mt-4">
                    <InputLabel htmlFor="password_confirmation" value="Konfirmasi Password" />
                    <TextInput
                        id="password_confirmation"
                        type="password"
                        name="password_confirmation"
                        value={data.password_confirmation}
                        className="mt-1 block w-full"
                        autoComplete="new-password"
                        onChange={(e) => setData('password_confirmation', e.target.value)}
                        required
                    />
                    <InputError message={errors.password_confirmation} className="mt-2" />
                </div>

                <div className="flex items-center justify-end mt-4">
                    <Link
                        href={route('login')}
                        className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Sudah punya akun?
                    </Link>

                    <PrimaryButton className="ms-4" disabled={processing}>
                        Daftar
                    </PrimaryButton>
                </div>
            </form>
        </GuestLayout>
    );
}
```

## Langkah 4: Setup Dashboard Routing

### 4.1 Update Web Routes
Edit `routes/web.php`:
```php
<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Hospital\DashboardController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'admin'])->name('dashboard');
});

// Doctor routes
Route::middleware(['auth', 'role:doctor'])->prefix('doctor')->name('doctor.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'doctor'])->name('dashboard');
});

// Nurse routes
Route::middleware(['auth', 'role:nurse'])->prefix('nurse')->name('nurse.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'nurse'])->name('dashboard');
});

// Receptionist routes
Route::middleware(['auth', 'role:receptionist'])->prefix('receptionist')->name('receptionist.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'receptionist'])->name('dashboard');
});

require __DIR__.'/auth.php';
```

### 4.2 Create Dashboard Controller
```bash
php artisan make:controller Hospital/DashboardController
```

Edit `app/Http/Controllers/Hospital/DashboardController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        // Redirect berdasarkan role
        switch ($user->role) {
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'doctor':
                return redirect()->route('doctor.dashboard');
            case 'nurse':
                return redirect()->route('nurse.dashboard');
            case 'receptionist':
                return redirect()->route('receptionist.dashboard');
            default:
                return $this->generalDashboard();
        }
    }

    public function admin()
    {
        return Inertia::render('Hospital/Admin/Dashboard', [
            'user' => auth()->user(),
            'stats' => [
                'total_patients' => 0, // Will be implemented later
                'total_doctors' => 0,
                'total_appointments_today' => 0,
                'total_staff' => 0,
            ]
        ]);
    }

    public function doctor()
    {
        return Inertia::render('Hospital/Doctor/Dashboard', [
            'user' => auth()->user(),
            'stats' => [
                'appointments_today' => 0,
                'patients_seen' => 0,
                'pending_appointments' => 0,
            ]
        ]);
    }

    public function nurse()
    {
        return Inertia::render('Hospital/Nurse/Dashboard', [
            'user' => auth()->user(),
            'stats' => [
                'patients_assigned' => 0,
                'tasks_pending' => 0,
                'medications_due' => 0,
            ]
        ]);
    }

    public function receptionist()
    {
        return Inertia::render('Hospital/Receptionist/Dashboard', [
            'user' => auth()->user(),
            'stats' => [
                'appointments_today' => 0,
                'walk_ins' => 0,
                'pending_registrations' => 0,
            ]
        ]);
    }

    private function generalDashboard()
    {
        return Inertia::render('Dashboard', [
            'user' => auth()->user()
        ]);
    }
}
```

## Langkah 5: Create Admin Seeder

### 5.1 Create Admin User Seeder
```bash
php artisan make:seeder AdminUserSeeder
```

Edit `database/seeders/AdminUserSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        User::create([
            'name' => 'Administrator',
            'first_name' => 'Admin',
            'last_name' => 'System',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'role' => 'admin',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create sample doctor
        User::create([
            'name' => 'Dr. Budi Santoso',
            'first_name' => 'Budi',
            'last_name' => 'Santoso',
            'email' => '<EMAIL>',
            'phone' => '081234567891',
            'role' => 'doctor',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
    }
}
```

### 5.2 Update DatabaseSeeder
Edit `database/seeders/DatabaseSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            DepartmentSeeder::class,
            AdminUserSeeder::class,
        ]);
    }
}
```

## Langkah 6: Testing Authentication

### 6.1 Run Seeders
```bash
php artisan db:seed --class=AdminUserSeeder
```

### 6.2 Test Login
1. Start server: `php artisan serve`
2. Akses `/login`
3. Login dengan:
   - Email: `<EMAIL>`
   - Password: `password`

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi patient management system
- Membuat CRUD operations untuk patients
- Setup patient registration form

## Checklist Completion

- [ ] User model dikustomisasi dengan role system
- [ ] Role middleware dibuat dan terdaftar
- [ ] Registration form mendukung role selection
- [ ] Dashboard routing berdasarkan role
- [ ] Admin user seeder berjalan
- [ ] Authentication system dapat ditest
- [ ] Authorization middleware berfungsi

**Estimasi Waktu**: 60-75 menit

**Difficulty Level**: Intermediate

**File yang Dibuat/Dimodifikasi**:
- User model (modified)
- CheckRole middleware
- RegisteredUserController (modified)
- Register.jsx (modified)
- DashboardController
- Web routes (modified)
- AdminUserSeeder
