# Clinic Management System - Quick Reference Guide

## 🚀 Quick Start Commands

### Initial Setup
```bash
# Clone and setup project
git clone [repository] clinic-management-system
cd clinic-management-system

# Install dependencies
composer install
npm install

# Environment setup
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate
php artisan db:seed

# Start development servers
php artisan serve &
npm run dev &
```

### Development Workflow
```bash
# Create new migration
php artisan make:migration create_table_name

# Create model with migration
php artisan make:model ModelName -m

# Create controller
php artisan make:controller Clinic/ControllerName

# Create policy
php artisan make:policy ModelPolicy

# Create service
php artisan make:service ServiceName

# Run tests
php artisan test

# Clear caches
php artisan optimize:clear
```

## 📊 Database Schema Overview

### Core Tables
```sql
-- Users (Extended from starter kit)
users: id, name, email, role, clinic_id, is_active

-- Clinics (Multi-clinic support)
clinics: id, name, code, address, bpjs_provider_code

-- Patients (Patient management)
patients: id, user_id, clinic_id, patient_number, national_id, 
         first_name, last_name, has_bpjs, bpjs_number

-- Doctors (Staff management)
doctors: id, user_id, clinic_id, department_id, license_number, 
        specialization, consultation_fee

-- Appointments (Scheduling)
appointments: id, patient_id, doctor_id, appointment_date, 
             status, notes

-- Medical Records (Patient history)
medical_records: id, patient_id, doctor_id, appointment_id, 
                diagnosis, treatment, notes

-- Prescriptions (Medication management)
prescriptions: id, patient_id, doctor_id, medical_record_id, 
              medications, instructions

-- BPJS Integration
bpjs_verifications: id, patient_id, verification_type, 
                   response_data, is_valid
```

## 🔐 Authentication & Authorization

### User Roles
```php
// Available roles
'admin'        // Full system access
'doctor'       // Medical operations
'nurse'        // Patient care support
'receptionist' // Front desk operations
'patient'      // Self-service access
```

### Permission System
```php
// Check permissions in controllers
$user->hasPermission('view_patients')
$user->canAccessClinic($clinicId)

// Middleware usage
Route::middleware(['permission:view_patients'])
Route::middleware(['role:doctor,nurse'])
Route::middleware(['clinic.access'])
```

### Frontend Authorization
```typescript
// React hooks
const { hasPermission, hasRole, isDoctor } = usePermissions();

// Usage in components
{hasPermission('create_patients') && (
    <Button>Create Patient</Button>
)}
```

## 🏥 Patient Management

### Patient Controller Actions
```php
// List patients with filters
GET /clinic/patients?search=john&bpjs_status=yes

// Create new patient
POST /clinic/patients
{
    "first_name": "John",
    "last_name": "Doe",
    "national_id": "1234567890123456",
    "has_bpjs": true,
    "bpjs_number": "0001234567890"
}

// Get patient details
GET /clinic/patients/{id}

// Update patient
PUT /clinic/patients/{id}
```

### Patient Search Filters
```typescript
interface PatientFilters {
    search?: string;           // Name, patient number, NIK, phone
    bpjs_status?: 'yes' | 'no'; // BPJS coverage
    gender?: 'male' | 'female'; // Gender filter
    age_min?: number;          // Minimum age
    age_max?: number;          // Maximum age
}
```

## 🛡 BPJS Integration

### Environment Variables
```env
BPJS_PPK_CODE=your_ppk_code
BPJS_CONS_ID=your_cons_id
BPJS_CONS_SECRET=your_cons_secret
BPJS_VCLAIM_USER_KEY=your_vclaim_key
BPJS_ANTREAN_USER_KEY=your_antrean_key
BPJS_ENVIRONMENT=development
```

### BPJS Service Usage
```php
// Verify patient by NIK
$bpjsService = new BPJSPatientService();
$result = $bpjsService->verifyPatientByNIK('1234567890123456');

// Verify by BPJS card
$result = $bpjsService->verifyPatientByCard('0001234567890');

// Create SEP
$sepService = new BPJSSEPService();
$sep = $sepService->createSEP($sepData);
```

### BPJS API Endpoints
```php
// Patient verification
POST /clinic/bpjs/verify/{patient}
{
    "verification_type": "nik|card",
    "identifier": "1234567890123456"
}

// Create SEP
POST /clinic/bpjs/sep/{patient}
{
    "jns_pelayanan": "2",
    "kls_rawat": "3",
    "diag_awal": "Z00.0"
}

// Get healthcare facilities
GET /clinic/bpjs/facilities?keyword=rumah+sakit&type=2
```

## 🎨 Frontend Components

### Common UI Components
```typescript
// Import from shadcn/ui
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Clinic-specific components
import BPJSVerification from '@/components/clinic/BPJSVerification';
import PatientCard from '@/components/clinic/PatientCard';
```

### Page Structure
```typescript
// Standard page layout
export default function PageName({ data }: Props) {
    return (
        <AppLayout>
            <Head title="Page Title" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <h1 className="text-3xl font-bold">Page Title</h1>
                    <Button>Action Button</Button>
                </div>

                {/* Content */}
                <Card>
                    <CardContent>
                        {/* Page content */}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
```

## 🔄 API Response Format

### Standard Response Structure
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data
    },
    "meta": {
        "current_page": 1,
        "total": 100,
        "per_page": 20
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Operation failed",
    "errors": {
        "field_name": ["Validation error message"]
    },
    "error_code": "VALIDATION_ERROR"
}
```

## 🧪 Testing

### Feature Tests
```php
// Test patient creation
public function test_can_create_patient()
{
    $user = User::factory()->create(['role' => 'receptionist']);
    
    $response = $this->actingAs($user)
        ->post('/clinic/patients', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'national_id' => '1234567890123456',
        ]);
    
    $response->assertRedirect();
    $this->assertDatabaseHas('patients', [
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);
}
```

### API Tests
```php
// Test BPJS verification
public function test_bpjs_verification()
{
    $patient = Patient::factory()->create();
    
    $response = $this->postJson("/clinic/bpjs/verify/{$patient->id}", [
        'verification_type' => 'nik',
        'identifier' => '1234567890123456',
    ]);
    
    $response->assertJson(['success' => true]);
}
```

## 📝 Common Patterns

### Controller Pattern
```php
class PatientController extends Controller
{
    public function index(Request $request)
    {
        // Apply filters and pagination
        // Return Inertia response
    }
    
    public function store(Request $request)
    {
        // Validate input
        // Create resource
        // Return redirect with success message
    }
}
```

### Service Pattern
```php
class PatientService
{
    public function createPatient(array $data): Patient
    {
        // Business logic
        // Database operations
        // Return result
    }
}
```

### React Component Pattern
```typescript
interface Props {
    // Define props
}

export default function Component({ prop }: Props) {
    // State management
    // Event handlers
    // Render JSX
}
```

## 🔧 Troubleshooting

### Common Issues
```bash
# Permission denied errors
php artisan cache:clear
php artisan config:clear

# Database connection issues
php artisan migrate:status
php artisan migrate:fresh --seed

# Frontend build issues
npm run build
npm run dev

# BPJS API errors
# Check environment variables
# Verify API credentials
# Check network connectivity
```

### Debug Commands
```bash
# Laravel debugging
php artisan tinker
php artisan route:list
php artisan config:show

# Database debugging
php artisan db:show
php artisan migrate:status

# Queue debugging
php artisan queue:work --verbose
```

---

**Quick Reference Version**: 1.0
**Last Updated**: Current tutorial progress
**For Full Documentation**: See individual chapter files
