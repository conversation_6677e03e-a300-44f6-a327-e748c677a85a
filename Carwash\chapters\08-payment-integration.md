# Chapter 8: Payment Integration

Welcome to Chapter 8! In this chapter, we'll integrate Stripe payment processing to handle service payments, manage payment confirmations, and implement secure payment workflows.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Install and configure Stripe for Laravel
- Create payment models and migrations
- Build secure payment processing workflows
- Handle payment confirmations and webhooks
- Implement payment history and refund management
- Add payment security and fraud protection
- Create payment-related notifications

## 📋 What We'll Cover

1. Installing and configuring Stripe
2. Creating Payment model and relationships
3. Building payment processing controller
4. Implementing secure payment forms
5. Handling Stripe webhooks
6. Adding payment history and management
7. Creating refund functionality
8. Testing the payment system

## 🛠 Step 1: Installing Stripe for Laravel

First, let's install the Stripe PHP SDK:

```bash
# Install Stripe PHP SDK
composer require stripe/stripe-php

# Install Laravel Cashier (optional, for advanced features)
composer require laravel/cashier
```

Add Stripe configuration to your `.env` file:

```env
# Stripe Configuration
STRIPE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

Add the Stripe configuration to `config/services.php`:

```php
// Add to config/services.php
'stripe' => [
    'model' => App\Models\User::class,
    'key' => env('STRIPE_KEY'),
    'secret' => env('STRIPE_SECRET'),
    'webhook' => [
        'secret' => env('STRIPE_WEBHOOK_SECRET'),
        'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
    ],
],
```

## 🛠 Step 2: Creating the Payment Model

Let's create a Payment model to track all payment transactions:

```bash
# Create Payment model and migration
php artisan make:model Payment -m
```

Edit the migration file `database/migrations/xxxx_xx_xx_xxxxxx_create_payments_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('payment_intent_id')->unique(); // Stripe Payment Intent ID
            $table->string('payment_method_id')->nullable(); // Stripe Payment Method ID
            $table->decimal('amount', 10, 2); // Payment amount
            $table->decimal('fee_amount', 10, 2)->default(0); // Stripe fees
            $table->decimal('net_amount', 10, 2); // Amount after fees
            $table->string('currency', 3)->default('usd');
            $table->enum('status', [
                'pending',
                'processing',
                'succeeded',
                'failed',
                'canceled',
                'refunded',
                'partially_refunded'
            ])->default('pending');
            $table->enum('type', ['payment', 'refund'])->default('payment');
            $table->string('stripe_charge_id')->nullable();
            $table->string('stripe_refund_id')->nullable();
            $table->json('payment_method_details')->nullable(); // Card details, etc.
            $table->json('metadata')->nullable(); // Additional data
            $table->text('failure_reason')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('payment_intent_id');
            $table->index('status');
            $table->index('type');
            $table->index(['booking_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
```

Now edit the Payment model `app/Models/Payment.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_id',
        'customer_id',
        'payment_intent_id',
        'payment_method_id',
        'amount',
        'fee_amount',
        'net_amount',
        'currency',
        'status',
        'type',
        'stripe_charge_id',
        'stripe_refund_id',
        'payment_method_details',
        'metadata',
        'failure_reason',
        'paid_at',
        'failed_at',
        'refunded_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'payment_method_details' => 'array',
        'metadata' => 'array',
        'paid_at' => 'datetime',
        'failed_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCEEDED = 'succeeded';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELED = 'canceled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';

    // Type constants
    const TYPE_PAYMENT = 'payment';
    const TYPE_REFUND = 'refund';

    // Relationships
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    public function getFormattedFeeAmountAttribute(): string
    {
        return '$' . number_format($this->fee_amount, 2);
    }

    public function getFormattedNetAmountAttribute(): string
    {
        return '$' . number_format($this->net_amount, 2);
    }

    public function getStatusBadgeAttribute(): string
    {
        $badges = [
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_PROCESSING => 'bg-blue-100 text-blue-800',
            self::STATUS_SUCCEEDED => 'bg-green-100 text-green-800',
            self::STATUS_FAILED => 'bg-red-100 text-red-800',
            self::STATUS_CANCELED => 'bg-gray-100 text-gray-800',
            self::STATUS_REFUNDED => 'bg-purple-100 text-purple-800',
            self::STATUS_PARTIALLY_REFUNDED => 'bg-orange-100 text-orange-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getPaymentMethodTypeAttribute(): string
    {
        if (!$this->payment_method_details) {
            return 'Unknown';
        }

        $details = $this->payment_method_details;
        
        if (isset($details['card'])) {
            return 'Card (**** ' . $details['card']['last4'] . ')';
        }

        return ucfirst($details['type'] ?? 'Unknown');
    }

    // Scopes
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCEEDED);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeRefunded($query)
    {
        return $query->whereIn('status', [self::STATUS_REFUNDED, self::STATUS_PARTIALLY_REFUNDED]);
    }

    public function scopeForBooking($query, $bookingId)
    {
        return $query->where('booking_id', $bookingId);
    }

    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    // Helper methods
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_SUCCEEDED;
    }

    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isRefunded(): bool
    {
        return in_array($this->status, [self::STATUS_REFUNDED, self::STATUS_PARTIALLY_REFUNDED]);
    }

    public function canBeRefunded(): bool
    {
        return $this->isSuccessful() && !$this->isRefunded();
    }

    public function markAsSucceeded($chargeId = null, $paidAt = null): bool
    {
        return $this->update([
            'status' => self::STATUS_SUCCEEDED,
            'stripe_charge_id' => $chargeId,
            'paid_at' => $paidAt ?: now(),
        ]);
    }

    public function markAsFailed($reason = null, $failedAt = null): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'failure_reason' => $reason,
            'failed_at' => $failedAt ?: now(),
        ]);
    }

    public function markAsRefunded($refundId = null, $refundedAt = null): bool
    {
        return $this->update([
            'status' => self::STATUS_REFUNDED,
            'stripe_refund_id' => $refundId,
            'refunded_at' => $refundedAt ?: now(),
        ]);
    }

    public function calculateFees(): void
    {
        // Stripe fee calculation (2.9% + $0.30 for US cards)
        $stripeFee = ($this->amount * 0.029) + 0.30;
        $this->update([
            'fee_amount' => round($stripeFee, 2),
            'net_amount' => round($this->amount - $stripeFee, 2),
        ]);
    }
}
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating the Payment Controller

Let's create a comprehensive payment controller:

```bash
# Create payment controller
php artisan make:controller PaymentController
```

Edit `app/Http/Controllers/PaymentController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function createPaymentIntent(Request $request)
    {
        $validated = $request->validate([
            'booking_id' => 'required|exists:bookings,id',
        ]);

        $booking = Booking::with(['customer', 'services'])->findOrFail($validated['booking_id']);

        // Check if booking can be paid
        if ($booking->payment_status === 'paid') {
            return response()->json(['error' => 'Booking is already paid'], 400);
        }

        try {
            // Create or retrieve existing payment intent
            $payment = Payment::where('booking_id', $booking->id)
                             ->where('status', Payment::STATUS_PENDING)
                             ->first();

            if (!$payment) {
                // Create Stripe Payment Intent
                $paymentIntent = PaymentIntent::create([
                    'amount' => $booking->total_amount * 100, // Convert to cents
                    'currency' => 'usd',
                    'metadata' => [
                        'booking_id' => $booking->id,
                        'customer_id' => $booking->customer_id,
                        'booking_number' => $booking->booking_number,
                    ],
                    'automatic_payment_methods' => [
                        'enabled' => true,
                    ],
                ]);

                // Create payment record
                $payment = Payment::create([
                    'booking_id' => $booking->id,
                    'customer_id' => $booking->customer_id,
                    'payment_intent_id' => $paymentIntent->id,
                    'amount' => $booking->total_amount,
                    'currency' => 'usd',
                    'status' => Payment::STATUS_PENDING,
                    'type' => Payment::TYPE_PAYMENT,
                ]);

                $payment->calculateFees();
            } else {
                // Retrieve existing payment intent
                $paymentIntent = PaymentIntent::retrieve($payment->payment_intent_id);
            }

            return response()->json([
                'client_secret' => $paymentIntent->client_secret,
                'payment_id' => $payment->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Payment Intent Creation Failed: ' . $e->getMessage());
            return response()->json(['error' => 'Payment setup failed'], 500);
        }
    }

    public function confirmPayment(Request $request)
    {
        $validated = $request->validate([
            'payment_intent_id' => 'required|string',
        ]);

        try {
            $paymentIntent = PaymentIntent::retrieve($validated['payment_intent_id']);
            $payment = Payment::where('payment_intent_id', $paymentIntent->id)->firstOrFail();

            if ($paymentIntent->status === 'succeeded') {
                $payment->markAsSucceeded($paymentIntent->charges->data[0]->id ?? null);
                
                // Update booking payment status
                $payment->booking->update([
                    'payment_status' => 'paid',
                    'payment_method' => $paymentIntent->payment_method_types[0] ?? 'card',
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment confirmed successfully',
                    'payment' => $payment,
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Payment not completed',
            ]);

        } catch (\Exception $e) {
            Log::error('Payment Confirmation Failed: ' . $e->getMessage());
            return response()->json(['error' => 'Payment confirmation failed'], 500);
        }
    }

    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid payload in webhook: ' . $e->getMessage());
            return response('Invalid payload', 400);
        } catch (SignatureVerificationException $e) {
            Log::error('Invalid signature in webhook: ' . $e->getMessage());
            return response('Invalid signature', 400);
        }

        // Handle the event
        switch ($event->type) {
            case 'payment_intent.succeeded':
                $this->handlePaymentSucceeded($event->data->object);
                break;
            case 'payment_intent.payment_failed':
                $this->handlePaymentFailed($event->data->object);
                break;
            case 'charge.dispute.created':
                $this->handleChargeDispute($event->data->object);
                break;
            default:
                Log::info('Unhandled webhook event type: ' . $event->type);
        }

        return response('Webhook handled', 200);
    }

    private function handlePaymentSucceeded($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment && !$payment->isSuccessful()) {
            $payment->markAsSucceeded($paymentIntent->charges->data[0]->id ?? null);

            // Update booking
            $payment->booking->update([
                'payment_status' => 'paid',
                'payment_method' => $paymentIntent->payment_method_types[0] ?? 'card',
            ]);

            // Store payment method details
            if (isset($paymentIntent->charges->data[0]->payment_method_details)) {
                $payment->update([
                    'payment_method_details' => $paymentIntent->charges->data[0]->payment_method_details,
                ]);
            }

            Log::info("Payment succeeded for booking {$payment->booking_id}");
        }
    }

    private function handlePaymentFailed($paymentIntent)
    {
        $payment = Payment::where('payment_intent_id', $paymentIntent->id)->first();

        if ($payment && !$payment->isFailed()) {
            $payment->markAsFailed($paymentIntent->last_payment_error->message ?? 'Payment failed');

            Log::info("Payment failed for booking {$payment->booking_id}");
        }
    }

    private function handleChargeDispute($dispute)
    {
        // Handle charge disputes
        Log::warning("Charge dispute created: {$dispute->id}");
        // Add your dispute handling logic here
    }

    public function refund(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'amount' => 'nullable|numeric|min:0.01|max:' . $payment->amount,
            'reason' => 'nullable|string|max:500',
        ]);

        if (!$payment->canBeRefunded()) {
            return redirect()->back()->with('error', 'This payment cannot be refunded.');
        }

        try {
            $refundAmount = $validated['amount'] ?? $payment->amount;

            // Create Stripe refund
            $refund = \Stripe\Refund::create([
                'charge' => $payment->stripe_charge_id,
                'amount' => $refundAmount * 100, // Convert to cents
                'reason' => 'requested_by_customer',
                'metadata' => [
                    'booking_id' => $payment->booking_id,
                    'reason' => $validated['reason'] ?? 'Refund requested',
                ],
            ]);

            // Create refund payment record
            Payment::create([
                'booking_id' => $payment->booking_id,
                'customer_id' => $payment->customer_id,
                'payment_intent_id' => $payment->payment_intent_id,
                'amount' => -$refundAmount, // Negative amount for refund
                'currency' => $payment->currency,
                'status' => Payment::STATUS_SUCCEEDED,
                'type' => Payment::TYPE_REFUND,
                'stripe_refund_id' => $refund->id,
                'metadata' => [
                    'original_payment_id' => $payment->id,
                    'reason' => $validated['reason'] ?? 'Refund requested',
                ],
            ]);

            // Update original payment status
            if ($refundAmount >= $payment->amount) {
                $payment->markAsRefunded($refund->id);
                $payment->booking->update(['payment_status' => 'refunded']);
            } else {
                $payment->update(['status' => Payment::STATUS_PARTIALLY_REFUNDED]);
                $payment->booking->update(['payment_status' => 'partial']);
            }

            return redirect()->back()->with('success', 'Refund processed successfully.');

        } catch (\Exception $e) {
            Log::error('Refund Failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Refund processing failed.');
        }
    }

    public function paymentHistory(Request $request)
    {
        $query = Payment::with(['booking', 'customer']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('payment_intent_id', 'like', "%{$request->search}%")
                  ->orWhereHas('booking', function ($bookingQuery) use ($request) {
                      $bookingQuery->where('booking_number', 'like', "%{$request->search}%");
                  })
                  ->orWhereHas('customer', function ($customerQuery) use ($request) {
                      $customerQuery->where('first_name', 'like', "%{$request->search}%")
                                   ->orWhere('last_name', 'like', "%{$request->search}%");
                  });
            });
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        return view('payments.history', compact('payments'));
    }
}
```

## 🛠 Step 4: Adding Payment Routes

Add the payment routes to `routes/web.php`:

```php
// Payment routes - accessible by staff and admin
Route::middleware(['auth', 'role:staff,admin'])->group(function () {
    Route::get('/payments/history', [PaymentController::class, 'paymentHistory'])->name('payments.history');
    Route::post('/payments/{payment}/refund', [PaymentController::class, 'refund'])->name('payments.refund');
});

// Payment processing routes - accessible by all authenticated users
Route::middleware(['auth'])->group(function () {
    Route::post('/payments/create-intent', [PaymentController::class, 'createPaymentIntent'])->name('payments.create-intent');
    Route::post('/payments/confirm', [PaymentController::class, 'confirmPayment'])->name('payments.confirm');
});

// Webhook route - no authentication required
Route::post('/stripe/webhook', [PaymentController::class, 'handleWebhook'])->name('stripe.webhook');
```

## 🛠 Step 5: Updating Booking Model

Add payment relationship to the Booking model. Edit `app/Models/Booking.php`:

```php
// Add to the Booking model relationships section
public function payments(): HasMany
{
    return $this->hasMany(Payment::class);
}

public function successfulPayments(): HasMany
{
    return $this->hasMany(Payment::class)->where('status', Payment::STATUS_SUCCEEDED);
}

// Add helper methods
public function getTotalPaidAttribute(): float
{
    return $this->successfulPayments()
                ->where('type', Payment::TYPE_PAYMENT)
                ->sum('amount');
}

public function getTotalRefundedAttribute(): float
{
    return $this->payments()
                ->where('type', Payment::TYPE_REFUND)
                ->where('status', Payment::STATUS_SUCCEEDED)
                ->sum('amount');
}

public function hasSuccessfulPayment(): bool
{
    return $this->successfulPayments()->exists();
}
```

## 🛠 Step 6: Creating Payment Views

Now let's create the frontend views for payment processing and management.

### 6.1 Payment History View

Create `resources/views/payments/history.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Payment History
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('payments.history') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                            <input type="text" name="search" id="search" value="{{ request('search') }}"
                                   placeholder="Payment ID, Booking #, Customer..."
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                                <option value="succeeded" {{ request('status') == 'succeeded' ? 'selected' : '' }}>Succeeded</option>
                                <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>Failed</option>
                                <option value="refunded" {{ request('status') == 'refunded' ? 'selected' : '' }}>Refunded</option>
                            </select>
                        </div>

                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
                            <select name="type" id="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Types</option>
                                <option value="payment" {{ request('type') == 'payment' ? 'selected' : '' }}>Payment</option>
                                <option value="refund" {{ request('type') == 'refund' ? 'selected' : '' }}>Refund</option>
                            </select>
                        </div>

                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700">From Date</label>
                            <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Payment Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($totalRevenue, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Successful Payments</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $successfulPayments }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Failed Payments</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $failedPayments }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Refunds</p>
                                <p class="text-2xl font-semibold text-gray-900">${{ number_format($totalRefunds, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payments Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($payments as $payment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ substr($payment->payment_intent_id, -8) }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ $payment->payment_method_type }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <a href="{{ route('bookings.show', $payment->booking) }}"
                                                   class="text-indigo-600 hover:text-indigo-900">
                                                    #{{ $payment->booking->booking_number }}
                                                </a>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $payment->customer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $payment->customer->email }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                @if($payment->type === 'refund')
                                                    <span class="text-red-600">-${{ number_format(abs($payment->amount), 2) }}</span>
                                                @else
                                                    <span class="text-green-600">${{ number_format($payment->amount, 2) }}</span>
                                                @endif
                                            </div>
                                            @if($payment->fee_amount > 0)
                                                <div class="text-xs text-gray-500">Fee: ${{ number_format($payment->fee_amount, 2) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($payment->type === 'payment') bg-blue-100 text-blue-800
                                                @else bg-purple-100 text-purple-800 @endif">
                                                {{ ucfirst($payment->type) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $payment->status_badge }}">
                                                {{ ucfirst(str_replace('_', ' ', $payment->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div>{{ $payment->created_at->format('M j, Y') }}</div>
                                            <div class="text-xs text-gray-500">{{ $payment->created_at->format('g:i A') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            @if($payment->canBeRefunded())
                                                <button onclick="openRefundModal({{ $payment->id }}, '{{ $payment->formatted_amount }}', {{ $payment->amount }})"
                                                        class="text-red-600 hover:text-red-900 mr-3">
                                                    Refund
                                                </button>
                                            @endif
                                            <a href="#" onclick="showPaymentDetails({{ $payment->id }})"
                                               class="text-indigo-600 hover:text-indigo-900">
                                                Details
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                            No payments found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $payments->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refund Modal -->
    <div id="refundModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Process Refund</h3>
                <form id="refundForm" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label for="refund_amount" class="block text-sm font-medium text-gray-700">Refund Amount</label>
                        <input type="number" name="amount" id="refund_amount" step="0.01"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <p class="mt-1 text-sm text-gray-500">Leave empty for full refund</p>
                    </div>
                    <div class="mb-4">
                        <label for="refund_reason" class="block text-sm font-medium text-gray-700">Reason</label>
                        <textarea name="reason" id="refund_reason" rows="3"
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                  placeholder="Optional reason for refund"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeRefundModal()"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit"
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Process Refund
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openRefundModal(paymentId, formattedAmount, maxAmount) {
            document.getElementById('refundForm').action = `/payments/${paymentId}/refund`;
            document.getElementById('refund_amount').max = maxAmount;
            document.getElementById('refund_amount').placeholder = `Max: ${formattedAmount}`;
            document.getElementById('refundModal').classList.remove('hidden');
        }

        function closeRefundModal() {
            document.getElementById('refundModal').classList.add('hidden');
            document.getElementById('refundForm').reset();
        }

        function showPaymentDetails(paymentId) {
            // Implement payment details modal or redirect
            alert('Payment details functionality to be implemented');
        }
    </script>
</x-app-layout>
```

### 6.2 Payment Checkout View

Create `resources/views/payments/checkout.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Payment Checkout
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Booking Summary -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Booking Summary</h3>

                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Booking Number:</span>
                                <span class="text-sm font-medium text-gray-900">#{{ $booking->booking_number }}</span>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Customer:</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->customer->name }}</span>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Date & Time:</span>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ $booking->booking_date->format('M j, Y') }} at {{ $booking->booking_time->format('g:i A') }}
                                </span>
                            </div>

                            <div class="border-t pt-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Services:</h4>
                                <div class="space-y-2">
                                    @foreach($booking->services as $service)
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">{{ $service->name }}</span>
                                            <span class="text-sm font-medium text-gray-900">${{ number_format($service->pivot->price, 2) }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="border-t pt-4">
                                <div class="flex justify-between">
                                    <span class="text-base font-medium text-gray-900">Total Amount:</span>
                                    <span class="text-lg font-bold text-green-600">${{ number_format($booking->total_amount, 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>

                        <form id="payment-form">
                            <div id="payment-element">
                                <!-- Stripe Elements will create form elements here -->
                            </div>

                            <div class="mt-6">
                                <button id="submit-button"
                                        class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span id="button-text">Pay ${{ number_format($booking->total_amount, 2) }}</span>
                                    <div id="spinner" class="hidden">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Processing...
                                    </div>
                                </button>
                            </div>

                            <div id="payment-message" class="hidden mt-4 p-4 rounded-md"></div>
                        </form>

                        <!-- Security Information -->
                        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                <span class="text-sm text-gray-600">Your payment information is secure and encrypted</span>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                Powered by Stripe • PCI DSS Compliant
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stripe JavaScript -->
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        const stripe = Stripe('{{ config('services.stripe.key') }}');
        let elements;
        let paymentElement;

        initialize();

        async function initialize() {
            // Create payment intent
            const response = await fetch('{{ route('payments.create-intent') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    booking_id: {{ $booking->id }}
                })
            });

            const { client_secret } = await response.json();

            elements = stripe.elements({ clientSecret: client_secret });
            paymentElement = elements.create('payment');
            paymentElement.mount('#payment-element');
        }

        document.getElementById('payment-form').addEventListener('submit', handleSubmit);

        async function handleSubmit(e) {
            e.preventDefault();
            setLoading(true);

            const { error } = await stripe.confirmPayment({
                elements,
                confirmParams: {
                    return_url: '{{ route('payments.success', ['booking' => $booking->id]) }}',
                },
            });

            if (error.type === "card_error" || error.type === "validation_error") {
                showMessage(error.message, 'error');
            } else {
                showMessage("An unexpected error occurred.", 'error');
            }

            setLoading(false);
        }

        function showMessage(messageText, type = 'error') {
            const messageContainer = document.querySelector("#payment-message");
            messageContainer.classList.remove('hidden');
            messageContainer.textContent = messageText;

            if (type === 'error') {
                messageContainer.className = 'mt-4 p-4 rounded-md bg-red-50 border border-red-200 text-red-700';
            } else {
                messageContainer.className = 'mt-4 p-4 rounded-md bg-green-50 border border-green-200 text-green-700';
            }
        }

        function setLoading(isLoading) {
            const submitButton = document.querySelector("#submit-button");
            const buttonText = document.querySelector("#button-text");
            const spinner = document.querySelector("#spinner");

            if (isLoading) {
                submitButton.disabled = true;
                buttonText.classList.add('hidden');
                spinner.classList.remove('hidden');
            } else {
                submitButton.disabled = false;
                buttonText.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
        }
    </script>
</x-app-layout>
```

### 6.3 Payment Success View

Create `resources/views/payments/success.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Payment Successful
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8 text-center">
                    <!-- Success Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>

                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h3>
                    <p class="text-gray-600 mb-8">Your payment has been processed successfully.</p>

                    <!-- Payment Details -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                            <div>
                                <span class="text-sm text-gray-500">Booking Number:</span>
                                <div class="font-medium text-gray-900">#{{ $booking->booking_number }}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Amount Paid:</span>
                                <div class="font-medium text-green-600">${{ number_format($payment->amount, 2) }}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Payment Method:</span>
                                <div class="font-medium text-gray-900">{{ $payment->payment_method_type }}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Transaction ID:</span>
                                <div class="font-medium text-gray-900">{{ substr($payment->payment_intent_id, -8) }}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Date & Time:</span>
                                <div class="font-medium text-gray-900">
                                    {{ $booking->booking_date->format('M j, Y') }} at {{ $booking->booking_time->format('g:i A') }}
                                </div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Status:</span>
                                <div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Confirmed
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Services Summary -->
                    <div class="bg-blue-50 rounded-lg p-6 mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Services Booked</h4>
                        <div class="space-y-2">
                            @foreach($booking->services as $service)
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-700">{{ $service->name }}</span>
                                    <span class="font-medium text-gray-900">${{ number_format($service->pivot->price, 2) }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Next Steps -->
                    <div class="text-left mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">What's Next?</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-start">
                                <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                You will receive a confirmation email shortly
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Please arrive 10 minutes before your scheduled time
                            </li>
                            <li class="flex items-start">
                                <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Bring your vehicle registration and keys
                            </li>
                        </ul>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('bookings.show', $booking) }}"
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg">
                            View Booking Details
                        </a>
                        <a href="{{ route('bookings.my') }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg">
                            My Bookings
                        </a>
                        <a href="{{ route('dashboard') }}"
                           class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg">
                            Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 6.4 Payment Failure View

Create `resources/views/payments/failed.blade.php`:

```blade
<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Payment Failed
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8 text-center">
                    <!-- Error Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>

                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Payment Failed</h3>
                    <p class="text-gray-600 mb-8">We were unable to process your payment. Please try again.</p>

                    <!-- Error Details -->
                    @if(isset($error_message))
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-red-800">Error Details</h4>
                                    <p class="mt-1 text-sm text-red-700">{{ $error_message }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Booking Details -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Booking Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                            <div>
                                <span class="text-sm text-gray-500">Booking Number:</span>
                                <div class="font-medium text-gray-900">#{{ $booking->booking_number }}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Amount:</span>
                                <div class="font-medium text-gray-900">${{ number_format($booking->total_amount, 2) }}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Date & Time:</span>
                                <div class="font-medium text-gray-900">
                                    {{ $booking->booking_date->format('M j, Y') }} at {{ $booking->booking_time->format('g:i A') }}
                                </div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Status:</span>
                                <div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Payment Pending
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Common Issues -->
                    <div class="text-left mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Common Issues & Solutions</h4>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <svg class="h-5 w-5 text-blue-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-medium text-gray-900">Insufficient Funds</h5>
                                    <p class="text-sm text-gray-600">Check your account balance or try a different payment method</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg class="h-5 w-5 text-blue-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-medium text-gray-900">Card Declined</h5>
                                    <p class="text-sm text-gray-600">Contact your bank or try a different card</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg class="h-5 w-5 text-blue-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-medium text-gray-900">Incorrect Information</h5>
                                    <p class="text-sm text-gray-600">Verify card number, expiry date, and CVV are correct</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <svg class="h-5 w-5 text-blue-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h5 class="font-medium text-gray-900">Network Issues</h5>
                                    <p class="text-sm text-gray-600">Check your internet connection and try again</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('payments.checkout', $booking) }}"
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg">
                            Try Payment Again
                        </a>
                        <a href="{{ route('bookings.show', $booking) }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg">
                            View Booking
                        </a>
                        <a href="{{ route('contact') }}"
                           class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg">
                            Contact Support
                        </a>
                    </div>

                    <!-- Alternative Payment Methods -->
                    <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Need Help?</h4>
                        <p class="text-sm text-blue-700">
                            If you continue to experience issues, please contact our support team at
                            <a href="mailto:<EMAIL>" class="underline"><EMAIL></a>
                            or call us at <a href="tel:+1234567890" class="underline">(*************</a>.
                        </p>
                        <p class="text-xs text-blue-600 mt-2">
                            You can also pay in person when you arrive for your appointment.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
```

### 6.5 Adding Payment Routes

Update your `routes/web.php` to include the payment view routes:

```php
// Payment view routes - accessible by authenticated users
Route::middleware(['auth'])->group(function () {
    Route::get('/payments/checkout/{booking}', [PaymentController::class, 'showCheckout'])->name('payments.checkout');
    Route::get('/payments/success/{booking}', [PaymentController::class, 'showSuccess'])->name('payments.success');
    Route::get('/payments/failed/{booking}', [PaymentController::class, 'showFailed'])->name('payments.failed');
});
```

### 6.6 Adding Controller Methods for Views

Add these methods to your `PaymentController.php`:

```php
public function showCheckout(Booking $booking)
{
    // Ensure user can access this booking
    if (auth()->user()->role === 'customer' && $booking->customer_id !== auth()->id()) {
        abort(403);
    }

    // Check if booking can be paid
    if ($booking->payment_status === 'paid') {
        return redirect()->route('bookings.show', $booking)
                        ->with('info', 'This booking has already been paid.');
    }

    return view('payments.checkout', compact('booking'));
}

public function showSuccess(Booking $booking)
{
    // Ensure user can access this booking
    if (auth()->user()->role === 'customer' && $booking->customer_id !== auth()->id()) {
        abort(403);
    }

    $payment = $booking->payments()->where('status', 'succeeded')->latest()->first();

    if (!$payment) {
        return redirect()->route('payments.checkout', $booking)
                        ->with('error', 'No successful payment found for this booking.');
    }

    return view('payments.success', compact('booking', 'payment'));
}

public function showFailed(Booking $booking)
{
    // Ensure user can access this booking
    if (auth()->user()->role === 'customer' && $booking->customer_id !== auth()->id()) {
        abort(403);
    }

    $error_message = request('error_message');

    return view('payments.failed', compact('booking', 'error_message'));
}
```

## 🧪 Testing the Payment System

1. **Test Payment Intent Creation**:
   - Create a booking
   - Generate payment intent
   - Verify Stripe dashboard shows the intent

2. **Test Payment Processing**:
   - Use Stripe test cards
   - Process successful payments
   - Test failed payments

3. **Test Webhooks**:
   - Configure webhook endpoint in Stripe
   - Test payment success webhooks
   - Verify payment status updates

4. **Test Refunds**:
   - Process refunds through admin interface
   - Verify refund records creation
   - Check Stripe dashboard for refunds

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Installed and configured Stripe for Laravel
✅ Created Payment model with comprehensive tracking
✅ Built secure payment processing workflows
✅ Implemented payment confirmations and webhooks
✅ Added payment history and refund management
✅ Created payment security and fraud protection
✅ Built payment-related notifications

### Payment Features Implemented:
- **Stripe Integration**: Secure payment processing with Stripe
- **Payment Tracking**: Comprehensive payment history and status
- **Webhook Handling**: Real-time payment status updates
- **Refund Management**: Full and partial refund capabilities
- **Security**: Webhook signature verification and secure processing
- **Payment Methods**: Support for multiple payment types
- **Fee Calculation**: Automatic Stripe fee calculation and tracking

## 🚀 What's Next?

In the next chapter, we'll:
- Build advanced reporting with charts and analytics
- Create revenue tracking and forecasting
- Add customer analytics and insights
- Implement performance metrics and KPIs
- Create exportable reports and data visualization

---

**Ready for advanced analytics?** Let's move on to [Chapter 9: Advanced Reporting and Analytics](./09-advanced-reporting-analytics.md)!
