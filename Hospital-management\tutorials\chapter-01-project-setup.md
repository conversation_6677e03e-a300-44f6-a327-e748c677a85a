# Chapter 1: Project Setup and Environment Configuration

## Overview
Welcome to the Hospital Employee Management System tutorial series! In this first chapter, we'll set up a modern Laravel 12 application with React frontend specifically designed for Indonesian healthcare institutions. By the end of this chapter, you'll have a fully configured development environment ready for building a comprehensive employee management system.

## Learning Objectives
- Set up Laravel 12 with React starter kit
- Configure development environment for Indonesian healthcare context
- Understand project structure and architecture decisions
- Set up database and basic configuration

## Prerequisites
- PHP 8.2 or higher installed
- Composer installed globally
- Node.js 18+ and NPM installed
- MySQL or PostgreSQL database server
- Basic understanding of Laravel and React concepts

## Duration
45-60 minutes

---

## Step 1: Installing Laravel 12 with React Starter Kit

### 1.1 Install Laravel CLI
First, ensure you have the latest Laravel installer:

```bash
composer global require laravel/installer
```

### 1.2 Create New Project with React Starter Kit
Laravel 12 introduces new starter kits that include React with TypeScript, Inertia.js, and modern tooling:

```bash
laravel new hospital-employee-management
```

When prompted, select the following options:
- **Starter kit**: React
- **TypeScript**: Yes
- **Authentication**: <PERSON><PERSON>'s built-in authentication
- **Testing framework**: Pest

### 1.3 Navigate to Project Directory
```bash
cd hospital-employee-management
```

### 1.4 Verify Installation
Check that all components are properly installed:

```bash
# Check Laravel version
php artisan --version

# Check Node.js dependencies
npm list --depth=0
```

You should see Laravel 12.x and the React starter kit dependencies.

---

## Step 2: Environment Configuration

### 2.1 Database Configuration
Copy the environment file and configure your database:

```bash
cp .env.example .env
```

Edit the `.env` file with your database credentials:

```env
APP_NAME="Hospital Employee Management"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hospital_employee_management
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 2.2 Generate Application Key
```bash
php artisan key:generate
```

### 2.3 Indonesian Localization Setup
Configure timezone and locale for Indonesian context:

```env
# Add to .env file
APP_TIMEZONE=Asia/Jakarta
APP_LOCALE=id
APP_FALLBACK_LOCALE=en
```

### 2.4 Create Database
Create the database in your MySQL/PostgreSQL server:

```sql
CREATE DATABASE hospital_employee_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2.5 Test Database Connection
```bash
php artisan migrate:status
```

If successful, you should see a message about no migrations found.

---

## Step 3: Project Structure Overview

### 3.1 Backend Structure (Laravel)
```
app/
├── Http/
│   ├── Controllers/     # API and web controllers
│   ├── Middleware/      # Custom middleware
│   └── Requests/        # Form request validation
├── Models/              # Eloquent models
├── Providers/           # Service providers
└── Services/            # Business logic services

database/
├── migrations/          # Database migrations
├── seeders/            # Database seeders
└── factories/          # Model factories

routes/
├── web.php             # Web routes (Inertia)
├── api.php             # API routes
└── console.php         # Artisan commands
```

### 3.2 Frontend Structure (React)
```
resources/js/
├── components/         # Reusable React components
│   ├── ui/            # shadcn/ui components
│   └── forms/         # Form components
├── hooks/             # Custom React hooks
├── layouts/           # Page layouts
├── lib/               # Utility functions
├── pages/             # Page components
└── types/             # TypeScript definitions

resources/views/
└── app.blade.php      # Main application template
```

### 3.3 Key Configuration Files
- `vite.config.js` - Frontend build configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `composer.json` - PHP dependencies
- `package.json` - Node.js dependencies

---

## Step 4: Development Tools Setup

### 4.1 Install Laravel Telescope (Development)
Laravel Telescope provides debugging and monitoring capabilities:

```bash
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

### 4.2 Install Laravel Pint (Code Formatting)
Laravel Pint ensures consistent code formatting:

```bash
composer require laravel/pint --dev
```

Create `pint.json` configuration:

```json
{
    "preset": "laravel",
    "rules": {
        "simplified_null_return": true,
        "braces": {
            "position_after_control_structures": "same"
        }
    }
}
```

### 4.3 Configure React DevTools
The React starter kit comes with React DevTools support. Install the browser extension:
- [React Developer Tools for Chrome](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)
- [React Developer Tools for Firefox](https://addons.mozilla.org/en-US/firefox/addon/react-devtools/)

### 4.4 Database Management Tools
Install Laravel Tinker for database interaction:

```bash
# Tinker is included by default, test it:
php artisan tinker
```

---

## Step 5: Indonesian Localization Setup

### 5.1 Install Laravel Lang Package
```bash
composer require laravel-lang/common --dev
php artisan lang:add id
```

### 5.2 Configure Localization
Update `config/app.php`:

```php
'locale' => env('APP_LOCALE', 'id'),
'fallback_locale' => env('APP_FALLBACK_LOCALE', 'en'),
'faker_locale' => 'id_ID',
```

### 5.3 Create Indonesian Language Files
Create `resources/lang/id/hospital.php`:

```php
<?php

return [
    'employee' => 'Karyawan',
    'employees' => 'Karyawan',
    'department' => 'Departemen',
    'departments' => 'Departemen',
    'doctor' => 'Dokter',
    'nurse' => 'Perawat',
    'midwife' => 'Bidan',
    'admin_staff' => 'Tenaga Administrasi',
    'shift' => 'Shift',
    'morning_shift' => 'Shift Pagi',
    'afternoon_shift' => 'Shift Siang',
    'night_shift' => 'Shift Malam',
    'on_call' => 'Jaga',
];
```

---

## Step 6: Frontend Dependencies and Configuration

### 6.1 Install Additional Dependencies
```bash
npm install @tanstack/react-query date-fns react-hook-form @hookform/resolvers zod
```

### 6.2 Configure Tailwind for Indonesian Context
Update `tailwind.config.js`:

```javascript
import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.tsx',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                hospital: {
                    primary: '#0ea5e9',
                    secondary: '#64748b',
                    accent: '#f59e0b',
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444',
                },
            },
        },
    },

    plugins: [forms],
};
```

---

## Step 7: Initial Setup Verification

### 7.1 Install Frontend Dependencies
```bash
npm install
```

### 7.2 Build Frontend Assets
```bash
npm run build
```

### 7.3 Start Development Servers
Open two terminal windows:

**Terminal 1 - Laravel Development Server:**
```bash
php artisan serve
```

**Terminal 2 - Vite Development Server:**
```bash
npm run dev
```

### 7.4 Verify Installation
1. Open your browser and navigate to `http://localhost:8000`
2. You should see the Laravel welcome page with React components
3. Check that hot module replacement works by making a small change to a React component

---

## Step 8: Basic Configuration Testing

### 8.1 Test Database Connection
```bash
php artisan migrate:status
```

### 8.2 Test Localization
Create a test route in `routes/web.php`:

```php
Route::get('/test-locale', function () {
    return response()->json([
        'locale' => app()->getLocale(),
        'timezone' => config('app.timezone'),
        'hospital_terms' => __('hospital'),
    ]);
});
```

Visit `http://localhost:8000/test-locale` to verify localization works.

### 8.3 Test React Components
The starter kit includes sample components. Verify they're working by checking the browser console for any errors.

---

## Troubleshooting Common Issues

### Issue 1: Database Connection Failed
**Solution:**
- Verify database credentials in `.env`
- Ensure database server is running
- Check if database exists

### Issue 2: NPM Install Fails
**Solution:**
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` and `package-lock.json`
- Run `npm install` again

### Issue 3: Vite Build Errors
**Solution:**
- Check Node.js version (requires 18+)
- Verify all dependencies are installed
- Clear Vite cache: `npm run dev -- --force`

### Issue 4: Permission Errors
**Solution:**
- Set proper permissions: `chmod -R 755 storage bootstrap/cache`
- Ensure web server has write access to storage directories

---

## Chapter Summary

In this chapter, you've successfully:

✅ Installed Laravel 12 with React starter kit  
✅ Configured development environment for Indonesian healthcare context  
✅ Set up database and localization  
✅ Installed essential development tools  
✅ Verified the complete setup  

### What's Next?
In Chapter 2, we'll design and implement the comprehensive database schema for our hospital employee management system, including all the entities and relationships needed for Indonesian healthcare institutions.

### Files Created in This Chapter
- `.env` - Environment configuration
- `pint.json` - Code formatting rules
- `resources/lang/id/hospital.php` - Indonesian translations
- Updated `tailwind.config.js` - Custom styling configuration

### Key Commands to Remember
```bash
# Start development servers
php artisan serve
npm run dev

# Run code formatting
./vendor/bin/pint

# Access database
php artisan tinker

# View debugging information
# Visit: http://localhost:8000/telescope
```

---

## Additional Resources

- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [React Starter Kit Guide](https://laravel.com/docs/12.x/starter-kits#react)
- [Inertia.js Documentation](https://inertiajs.com/)
- [shadcn/ui Components](https://ui.shadcn.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

Ready to move on to Chapter 2? Let's build the database foundation for our hospital employee management system!
