# Chapter 06 - Appointment Scheduling System

## Tujuan Chapter
Pada chapter ini, kita akan:
- Membuat Eloquent model untuk Appointment
- Implementasi appointment booking system
- Setup calendar view untuk appointments
- Membuat appointment status management
- Implementasi appointment validation dan conflict checking

## Langkah 1: Create Appointment Model

### 1.1 Generate Appointment Model
```bash
php artisan make:model Hospital/Appointment
```

Edit `app/Models/Hospital/Appointment.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'appointment_number',
        'patient_id',
        'doctor_id',
        'department_id',
        'appointment_date',
        'appointment_type',
        'status',
        'reason_for_visit',
        'notes',
        'fee',
        'payment_status',
        'checked_in_at',
        'checked_out_at',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'appointment_date' => 'datetime',
            'checked_in_at' => 'datetime',
            'checked_out_at' => 'datetime',
            'fee' => 'decimal:2',
        ];
    }

    // Generate appointment number otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($appointment) {
            if (empty($appointment->appointment_number)) {
                $appointment->appointment_number = self::generateAppointmentNumber();
            }
        });
    }

    private static function generateAppointmentNumber(): string
    {
        $prefix = 'APT';
        $date = date('Ymd');
        
        $lastAppointment = self::where('appointment_number', 'like', $prefix . $date . '%')
                              ->orderBy('appointment_number', 'desc')
                              ->first();

        if ($lastAppointment) {
            $lastNumber = (int) substr($lastAppointment->appointment_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function medicalRecord()
    {
        return $this->hasOne(MedicalRecord::class);
    }

    // Accessors
    public function getAppointmentTimeAttribute(): string
    {
        return $this->appointment_date->format('H:i');
    }

    public function getAppointmentDateFormattedAttribute(): string
    {
        return $this->appointment_date->format('d/m/Y');
    }

    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'scheduled' => 'Terjadwal',
            'confirmed' => 'Dikonfirmasi',
            'in_progress' => 'Sedang Berlangsung',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
            'no_show' => 'Tidak Hadir',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getAppointmentTypeTextAttribute(): string
    {
        $types = [
            'consultation' => 'Konsultasi',
            'follow_up' => 'Kontrol',
            'emergency' => 'Darurat',
            'surgery' => 'Operasi',
        ];

        return $types[$this->appointment_type] ?? $this->appointment_type;
    }

    public function getPaymentStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => 'Menunggu Pembayaran',
            'paid' => 'Sudah Dibayar',
            'cancelled' => 'Dibatalkan',
        ];

        return $statuses[$this->payment_status] ?? $this->payment_status;
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('appointment_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('appointment_date', '>=', now());
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('appointment_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function canBeCheckedIn(): bool
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && 
               $this->appointment_date->isToday() &&
               !$this->checked_in_at;
    }

    public function canBeCheckedOut(): bool
    {
        return $this->status === 'in_progress' && 
               $this->checked_in_at && 
               !$this->checked_out_at;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && 
               $this->appointment_date->isFuture();
    }

    public function getDurationAttribute(): ?int
    {
        if ($this->checked_in_at && $this->checked_out_at) {
            return $this->checked_in_at->diffInMinutes($this->checked_out_at);
        }
        return null;
    }

    // Static methods untuk conflict checking
    public static function hasConflict($doctorId, $appointmentDate, $excludeId = null): bool
    {
        $query = self::where('doctor_id', $doctorId)
                    ->where('appointment_date', $appointmentDate)
                    ->whereNotIn('status', ['cancelled', 'no_show']);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    public static function getAvailableSlots($doctorId, $date): array
    {
        $doctor = Doctor::find($doctorId);
        if (!$doctor) return [];

        $dayName = Carbon::parse($date)->format('l');
        if (!$doctor->isAvailableOnDay($dayName)) {
            return [];
        }

        $startTime = Carbon::parse($date . ' ' . $doctor->available_from->format('H:i'));
        $endTime = Carbon::parse($date . ' ' . $doctor->available_to->format('H:i'));
        
        $slots = [];
        $slotDuration = 30; // 30 minutes per slot

        while ($startTime->lt($endTime)) {
            $slotTime = $startTime->copy();
            
            // Check if slot is available
            if (!self::hasConflict($doctorId, $slotTime)) {
                $slots[] = [
                    'time' => $slotTime->format('H:i'),
                    'datetime' => $slotTime->toDateTimeString(),
                    'available' => true,
                ];
            }

            $startTime->addMinutes($slotDuration);
        }

        return $slots;
    }
}
```

## Langkah 2: Create Appointment Controller

### 2.1 Generate Controller
```bash
php artisan make:controller Hospital/AppointmentController --resource
```

Edit `app/Http/Controllers/Hospital/AppointmentController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Department;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    public function index(Request $request)
    {
        $query = Appointment::with(['patient', 'doctor.user', 'department']);

        // Filter by date range
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->byDateRange($request->date_from, $request->date_to);
        } elseif ($request->filled('date')) {
            $query->whereDate('appointment_date', $request->date);
        } else {
            // Default to today's appointments
            $query->today();
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // Filter by doctor
        if ($request->filled('doctor')) {
            $query->byDoctor($request->doctor);
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->where('department_id', $request->department);
        }

        $appointments = $query->orderBy('appointment_date', 'asc')
                             ->paginate(20)
                             ->withQueryString();

        $doctors = Doctor::with('user')->active()->get();
        $departments = Department::active()->get();

        return Inertia::render('Hospital/Appointments/Index', [
            'appointments' => $appointments,
            'doctors' => $doctors,
            'departments' => $departments,
            'filters' => $request->only(['date', 'date_from', 'date_to', 'status', 'doctor', 'department']),
        ]);
    }

    public function create(Request $request)
    {
        $patients = Patient::active()->get();
        $doctors = Doctor::with(['user', 'department'])->available()->get();
        $departments = Department::active()->get();

        // If patient_id is provided, pre-select the patient
        $selectedPatient = null;
        if ($request->filled('patient_id')) {
            $selectedPatient = Patient::find($request->patient_id);
        }

        return Inertia::render('Hospital/Appointments/Create', [
            'patients' => $patients,
            'doctors' => $doctors,
            'departments' => $departments,
            'selectedPatient' => $selectedPatient,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'department_id' => 'required|exists:departments,id',
            'appointment_date' => 'required|date|after:now',
            'appointment_type' => 'required|in:consultation,follow_up,emergency,surgery',
            'reason_for_visit' => 'required|string',
            'notes' => 'nullable|string',
        ]);

        // Check for conflicts
        if (Appointment::hasConflict($validated['doctor_id'], $validated['appointment_date'])) {
            return back()->withErrors([
                'appointment_date' => 'Waktu appointment sudah terboking. Silakan pilih waktu lain.'
            ]);
        }

        // Get doctor's consultation fee
        $doctor = Doctor::find($validated['doctor_id']);
        $validated['fee'] = $doctor->consultation_fee;
        $validated['status'] = 'scheduled';
        $validated['payment_status'] = 'pending';
        $validated['created_by'] = auth()->id();

        $appointment = Appointment::create($validated);

        return redirect()->route('appointments.show', $appointment)
                        ->with('success', 'Appointment berhasil dibuat.');
    }

    public function show(Appointment $appointment)
    {
        $appointment->load(['patient', 'doctor.user', 'department', 'createdBy', 'medicalRecord']);

        return Inertia::render('Hospital/Appointments/Show', [
            'appointment' => $appointment,
        ]);
    }

    public function edit(Appointment $appointment)
    {
        if (!in_array($appointment->status, ['scheduled', 'confirmed'])) {
            return back()->with('error', 'Appointment ini tidak dapat diedit.');
        }

        $patients = Patient::active()->get();
        $doctors = Doctor::with(['user', 'department'])->available()->get();
        $departments = Department::active()->get();

        $appointment->load(['patient', 'doctor', 'department']);

        return Inertia::render('Hospital/Appointments/Edit', [
            'appointment' => $appointment,
            'patients' => $patients,
            'doctors' => $doctors,
            'departments' => $departments,
        ]);
    }

    public function update(Request $request, Appointment $appointment)
    {
        if (!in_array($appointment->status, ['scheduled', 'confirmed'])) {
            return back()->with('error', 'Appointment ini tidak dapat diedit.');
        }

        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'department_id' => 'required|exists:departments,id',
            'appointment_date' => 'required|date|after:now',
            'appointment_type' => 'required|in:consultation,follow_up,emergency,surgery',
            'reason_for_visit' => 'required|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:scheduled,confirmed,cancelled',
        ]);

        // Check for conflicts (excluding current appointment)
        if (Appointment::hasConflict($validated['doctor_id'], $validated['appointment_date'], $appointment->id)) {
            return back()->withErrors([
                'appointment_date' => 'Waktu appointment sudah terboking. Silakan pilih waktu lain.'
            ]);
        }

        $appointment->update($validated);

        return redirect()->route('appointments.show', $appointment)
                        ->with('success', 'Appointment berhasil diperbarui.');
    }

    public function destroy(Appointment $appointment)
    {
        if (!$appointment->canBeCancelled()) {
            return back()->with('error', 'Appointment ini tidak dapat dibatalkan.');
        }

        $appointment->update(['status' => 'cancelled']);

        return redirect()->route('appointments.index')
                        ->with('success', 'Appointment berhasil dibatalkan.');
    }

    // Additional methods for appointment management
    public function checkIn(Appointment $appointment)
    {
        if (!$appointment->canBeCheckedIn()) {
            return back()->with('error', 'Appointment tidak dapat di-check in.');
        }

        $appointment->update([
            'status' => 'in_progress',
            'checked_in_at' => now(),
        ]);

        return back()->with('success', 'Pasien berhasil di-check in.');
    }

    public function checkOut(Appointment $appointment)
    {
        if (!$appointment->canBeCheckedOut()) {
            return back()->with('error', 'Appointment tidak dapat di-check out.');
        }

        $appointment->update([
            'status' => 'completed',
            'checked_out_at' => now(),
        ]);

        return back()->with('success', 'Pasien berhasil di-check out.');
    }

    public function getAvailableSlots(Request $request)
    {
        $request->validate([
            'doctor_id' => 'required|exists:doctors,id',
            'date' => 'required|date|after_or_equal:today',
        ]);

        $slots = Appointment::getAvailableSlots($request->doctor_id, $request->date);

        return response()->json(['slots' => $slots]);
    }

    public function calendar(Request $request)
    {
        $startDate = $request->get('start', now()->startOfMonth());
        $endDate = $request->get('end', now()->endOfMonth());

        $appointments = Appointment::with(['patient', 'doctor.user'])
                                  ->byDateRange($startDate, $endDate)
                                  ->get()
                                  ->map(function ($appointment) {
                                      return [
                                          'id' => $appointment->id,
                                          'title' => $appointment->patient->full_name . ' - ' . $appointment->doctor->user->full_name,
                                          'start' => $appointment->appointment_date->toISOString(),
                                          'backgroundColor' => $this->getStatusColor($appointment->status),
                                          'borderColor' => $this->getStatusColor($appointment->status),
                                          'extendedProps' => [
                                              'appointment_number' => $appointment->appointment_number,
                                              'patient_name' => $appointment->patient->full_name,
                                              'doctor_name' => $appointment->doctor->user->full_name,
                                              'status' => $appointment->status_text,
                                              'type' => $appointment->appointment_type_text,
                                          ],
                                      ];
                                  });

        return Inertia::render('Hospital/Appointments/Calendar', [
            'appointments' => $appointments,
        ]);
    }

    private function getStatusColor($status): string
    {
        $colors = [
            'scheduled' => '#3B82F6',
            'confirmed' => '#10B981',
            'in_progress' => '#F59E0B',
            'completed' => '#6B7280',
            'cancelled' => '#EF4444',
            'no_show' => '#8B5CF6',
        ];

        return $colors[$status] ?? '#6B7280';
    }
}
```

## Langkah 3: Add Appointment Routes

### 3.1 Update Routes
Edit `routes/web.php` dan tambahkan:
```php
// Appointment management routes
Route::middleware(['auth', 'role:admin,receptionist,nurse,doctor'])->group(function () {
    Route::resource('appointments', AppointmentController::class);
    Route::get('/appointments-calendar', [AppointmentController::class, 'calendar'])->name('appointments.calendar');
    Route::get('/available-slots', [AppointmentController::class, 'getAvailableSlots'])->name('appointments.available-slots');
    
    // Appointment actions
    Route::post('/appointments/{appointment}/check-in', [AppointmentController::class, 'checkIn'])->name('appointments.check-in');
    Route::post('/appointments/{appointment}/check-out', [AppointmentController::class, 'checkOut'])->name('appointments.check-out');
});
```

## Langkah 4: Create Appointment Seeder

### 4.1 Create Seeder
```bash
php artisan make:seeder AppointmentSeeder
```

Edit `database/seeders/AppointmentSeeder.php`:
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Department;
use App\Models\User;
use Carbon\Carbon;

class AppointmentSeeder extends Seeder
{
    public function run(): void
    {
        // Create sample patients first
        $patients = Patient::factory(10)->create();
        
        // Create sample doctors
        $doctorUsers = User::factory(3)->create(['role' => 'doctor']);
        $department = Department::first();
        
        $doctors = [];
        foreach ($doctorUsers as $user) {
            $doctors[] = Doctor::create([
                'user_id' => $user->id,
                'department_id' => $department->id,
                'license_number' => 'LIC' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                'specialization' => 'Dokter Umum',
                'qualification' => 'S1 Kedokteran',
                'experience_years' => rand(1, 20),
                'phone' => '081234567' . rand(100, 999),
                'consultation_fee' => rand(100000, 500000),
                'available_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'available_from' => '08:00',
                'available_to' => '16:00',
            ]);
        }

        // Create sample appointments
        $admin = User::where('role', 'admin')->first();
        
        foreach (range(1, 20) as $i) {
            $appointmentDate = Carbon::today()->addDays(rand(-7, 14))->setHour(rand(8, 15))->setMinute(rand(0, 1) * 30);
            
            Appointment::create([
                'patient_id' => $patients->random()->id,
                'doctor_id' => collect($doctors)->random()->id,
                'department_id' => $department->id,
                'appointment_date' => $appointmentDate,
                'appointment_type' => collect(['consultation', 'follow_up', 'emergency'])->random(),
                'status' => collect(['scheduled', 'confirmed', 'completed', 'cancelled'])->random(),
                'reason_for_visit' => 'Konsultasi kesehatan rutin',
                'fee' => rand(100000, 300000),
                'payment_status' => collect(['pending', 'paid'])->random(),
                'created_by' => $admin->id,
            ]);
        }
    }
}
```

## Langkah 5: Create React Components

### 5.1 Create Appointment Index Component
Create `resources/js/Pages/Hospital/Appointments/Index.jsx`:
```jsx
import { useState } from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, Link, router } from '@inertiajs/react';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import SelectInput from '@/Components/SelectInput';

export default function AppointmentIndex({ auth, appointments, doctors, departments, filters }) {
    const [search, setSearch] = useState(filters.search || '');
    const [date, setDate] = useState(filters.date || '');
    const [status, setStatus] = useState(filters.status || '');
    const [doctor, setDoctor] = useState(filters.doctor || '');

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(route('appointments.index'), {
            search,
            date,
            status,
            doctor,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const getStatusColor = (status) => {
        const colors = {
            'scheduled': 'bg-blue-100 text-blue-800',
            'confirmed': 'bg-green-100 text-green-800',
            'in_progress': 'bg-yellow-100 text-yellow-800',
            'completed': 'bg-gray-100 text-gray-800',
            'cancelled': 'bg-red-100 text-red-800',
            'no_show': 'bg-purple-100 text-purple-800',
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const handleCheckIn = (appointmentId) => {
        router.post(route('appointments.check-in', appointmentId), {}, {
            preserveScroll: true,
        });
    };

    const handleCheckOut = (appointmentId) => {
        router.post(route('appointments.check-out', appointmentId), {}, {
            preserveScroll: true,
        });
    };

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Manajemen Appointment
                    </h2>
                    <div className="flex gap-2">
                        <Link href={route('appointments.calendar')}>
                            <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                                Calendar View
                            </button>
                        </Link>
                        <Link href={route('appointments.create')}>
                            <PrimaryButton>Buat Appointment</PrimaryButton>
                        </Link>
                    </div>
                </div>
            }
        >
            <Head title="Manajemen Appointment" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Search and Filter */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6">
                            <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <TextInput
                                        placeholder="Cari appointment..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <TextInput
                                        type="date"
                                        value={date}
                                        onChange={(e) => setDate(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <SelectInput
                                        value={status}
                                        onChange={(e) => setStatus(e.target.value)}
                                        className="w-full"
                                    >
                                        <option value="">Semua Status</option>
                                        <option value="scheduled">Terjadwal</option>
                                        <option value="confirmed">Dikonfirmasi</option>
                                        <option value="in_progress">Sedang Berlangsung</option>
                                        <option value="completed">Selesai</option>
                                        <option value="cancelled">Dibatalkan</option>
                                    </SelectInput>
                                </div>
                                <div>
                                    <SelectInput
                                        value={doctor}
                                        onChange={(e) => setDoctor(e.target.value)}
                                        className="w-full"
                                    >
                                        <option value="">Semua Dokter</option>
                                        {doctors.map((doc) => (
                                            <option key={doc.id} value={doc.id}>
                                                {doc.user.full_name}
                                            </option>
                                        ))}
                                    </SelectInput>
                                </div>
                                <div>
                                    <PrimaryButton type="submit" className="w-full">
                                        Cari
                                    </PrimaryButton>
                                </div>
                            </form>
                        </div>
                    </div>

                    {/* Appointments Table */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                No. Appointment
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Pasien
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Dokter
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Tanggal & Waktu
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Aksi
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {appointments.data.map((appointment) => (
                                            <tr key={appointment.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {appointment.appointment_number}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {appointment.patient.full_name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {appointment.patient.patient_id}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {appointment.doctor.user.full_name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {appointment.department.name}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {appointment.appointment_date_formatted}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {appointment.appointment_time}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(appointment.status)}`}>
                                                        {appointment.status_text}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="flex gap-2">
                                                        <Link
                                                            href={route('appointments.show', appointment.id)}
                                                            className="text-blue-600 hover:text-blue-900"
                                                        >
                                                            Lihat
                                                        </Link>
                                                        {appointment.can_be_checked_in && (
                                                            <button
                                                                onClick={() => handleCheckIn(appointment.id)}
                                                                className="text-green-600 hover:text-green-900"
                                                            >
                                                                Check In
                                                            </button>
                                                        )}
                                                        {appointment.can_be_checked_out && (
                                                            <button
                                                                onClick={() => handleCheckOut(appointment.id)}
                                                                className="text-purple-600 hover:text-purple-900"
                                                            >
                                                                Check Out
                                                            </button>
                                                        )}
                                                        {appointment.status === 'completed' && (
                                                            <Link
                                                                href={route('medical-records.create', { appointment_id: appointment.id })}
                                                                className="text-indigo-600 hover:text-indigo-900"
                                                            >
                                                                Rekam Medis
                                                            </Link>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* Pagination */}
                            {appointments.links && (
                                <div className="mt-6">
                                    {/* Pagination component would go here */}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
```

### 5.2 Create Appointment Calendar Component
Create `resources/js/Pages/Hospital/Appointments/Calendar.jsx`:
```jsx
import { useState, useEffect } from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, Link } from '@inertiajs/react';
import PrimaryButton from '@/Components/PrimaryButton';

export default function AppointmentCalendar({ auth, appointments }) {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [calendarEvents, setCalendarEvents] = useState([]);

    useEffect(() => {
        // Transform appointments data for calendar display
        const events = appointments.map(appointment => ({
            id: appointment.id,
            title: `${appointment.patient_name} - ${appointment.doctor_name}`,
            start: new Date(appointment.start),
            end: new Date(appointment.start),
            backgroundColor: appointment.backgroundColor,
            extendedProps: appointment.extendedProps,
        }));
        setCalendarEvents(events);
    }, [appointments]);

    const getDaysInMonth = (date) => {
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            days.push(null);
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            days.push(new Date(year, month, day));
        }

        return days;
    };

    const getEventsForDate = (date) => {
        if (!date) return [];
        return calendarEvents.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate.toDateString() === date.toDateString();
        });
    };

    const navigateMonth = (direction) => {
        const newDate = new Date(currentDate);
        newDate.setMonth(currentDate.getMonth() + direction);
        setCurrentDate(newDate);
    };

    const monthNames = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];

    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Calendar Appointment
                    </h2>
                    <div className="flex gap-2">
                        <Link href={route('appointments.index')}>
                            <button className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                                List View
                            </button>
                        </Link>
                        <Link href={route('appointments.create')}>
                            <PrimaryButton>Buat Appointment</PrimaryButton>
                        </Link>
                    </div>
                </div>
            }
        >
            <Head title="Calendar Appointment" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            {/* Calendar Header */}
                            <div className="flex justify-between items-center mb-6">
                                <button
                                    onClick={() => navigateMonth(-1)}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                >
                                    ← Bulan Sebelumnya
                                </button>
                                <h3 className="text-xl font-semibold">
                                    {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                                </h3>
                                <button
                                    onClick={() => navigateMonth(1)}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                >
                                    Bulan Berikutnya →
                                </button>
                            </div>

                            {/* Calendar Grid */}
                            <div className="grid grid-cols-7 gap-1">
                                {/* Day Headers */}
                                {dayNames.map(day => (
                                    <div key={day} className="p-2 text-center font-semibold bg-gray-100">
                                        {day}
                                    </div>
                                ))}

                                {/* Calendar Days */}
                                {getDaysInMonth(currentDate).map((date, index) => {
                                    const dayEvents = getEventsForDate(date);
                                    const isToday = date && date.toDateString() === new Date().toDateString();

                                    return (
                                        <div
                                            key={index}
                                            className={`min-h-[100px] p-1 border border-gray-200 ${
                                                date ? 'bg-white' : 'bg-gray-50'
                                            } ${isToday ? 'bg-blue-50 border-blue-300' : ''}`}
                                        >
                                            {date && (
                                                <>
                                                    <div className={`text-sm font-medium mb-1 ${
                                                        isToday ? 'text-blue-600' : 'text-gray-900'
                                                    }`}>
                                                        {date.getDate()}
                                                    </div>
                                                    <div className="space-y-1">
                                                        {dayEvents.slice(0, 3).map(event => (
                                                            <div
                                                                key={event.id}
                                                                className="text-xs p-1 rounded text-white truncate"
                                                                style={{ backgroundColor: event.backgroundColor }}
                                                                title={event.title}
                                                            >
                                                                {event.extendedProps.appointment_number}
                                                            </div>
                                                        ))}
                                                        {dayEvents.length > 3 && (
                                                            <div className="text-xs text-gray-500">
                                                                +{dayEvents.length - 3} lainnya
                                                            </div>
                                                        )}
                                                    </div>
                                                </>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>

                            {/* Legend */}
                            <div className="mt-6 flex flex-wrap gap-4">
                                <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 bg-blue-500 rounded"></div>
                                    <span className="text-sm">Terjadwal</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 bg-green-500 rounded"></div>
                                    <span className="text-sm">Dikonfirmasi</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                                    <span className="text-sm">Sedang Berlangsung</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 bg-gray-500 rounded"></div>
                                    <span className="text-sm">Selesai</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 bg-red-500 rounded"></div>
                                    <span className="text-sm">Dibatalkan</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi medical records system
- Membuat patient medical history tracking
- Setup prescription management
- Implementasi vital signs recording

## Checklist Completion

- [x] Appointment model dengan auto-numbering
- [x] Appointment controller dengan CRUD operations
- [x] Conflict checking untuk appointment scheduling
- [x] Available slots API endpoint
- [x] Check-in/check-out functionality
- [x] Calendar view untuk appointments
- [x] Appointment status management
- [x] Sample data seeder
- [x] React components untuk appointment management
- [x] Calendar view React component

**Estimasi Waktu**: 150-180 menit

**Difficulty Level**: Advanced

**File yang Dibuat**:
- Appointment model
- AppointmentController
- Appointment routes
- AppointmentSeeder
- Appointment Index React component
- Appointment Calendar React component
