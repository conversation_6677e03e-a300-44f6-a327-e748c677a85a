# Chapter 8: Prescription Management

## Building Digital Prescription System

In this chapter, we'll create a comprehensive prescription management system that handles medication prescribing, drug interaction checks, prescription history tracking, and pharmacy integration.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create digital prescription management system
- Build medication database with drug interactions
- Implement prescription workflow and approvals
- Design pharmacy integration features
- Set up prescription history tracking
- Create drug interaction checking system

## 💊 Prescription System Requirements

### Core Features

1. **Digital Prescriptions**: Electronic prescription creation and management
2. **Medication Database**: Comprehensive drug information and interactions
3. **Drug Interaction Checks**: Automated safety checks for drug combinations
4. **Prescription History**: Patient medication history tracking
5. **Pharmacy Integration**: Electronic prescription transmission
6. **Dosage Management**: Accurate dosing and administration instructions

## 🛠 Backend Implementation

### Step 1: Prescription Tables Migration

```bash
php artisan make:migration create_medications_table
php artisan make:migration create_prescriptions_table
php artisan make:migration create_prescription_items_table
php artisan make:migration create_drug_interactions_table
```

**database/migrations/xxxx_create_medications_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('medications', function (Blueprint $table) {
            $table->id();
            
            // Basic medication information
            $table->string('name');
            $table->string('generic_name')->nullable();
            $table->string('brand_name')->nullable();
            $table->string('drug_code')->unique(); // National drug code
            
            // Classification
            $table->string('drug_class')->nullable();
            $table->string('therapeutic_class')->nullable();
            $table->json('indications')->nullable(); // Medical conditions treated
            
            // Formulation details
            $table->string('dosage_form'); // tablet, capsule, syrup, etc.
            $table->string('strength'); // mg, ml, etc.
            $table->string('route_of_administration'); // oral, injection, topical
            
            // Prescribing information
            $table->text('dosing_instructions')->nullable();
            $table->json('contraindications')->nullable();
            $table->json('side_effects')->nullable();
            $table->text('warnings')->nullable();
            
            // Regulatory and availability
            $table->boolean('requires_prescription')->default(true);
            $table->boolean('is_controlled_substance')->default(false);
            $table->string('controlled_substance_schedule')->nullable();
            $table->boolean('is_available')->default(true);
            
            // Pricing (optional)
            $table->decimal('unit_price', 10, 2)->nullable();
            $table->string('unit_of_measure')->nullable();
            
            $table->timestamps();
            
            $table->index(['name', 'is_available']);
            $table->index('drug_class');
            $table->index('generic_name');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('medications');
    }
};
```

**database/migrations/xxxx_create_prescriptions_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('medical_record_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            
            // Prescription details
            $table->string('prescription_number')->unique();
            $table->date('prescribed_date');
            $table->date('valid_until')->nullable(); // Prescription expiry
            
            // Status tracking
            $table->enum('status', ['draft', 'prescribed', 'dispensed', 'completed', 'cancelled'])->default('draft');
            $table->timestamp('prescribed_at')->nullable();
            $table->timestamp('dispensed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            
            // Pharmacy information
            $table->string('pharmacy_name')->nullable();
            $table->string('pharmacy_contact')->nullable();
            $table->text('pharmacy_notes')->nullable();
            
            // Additional information
            $table->text('clinical_notes')->nullable();
            $table->text('patient_instructions')->nullable();
            $table->boolean('allow_generic_substitution')->default(true);
            $table->integer('refills_allowed')->default(0);
            $table->integer('refills_remaining')->default(0);
            
            // Digital signature and verification
            $table->string('digital_signature')->nullable();
            $table->timestamp('signed_at')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['patient_id', 'prescribed_date']);
            $table->index(['doctor_id', 'prescribed_date']);
            $table->index(['clinic_id', 'status']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('prescriptions');
    }
};
```

**database/migrations/xxxx_create_prescription_items_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('prescription_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prescription_id')->constrained()->onDelete('cascade');
            $table->foreignId('medication_id')->constrained()->onDelete('cascade');
            
            // Dosing information
            $table->string('dosage'); // e.g., "500mg"
            $table->string('frequency'); // e.g., "twice daily", "every 8 hours"
            $table->string('route'); // oral, topical, injection
            $table->integer('quantity_prescribed');
            $table->string('unit'); // tablets, ml, etc.
            $table->integer('days_supply');
            
            // Administration instructions
            $table->text('administration_instructions'); // "Take with food", "Before meals"
            $table->text('special_instructions')->nullable();
            
            // Dispensing information
            $table->integer('quantity_dispensed')->default(0);
            $table->date('last_dispensed_date')->nullable();
            
            // Status
            $table->enum('status', ['pending', 'dispensed', 'completed', 'discontinued'])->default('pending');
            $table->text('discontinuation_reason')->nullable();
            $table->date('discontinued_date')->nullable();
            
            $table->timestamps();
            
            $table->index(['prescription_id', 'status']);
            $table->index('medication_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('prescription_items');
    }
};
```

### Step 2: Prescription Models

**app/Models/Medication.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Medication extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'generic_name', 'brand_name', 'drug_code', 'drug_class',
        'therapeutic_class', 'indications', 'dosage_form', 'strength',
        'route_of_administration', 'dosing_instructions', 'contraindications',
        'side_effects', 'warnings', 'requires_prescription', 'is_controlled_substance',
        'controlled_substance_schedule', 'is_available', 'unit_price', 'unit_of_measure'
    ];

    protected function casts(): array
    {
        return [
            'indications' => 'array',
            'contraindications' => 'array',
            'side_effects' => 'array',
            'requires_prescription' => 'boolean',
            'is_controlled_substance' => 'boolean',
            'is_available' => 'boolean',
            'unit_price' => 'decimal:2',
        ];
    }

    // Relationships
    public function prescriptionItems()
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    public function drugInteractions()
    {
        return $this->belongsToMany(Medication::class, 'drug_interactions', 'medication_id', 'interacting_medication_id')
            ->withPivot('interaction_type', 'severity', 'description');
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeByClass($query, $drugClass)
    {
        return $query->where('drug_class', $drugClass);
    }

    public function scopeRequiresPrescription($query)
    {
        return $query->where('requires_prescription', true);
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->brand_name ?: $this->generic_name ?: $this->name;
    }

    public function isControlledSubstance()
    {
        return $this->is_controlled_substance;
    }

    public function hasInteractionWith(Medication $medication)
    {
        return $this->drugInteractions()->where('interacting_medication_id', $medication->id)->exists();
    }
}
```

**app/Models/Prescription.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id', 'doctor_id', 'medical_record_id', 'clinic_id',
        'prescription_number', 'prescribed_date', 'valid_until', 'status',
        'pharmacy_name', 'pharmacy_contact', 'pharmacy_notes',
        'clinical_notes', 'patient_instructions', 'allow_generic_substitution',
        'refills_allowed', 'refills_remaining', 'digital_signature'
    ];

    protected function casts(): array
    {
        return [
            'prescribed_date' => 'date',
            'valid_until' => 'date',
            'prescribed_at' => 'datetime',
            'dispensed_at' => 'datetime',
            'completed_at' => 'datetime',
            'signed_at' => 'datetime',
            'verified_at' => 'datetime',
            'allow_generic_substitution' => 'boolean',
            'is_verified' => 'boolean',
        ];
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function medicalRecord()
    {
        return $this->belongsTo(MedicalRecord::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function items()
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['prescribed', 'dispensed']);
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    // Helper methods
    public function prescribe()
    {
        $this->update([
            'status' => 'prescribed',
            'prescribed_at' => now()
        ]);
    }

    public function dispense()
    {
        $this->update([
            'status' => 'dispensed',
            'dispensed_at' => now()
        ]);
    }

    public function complete()
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }

    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
    }

    public function sign($signature)
    {
        $this->update([
            'digital_signature' => $signature,
            'signed_at' => now()
        ]);
    }

    public function verify()
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now()
        ]);
    }

    public function isExpired()
    {
        return $this->valid_until && $this->valid_until->isPast();
    }

    public function hasRefillsRemaining()
    {
        return $this->refills_remaining > 0;
    }

    // Boot method for auto-generating prescription number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            if (empty($prescription->prescription_number)) {
                $prescription->prescription_number = static::generatePrescriptionNumber();
            }
        });
    }

    public static function generatePrescriptionNumber()
    {
        $date = now()->format('Ymd');
        $lastPrescription = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastPrescription) {
            $lastNumber = substr($lastPrescription->prescription_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return 'RX' . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

**app/Models/PrescriptionItem.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrescriptionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_id', 'medication_id', 'dosage', 'frequency', 'route',
        'quantity_prescribed', 'unit', 'days_supply', 'administration_instructions',
        'special_instructions', 'quantity_dispensed', 'last_dispensed_date',
        'status', 'discontinuation_reason', 'discontinued_date'
    ];

    protected function casts(): array
    {
        return [
            'last_dispensed_date' => 'date',
            'discontinued_date' => 'date',
        ];
    }

    // Relationships
    public function prescription()
    {
        return $this->belongsTo(Prescription::class);
    }

    public function medication()
    {
        return $this->belongsTo(Medication::class);
    }

    // Helper methods
    public function dispense($quantity)
    {
        $this->update([
            'quantity_dispensed' => $this->quantity_dispensed + $quantity,
            'last_dispensed_date' => now(),
            'status' => $this->quantity_dispensed + $quantity >= $this->quantity_prescribed ? 'completed' : 'dispensed'
        ]);
    }

    public function discontinue($reason)
    {
        $this->update([
            'status' => 'discontinued',
            'discontinuation_reason' => $reason,
            'discontinued_date' => now()
        ]);
    }

    public function getRemainingQuantityAttribute()
    {
        return $this->quantity_prescribed - $this->quantity_dispensed;
    }

    public function getFullInstructionsAttribute()
    {
        $instructions = "{$this->dosage} {$this->frequency}";
        if ($this->administration_instructions) {
            $instructions .= " - {$this->administration_instructions}";
        }
        return $instructions;
    }
}
```

### Step 3: Drug Interaction Service

**app/Services/DrugInteractionService.php**:

```php
<?php

namespace App\Services;

use App\Models\Medication;
use App\Models\Patient;
use App\Models\Prescription;
use Illuminate\Support\Collection;

class DrugInteractionService
{
    /**
     * Check for drug interactions in a prescription
     */
    public function checkPrescriptionInteractions(array $medicationIds): array
    {
        $interactions = [];
        $medications = Medication::whereIn('id', $medicationIds)->get();

        foreach ($medications as $medication) {
            foreach ($medications as $otherMedication) {
                if ($medication->id !== $otherMedication->id) {
                    $interaction = $this->checkInteraction($medication, $otherMedication);
                    if ($interaction) {
                        $interactions[] = $interaction;
                    }
                }
            }
        }

        return $this->removeDuplicateInteractions($interactions);
    }

    /**
     * Check interaction between two medications
     */
    public function checkInteraction(Medication $medication1, Medication $medication2): ?array
    {
        $interaction = $medication1->drugInteractions()
            ->where('interacting_medication_id', $medication2->id)
            ->first();

        if ($interaction) {
            return [
                'medication1' => $medication1,
                'medication2' => $medication2,
                'interaction_type' => $interaction->pivot->interaction_type,
                'severity' => $interaction->pivot->severity,
                'description' => $interaction->pivot->description,
            ];
        }

        return null;
    }

    /**
     * Check patient's current medications for interactions with new prescription
     */
    public function checkPatientMedicationInteractions(Patient $patient, array $newMedicationIds): array
    {
        // Get patient's current active prescriptions
        $currentMedications = $this->getPatientCurrentMedications($patient);
        $allMedicationIds = array_merge($currentMedications->pluck('id')->toArray(), $newMedicationIds);

        return $this->checkPrescriptionInteractions($allMedicationIds);
    }

    /**
     * Get patient's current active medications
     */
    public function getPatientCurrentMedications(Patient $patient): Collection
    {
        return Medication::whereHas('prescriptionItems', function ($query) use ($patient) {
            $query->whereHas('prescription', function ($prescriptionQuery) use ($patient) {
                $prescriptionQuery->where('patient_id', $patient->id)
                    ->whereIn('status', ['prescribed', 'dispensed']);
            })->whereIn('status', ['pending', 'dispensed']);
        })->get();
    }

    /**
     * Remove duplicate interactions (A-B is same as B-A)
     */
    private function removeDuplicateInteractions(array $interactions): array
    {
        $unique = [];
        $seen = [];

        foreach ($interactions as $interaction) {
            $key1 = $interaction['medication1']->id . '-' . $interaction['medication2']->id;
            $key2 = $interaction['medication2']->id . '-' . $interaction['medication1']->id;

            if (!in_array($key1, $seen) && !in_array($key2, $seen)) {
                $unique[] = $interaction;
                $seen[] = $key1;
            }
        }

        return $unique;
    }

    /**
     * Get interaction severity level
     */
    public function getInteractionSeverityLevel(array $interactions): string
    {
        $severityLevels = ['minor', 'moderate', 'major', 'contraindicated'];
        $maxSeverity = 'none';

        foreach ($interactions as $interaction) {
            $severity = $interaction['severity'];
            if (array_search($severity, $severityLevels) > array_search($maxSeverity, $severityLevels)) {
                $maxSeverity = $severity;
            }
        }

        return $maxSeverity;
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive prescription management system** with digital prescriptions
2. ✅ **Created medication database** with drug information and classifications
3. ✅ **Implemented drug interaction checking** for patient safety
4. ✅ **Designed prescription workflow** with status tracking
5. ✅ **Added prescription item management** with dosing instructions
6. ✅ **Built drug interaction service** for automated safety checks

### What We Have Now

- Complete digital prescription management system
- Comprehensive medication database with drug interactions
- Automated drug interaction checking for patient safety
- Prescription workflow with status tracking and approvals
- Integration with medical records and patient data

### Next Steps

In **Chapter 9: Billing and Payment System**, we'll:

- Build billing and invoice management system
- Create payment processing and tracking
- Implement insurance claim handling
- Design financial reporting features

---

**Ready to continue?** Proceed to [Chapter 9: Billing and Payment System](./chapter-09-billing.md) to build the billing system.
