# Chapter 6: Appointment Scheduling System

## Building Calendar-Based Appointment Management

In this chapter, we'll create a comprehensive appointment scheduling system with calendar integration, time slot management, conflict resolution, and automated notifications. This system will handle the core scheduling needs of healthcare facilities.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create appointment booking and management system
- Build calendar-based scheduling interface
- Implement time slot management and conflict detection
- Design appointment status tracking and workflows
- Set up automated notifications and reminders
- Create patient and staff appointment dashboards

## 🏥 Appointment System Requirements

### Core Features

1. **Appointment Booking**: Online and offline appointment scheduling
2. **Calendar Integration**: Visual calendar interface for scheduling
3. **Time Slot Management**: Available time slots based on doctor schedules
4. **Conflict Detection**: Prevent double-booking and scheduling conflicts
5. **Status Tracking**: Appointment lifecycle management
6. **Notifications**: Automated reminders and confirmations

### User Stories

- **Patient**: "I want to book appointments online and receive reminders"
- **Receptionist**: "I need to manage appointments and handle walk-ins"
- **Doctor**: "I want to see my daily schedule and patient information"
- **Admin**: "I need to monitor appointment metrics and optimize scheduling"

## 🛠 Backend Implementation

### Step 1: Appointments Migration

Create the appointments table:

```bash
php artisan make:migration create_appointments_table
php artisan make:migration create_appointment_notes_table
```

**database/migrations/xxxx_create_appointments_table.php**:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Appointment details
            $table->string('appointment_number')->unique();
            $table->date('appointment_date');
            $table->time('appointment_time');
            $table->integer('duration')->default(30); // minutes
            $table->time('end_time')->nullable(); // calculated field
            
            // Appointment type and priority
            $table->enum('type', ['consultation', 'follow_up', 'emergency', 'procedure', 'checkup'])->default('consultation');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            
            // Status tracking
            $table->enum('status', ['scheduled', 'confirmed', 'checked_in', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('scheduled');
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('checked_in_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            
            // Additional information
            $table->text('reason')->nullable(); // Chief complaint
            $table->text('notes')->nullable(); // Appointment notes
            $table->text('cancellation_reason')->nullable();
            $table->foreignId('cancelled_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('cancelled_at')->nullable();
            
            // Booking information
            $table->enum('booking_source', ['online', 'phone', 'walk_in', 'referral'])->default('online');
            $table->json('reminder_settings')->nullable(); // SMS, email preferences
            $table->timestamp('last_reminder_sent')->nullable();
            
            // BPJS integration
            $table->boolean('uses_bpjs')->default(false);
            $table->string('sep_number')->nullable(); // SEP number if BPJS
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['appointment_date', 'appointment_time']);
            $table->index(['doctor_id', 'appointment_date']);
            $table->index(['patient_id', 'status']);
            $table->index(['clinic_id', 'appointment_date']);
            $table->index('status');
            
            // Unique constraint to prevent double booking
            $table->unique(['doctor_id', 'appointment_date', 'appointment_time'], 'unique_doctor_slot');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
```

### Step 2: Appointment Model

Create **app/Models/Appointment.php**:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id', 'doctor_id', 'clinic_id', 'created_by',
        'appointment_number', 'appointment_date', 'appointment_time',
        'duration', 'end_time', 'type', 'priority', 'status',
        'reason', 'notes', 'booking_source', 'reminder_settings',
        'uses_bpjs', 'sep_number'
    ];

    protected function casts(): array
    {
        return [
            'appointment_date' => 'date',
            'appointment_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'confirmed_at' => 'datetime',
            'checked_in_at' => 'datetime',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'last_reminder_sent' => 'datetime',
            'reminder_settings' => 'array',
            'uses_bpjs' => 'boolean',
        ];
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function cancelledBy()
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('appointment_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('appointment_date', '>=', today())
            ->whereNotIn('status', ['completed', 'cancelled', 'no_show']);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeForDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    // Accessors & Mutators
    public function getFullDateTimeAttribute()
    {
        return Carbon::parse($this->appointment_date->format('Y-m-d') . ' ' . $this->appointment_time->format('H:i:s'));
    }

    public function getIsUpcomingAttribute()
    {
        return $this->full_date_time > now() && !in_array($this->status, ['completed', 'cancelled', 'no_show']);
    }

    public function getIsTodayAttribute()
    {
        return $this->appointment_date->isToday();
    }

    public function getCanBeCancelledAttribute()
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && $this->full_date_time > now();
    }

    public function getCanBeRescheduledAttribute()
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && $this->full_date_time > now();
    }

    // Helper methods
    public function calculateEndTime()
    {
        $startTime = Carbon::parse($this->appointment_time);
        $this->end_time = $startTime->addMinutes($this->duration);
        return $this->end_time;
    }

    public function confirm()
    {
        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now()
        ]);
    }

    public function checkIn()
    {
        $this->update([
            'status' => 'checked_in',
            'checked_in_at' => now()
        ]);
    }

    public function start()
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now()
        ]);
    }

    public function complete()
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }

    public function cancel($reason = null, $cancelledBy = null)
    {
        $this->update([
            'status' => 'cancelled',
            'cancellation_reason' => $reason,
            'cancelled_by' => $cancelledBy ?? auth()->id(),
            'cancelled_at' => now()
        ]);
    }

    public function markNoShow()
    {
        $this->update([
            'status' => 'no_show'
        ]);
    }

    // Boot method for auto-generating appointment number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($appointment) {
            if (empty($appointment->appointment_number)) {
                $appointment->appointment_number = static::generateAppointmentNumber();
            }
            
            // Calculate end time
            if (empty($appointment->end_time)) {
                $appointment->calculateEndTime();
            }
        });
    }

    public static function generateAppointmentNumber()
    {
        $date = now()->format('Ymd');
        $lastAppointment = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastAppointment) {
            $lastNumber = substr($lastAppointment->appointment_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return 'APT' . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

### Step 3: Appointment Service

Create **app/Services/AppointmentService.php**:

```php
<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\Doctor;
use App\Models\Patient;
use App\Models\StaffSchedule;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class AppointmentService
{
    /**
     * Get available time slots for a doctor on a specific date
     */
    public function getAvailableTimeSlots($doctorId, $date, $duration = 30): Collection
    {
        $doctor = Doctor::with('user')->findOrFail($doctorId);
        $appointmentDate = Carbon::parse($date);
        $dayOfWeek = strtolower($appointmentDate->format('l'));

        // Check if doctor is available on this day
        if (!$doctor->isAvailableOn($dayOfWeek)) {
            return collect();
        }

        // Get doctor's schedule for this day
        $schedule = StaffSchedule::where('user_id', $doctor->user_id)
            ->forDay($dayOfWeek)
            ->current()
            ->active()
            ->first();

        if (!$schedule) {
            // Use default doctor hours if no specific schedule
            $startTime = Carbon::parse($doctor->start_time);
            $endTime = Carbon::parse($doctor->end_time);
        } else {
            $startTime = Carbon::parse($schedule->start_time);
            $endTime = Carbon::parse($schedule->end_time);
        }

        // Get existing appointments for this doctor on this date
        $existingAppointments = Appointment::forDoctor($doctorId)
            ->whereDate('appointment_date', $appointmentDate)
            ->whereNotIn('status', ['cancelled', 'no_show'])
            ->orderBy('appointment_time')
            ->get();

        // Generate time slots
        $timeSlots = collect();
        $currentTime = $startTime->copy();

        while ($currentTime->addMinutes($duration)->lte($endTime)) {
            $slotStart = $currentTime->copy()->subMinutes($duration);
            $slotEnd = $currentTime->copy();

            // Check if this slot conflicts with existing appointments
            $hasConflict = $existingAppointments->contains(function ($appointment) use ($slotStart, $slotEnd) {
                $appointmentStart = Carbon::parse($appointment->appointment_time);
                $appointmentEnd = Carbon::parse($appointment->end_time);

                return $slotStart->lt($appointmentEnd) && $slotEnd->gt($appointmentStart);
            });

            if (!$hasConflict) {
                $timeSlots->push([
                    'time' => $slotStart->format('H:i'),
                    'display_time' => $slotStart->format('g:i A'),
                    'available' => true,
                    'datetime' => $appointmentDate->copy()->setTimeFromTimeString($slotStart->format('H:i:s'))
                ]);
            }
        }

        return $timeSlots;
    }

    /**
     * Check if appointment slot is available
     */
    public function isSlotAvailable($doctorId, $date, $time, $duration = 30, $excludeAppointmentId = null): bool
    {
        $appointmentStart = Carbon::parse($date . ' ' . $time);
        $appointmentEnd = $appointmentStart->copy()->addMinutes($duration);

        $query = Appointment::forDoctor($doctorId)
            ->whereDate('appointment_date', $date)
            ->whereNotIn('status', ['cancelled', 'no_show'])
            ->where(function ($q) use ($appointmentStart, $appointmentEnd) {
                $q->whereBetween('appointment_time', [$appointmentStart->format('H:i'), $appointmentEnd->format('H:i')])
                  ->orWhere(function ($subQ) use ($appointmentStart, $appointmentEnd) {
                      $subQ->where('appointment_time', '<', $appointmentStart->format('H:i'))
                           ->where('end_time', '>', $appointmentStart->format('H:i'));
                  });
            });

        if ($excludeAppointmentId) {
            $query->where('id', '!=', $excludeAppointmentId);
        }

        return $query->count() === 0;
    }

    /**
     * Create new appointment
     */
    public function createAppointment(array $data): Appointment
    {
        // Validate slot availability
        if (!$this->isSlotAvailable($data['doctor_id'], $data['appointment_date'], $data['appointment_time'], $data['duration'] ?? 30)) {
            throw new \Exception('The selected time slot is not available.');
        }

        return Appointment::create($data);
    }

    /**
     * Reschedule appointment
     */
    public function rescheduleAppointment(Appointment $appointment, $newDate, $newTime): bool
    {
        if (!$this->isSlotAvailable($appointment->doctor_id, $newDate, $newTime, $appointment->duration, $appointment->id)) {
            throw new \Exception('The new time slot is not available.');
        }

        return $appointment->update([
            'appointment_date' => $newDate,
            'appointment_time' => $newTime,
            'status' => 'scheduled' // Reset status when rescheduled
        ]);
    }

    /**
     * Get doctor's daily schedule
     */
    public function getDoctorDailySchedule($doctorId, $date): Collection
    {
        return Appointment::forDoctor($doctorId)
            ->whereDate('appointment_date', $date)
            ->whereNotIn('status', ['cancelled', 'no_show'])
            ->with(['patient', 'clinic'])
            ->orderBy('appointment_time')
            ->get();
    }

    /**
     * Get appointment statistics
     */
    public function getAppointmentStats($clinicId, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->startOfMonth();
        $endDate = $endDate ? Carbon::parse($endDate) : now()->endOfMonth();

        $query = Appointment::where('clinic_id', $clinicId)
            ->whereBetween('appointment_date', [$startDate, $endDate]);

        return [
            'total_appointments' => $query->count(),
            'completed_appointments' => $query->clone()->where('status', 'completed')->count(),
            'cancelled_appointments' => $query->clone()->where('status', 'cancelled')->count(),
            'no_show_appointments' => $query->clone()->where('status', 'no_show')->count(),
            'upcoming_appointments' => $query->clone()->upcoming()->count(),
            'today_appointments' => Appointment::where('clinic_id', $clinicId)->today()->count(),
        ];
    }
}
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive appointment scheduling system** with calendar integration
2. ✅ **Implemented time slot management** with conflict detection
3. ✅ **Created appointment status tracking** and workflow management
4. ✅ **Designed appointment service layer** for business logic
5. ✅ **Added appointment statistics** and reporting capabilities
6. ✅ **Built appointment model** with full lifecycle management

### What We Have Now

- Complete appointment booking and management system
- Time slot availability checking and conflict prevention
- Appointment status lifecycle management
- Doctor schedule integration
- Appointment statistics and reporting foundation

### Next Steps

In **Chapter 7: Medical Records and History**, we'll:

- Build medical record management system
- Create patient medical history tracking
- Implement diagnosis and treatment recording
- Design secure medical record interfaces

---

**Ready to continue?** Proceed to [Chapter 7: Medical Records and History](./chapter-07-medical-records.md) to build the medical records system.
