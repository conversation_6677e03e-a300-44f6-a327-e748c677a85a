# Chapter 04 - Patient Management System

## Tujuan Chapter
Pada chapter ini, kita akan:
- Membuat Eloquent model untuk Patient
- Implementasi CRUD operations untuk patient management
- Membuat form registrasi pasien yang komprehensif
- Setup patient search dan filtering
- Implementasi patient profile dan medical history

## Langkah 1: Create Patient Model

### 1.1 Generate Patient Model
```bash
php artisan make:model Hospital/Patient
```

Edit `app/Models/Hospital/Patient.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Patient extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'patient_id',
        'nik',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'phone',
        'email',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'blood_type',
        'allergies',
        'medical_history',
        'marital_status',
        'occupation',
        'insurance_number',
        'insurance_provider',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'date_of_birth' => 'date',
            'is_active' => 'boolean',
        ];
    }

    // Accessor untuk full name
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    // Accessor untuk age
    public function getAgeAttribute(): int
    {
        return $this->date_of_birth->age;
    }

    // Generate patient ID otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($patient) {
            if (empty($patient->patient_id)) {
                $patient->patient_id = self::generatePatientId();
            }
        });
    }

    private static function generatePatientId(): string
    {
        $prefix = 'P';
        $year = date('Y');
        $month = date('m');
        
        // Get last patient number for current month
        $lastPatient = self::where('patient_id', 'like', $prefix . $year . $month . '%')
                          ->orderBy('patient_id', 'desc')
                          ->first();

        if ($lastPatient) {
            $lastNumber = (int) substr($lastPatient->patient_id, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'like', "%{$search}%")
              ->orWhere('last_name', 'like', "%{$search}%")
              ->orWhere('patient_id', 'like', "%{$search}%")
              ->orWhere('nik', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%");
        });
    }
}
```

## Langkah 2: Create Patient Controller

### 2.1 Generate Controller
```bash
php artisan make:controller Hospital/PatientController --resource
```

Edit `app/Http/Controllers/Hospital/PatientController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\Patient;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PatientController extends Controller
{
    public function index(Request $request)
    {
        $query = Patient::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by gender
        if ($request->filled('gender')) {
            $query->byGender($request->gender);
        }

        // Filter by active status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $patients = $query->orderBy('created_at', 'desc')
                         ->paginate(15)
                         ->withQueryString();

        return Inertia::render('Hospital/Patients/Index', [
            'patients' => $patients,
            'filters' => $request->only(['search', 'gender', 'status']),
        ]);
    }

    public function create()
    {
        return Inertia::render('Hospital/Patients/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nik' => 'required|string|size:16|unique:patients,nik',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'phone' => 'required|string|max:15',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:15',
            'blood_type' => 'nullable|string|max:5',
            'allergies' => 'nullable|string',
            'medical_history' => 'nullable|string',
            'marital_status' => 'required|in:single,married,divorced,widowed',
            'occupation' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:255',
            'insurance_provider' => 'nullable|string|max:255',
        ]);

        $patient = Patient::create($validated);

        return redirect()->route('patients.show', $patient)
                        ->with('success', 'Pasien berhasil didaftarkan.');
    }

    public function show(Patient $patient)
    {
        $patient->load(['appointments.doctor', 'medicalRecords.doctor']);

        return Inertia::render('Hospital/Patients/Show', [
            'patient' => $patient,
        ]);
    }

    public function edit(Patient $patient)
    {
        return Inertia::render('Hospital/Patients/Edit', [
            'patient' => $patient,
        ]);
    }

    public function update(Request $request, Patient $patient)
    {
        $validated = $request->validate([
            'nik' => 'required|string|size:16|unique:patients,nik,' . $patient->id,
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'phone' => 'required|string|max:15',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:15',
            'blood_type' => 'nullable|string|max:5',
            'allergies' => 'nullable|string',
            'medical_history' => 'nullable|string',
            'marital_status' => 'required|in:single,married,divorced,widowed',
            'occupation' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:255',
            'insurance_provider' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $patient->update($validated);

        return redirect()->route('patients.show', $patient)
                        ->with('success', 'Data pasien berhasil diperbarui.');
    }

    public function destroy(Patient $patient)
    {
        $patient->delete();

        return redirect()->route('patients.index')
                        ->with('success', 'Pasien berhasil dihapus.');
    }
}
```

## Langkah 3: Create Patient Request Validation

### 3.1 Create Form Request
```bash
php artisan make:request Hospital/StorePatientRequest
php artisan make:request Hospital/UpdatePatientRequest
```

Edit `app/Http/Requests/Hospital/StorePatientRequest.php`:
```php
<?php

namespace App\Http\Requests\Hospital;

use Illuminate\Foundation\Http\FormRequest;

class StorePatientRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check() && in_array(auth()->user()->role, ['admin', 'receptionist', 'nurse']);
    }

    public function rules(): array
    {
        return [
            'nik' => 'required|string|size:16|unique:patients,nik',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'phone' => 'required|string|max:15',
            'email' => 'nullable|email|max:255',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:15',
            'blood_type' => 'nullable|string|max:5',
            'allergies' => 'nullable|string',
            'medical_history' => 'nullable|string',
            'marital_status' => 'required|in:single,married,divorced,widowed',
            'occupation' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:255',
            'insurance_provider' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'nik.required' => 'NIK wajib diisi.',
            'nik.size' => 'NIK harus 16 digit.',
            'nik.unique' => 'NIK sudah terdaftar.',
            'first_name.required' => 'Nama depan wajib diisi.',
            'last_name.required' => 'Nama belakang wajib diisi.',
            'date_of_birth.required' => 'Tanggal lahir wajib diisi.',
            'date_of_birth.before' => 'Tanggal lahir harus sebelum hari ini.',
            'gender.required' => 'Jenis kelamin wajib dipilih.',
            'phone.required' => 'Nomor telepon wajib diisi.',
            'address.required' => 'Alamat wajib diisi.',
            'emergency_contact_name.required' => 'Nama kontak darurat wajib diisi.',
            'emergency_contact_phone.required' => 'Nomor kontak darurat wajib diisi.',
            'marital_status.required' => 'Status pernikahan wajib dipilih.',
        ];
    }
}
```

## Langkah 4: Create Patient Routes

### 4.1 Add Routes
Edit `routes/web.php` dan tambahkan:
```php
// Patient management routes
Route::middleware(['auth', 'role:admin,receptionist,nurse,doctor'])->group(function () {
    Route::resource('patients', PatientController::class);
    Route::get('/patients/{patient}/medical-history', [PatientController::class, 'medicalHistory'])
         ->name('patients.medical-history');
});
```

## Langkah 5: Create React Components

### 5.1 Create Patient Index Component
Create `resources/js/Pages/Hospital/Patients/Index.jsx`:
```jsx
import { useState } from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, Link, router } from '@inertiajs/react';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import { MagnifyingGlassIcon, PlusIcon } from '@heroicons/react/24/outline';

export default function Index({ auth, patients, filters }) {
    const [search, setSearch] = useState(filters.search || '');
    const [gender, setGender] = useState(filters.gender || '');
    const [status, setStatus] = useState(filters.status || '');

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(route('patients.index'), {
            search,
            gender,
            status,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const clearFilters = () => {
        setSearch('');
        setGender('');
        setStatus('');
        router.get(route('patients.index'));
    };

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Manajemen Pasien
                    </h2>
                    <Link href={route('patients.create')}>
                        <PrimaryButton>
                            <PlusIcon className="w-4 h-4 mr-2" />
                            Daftar Pasien Baru
                        </PrimaryButton>
                    </Link>
                </div>
            }
        >
            <Head title="Manajemen Pasien" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Search and Filters */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6">
                            <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <TextInput
                                        type="text"
                                        placeholder="Cari pasien..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <select
                                        value={gender}
                                        onChange={(e) => setGender(e.target.value)}
                                        className="w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    >
                                        <option value="">Semua Jenis Kelamin</option>
                                        <option value="male">Laki-laki</option>
                                        <option value="female">Perempuan</option>
                                    </select>
                                </div>
                                <div>
                                    <select
                                        value={status}
                                        onChange={(e) => setStatus(e.target.value)}
                                        className="w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    >
                                        <option value="">Semua Status</option>
                                        <option value="active">Aktif</option>
                                        <option value="inactive">Tidak Aktif</option>
                                    </select>
                                </div>
                                <div className="flex space-x-2">
                                    <button
                                        type="submit"
                                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                                    >
                                        <MagnifyingGlassIcon className="w-4 h-4 inline mr-1" />
                                        Cari
                                    </button>
                                    <button
                                        type="button"
                                        onClick={clearFilters}
                                        className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                                    >
                                        Reset
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    {/* Patients Table */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            ID Pasien
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Nama Lengkap
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Jenis Kelamin
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Umur
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Telepon
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Aksi
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {patients.data.map((patient) => (
                                        <tr key={patient.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {patient.patient_id}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {patient.full_name}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    NIK: {patient.nik}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {patient.gender === 'male' ? 'Laki-laki' : 'Perempuan'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {patient.age} tahun
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {patient.phone}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    patient.is_active 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {patient.is_active ? 'Aktif' : 'Tidak Aktif'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <Link
                                                    href={route('patients.show', patient.id)}
                                                    className="text-indigo-600 hover:text-indigo-900 mr-3"
                                                >
                                                    Lihat
                                                </Link>
                                                <Link
                                                    href={route('patients.edit', patient.id)}
                                                    className="text-yellow-600 hover:text-yellow-900"
                                                >
                                                    Edit
                                                </Link>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {patients.links && (
                            <div className="px-6 py-3 border-t border-gray-200">
                                {/* Pagination component will be implemented */}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Melanjutkan implementasi form Create dan Edit patient
- Membuat patient detail view
- Implementasi doctor/staff management
- Setup appointment scheduling

## Checklist Completion

- [ ] Patient model dibuat dengan relationships
- [ ] Patient controller dengan CRUD operations
- [ ] Form request validation
- [ ] Patient routes terdaftar
- [ ] Patient index page dengan search dan filter
- [ ] Auto-generate patient ID
- [ ] Soft delete implementation

**Estimasi Waktu**: 75-90 menit

**Difficulty Level**: Intermediate

**File yang Dibuat**:
- Patient model
- PatientController
- StorePatientRequest
- Patient routes
- Patient Index React component
