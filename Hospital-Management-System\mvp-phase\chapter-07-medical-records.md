# Chapter 07 - Medical Records Management

## Tujuan Chapter
Pada chapter ini, kita akan:
- Membuat Eloquent model untuk Medical Records
- Implementasi medical records CRUD operations
- Setup vital signs recording system
- Membuat prescription management
- Implementasi medical history tracking

## Langkah 1: Create Medical Record Model

### 1.1 Generate Medical Record Model
```bash
php artisan make:model Hospital/MedicalRecord
```

Edit `app/Models/Hospital/MedicalRecord.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicalRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'record_number',
        'patient_id',
        'doctor_id',
        'appointment_id',
        'visit_date',
        'chief_complaint',
        'history_of_present_illness',
        'physical_examination',
        'diagnosis',
        'treatment_plan',
        'medications',
        'lab_results',
        'notes',
        'next_visit_date',
        'vital_signs_temperature',
        'vital_signs_blood_pressure_systolic',
        'vital_signs_blood_pressure_diastolic',
        'vital_signs_heart_rate',
        'vital_signs_respiratory_rate',
        'vital_signs_weight',
        'vital_signs_height',
    ];

    protected function casts(): array
    {
        return [
            'visit_date' => 'datetime',
            'next_visit_date' => 'datetime',
            'vital_signs_temperature' => 'decimal:1',
            'vital_signs_weight' => 'decimal:2',
            'vital_signs_height' => 'decimal:2',
        ];
    }

    // Generate record number otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($record) {
            if (empty($record->record_number)) {
                $record->record_number = self::generateRecordNumber();
            }
        });
    }

    private static function generateRecordNumber(): string
    {
        $prefix = 'MR';
        $date = date('Ymd');
        
        $lastRecord = self::where('record_number', 'like', $prefix . $date . '%')
                         ->orderBy('record_number', 'desc')
                         ->first();

        if ($lastRecord) {
            $lastNumber = (int) substr($lastRecord->record_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    // Accessors
    public function getBloodPressureAttribute(): ?string
    {
        if ($this->vital_signs_blood_pressure_systolic && $this->vital_signs_blood_pressure_diastolic) {
            return $this->vital_signs_blood_pressure_systolic . '/' . $this->vital_signs_blood_pressure_diastolic;
        }
        return null;
    }

    public function getBmiAttribute(): ?float
    {
        if ($this->vital_signs_weight && $this->vital_signs_height) {
            $heightInMeters = $this->vital_signs_height / 100;
            return round($this->vital_signs_weight / ($heightInMeters * $heightInMeters), 2);
        }
        return null;
    }

    public function getBmiCategoryAttribute(): ?string
    {
        $bmi = $this->bmi;
        if (!$bmi) return null;

        if ($bmi < 18.5) return 'Underweight';
        if ($bmi < 25) return 'Normal';
        if ($bmi < 30) return 'Overweight';
        return 'Obese';
    }

    public function getVitalSignsSummaryAttribute(): array
    {
        return [
            'temperature' => $this->vital_signs_temperature ? $this->vital_signs_temperature . '°C' : null,
            'blood_pressure' => $this->blood_pressure ? $this->blood_pressure . ' mmHg' : null,
            'heart_rate' => $this->vital_signs_heart_rate ? $this->vital_signs_heart_rate . ' bpm' : null,
            'respiratory_rate' => $this->vital_signs_respiratory_rate ? $this->vital_signs_respiratory_rate . ' /min' : null,
            'weight' => $this->vital_signs_weight ? $this->vital_signs_weight . ' kg' : null,
            'height' => $this->vital_signs_height ? $this->vital_signs_height . ' cm' : null,
            'bmi' => $this->bmi ? $this->bmi . ' (' . $this->bmi_category . ')' : null,
        ];
    }

    // Scopes
    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('visit_date', [$startDate, $endDate]);
    }

    public function scopeRecent($query, $limit = 10)
    {
        return $query->orderBy('visit_date', 'desc')->limit($limit);
    }
}
```

## Langkah 2: Create Medical Record Controller

### 2.1 Generate Controller
```bash
php artisan make:controller Hospital/MedicalRecordController --resource
```

Edit `app/Http/Controllers/Hospital/MedicalRecordController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\MedicalRecord;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Appointment;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MedicalRecordController extends Controller
{
    public function index(Request $request)
    {
        $query = MedicalRecord::with(['patient', 'doctor.user', 'appointment']);

        // Filter by patient
        if ($request->filled('patient_id')) {
            $query->byPatient($request->patient_id);
        }

        // Filter by doctor
        if ($request->filled('doctor_id')) {
            $query->byDoctor($request->doctor_id);
        }

        // Filter by date range
        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('record_number', 'like', "%{$request->search}%")
                  ->orWhere('diagnosis', 'like', "%{$request->search}%")
                  ->orWhere('chief_complaint', 'like', "%{$request->search}%")
                  ->orWhereHas('patient', function ($patientQuery) use ($request) {
                      $patientQuery->where('first_name', 'like', "%{$request->search}%")
                                  ->orWhere('last_name', 'like', "%{$request->search}%")
                                  ->orWhere('patient_id', 'like', "%{$request->search}%");
                  });
            });
        }

        $medicalRecords = $query->orderBy('visit_date', 'desc')
                               ->paginate(15)
                               ->withQueryString();

        $patients = Patient::active()->get();
        $doctors = Doctor::with('user')->active()->get();

        return Inertia::render('Hospital/MedicalRecords/Index', [
            'medicalRecords' => $medicalRecords,
            'patients' => $patients,
            'doctors' => $doctors,
            'filters' => $request->only(['patient_id', 'doctor_id', 'date_from', 'date_to', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        $patients = Patient::active()->get();
        $doctors = Doctor::with('user')->active()->get();
        
        // Pre-select data if coming from appointment
        $selectedAppointment = null;
        $selectedPatient = null;
        $selectedDoctor = null;

        if ($request->filled('appointment_id')) {
            $selectedAppointment = Appointment::with(['patient', 'doctor.user'])
                                             ->find($request->appointment_id);
            if ($selectedAppointment) {
                $selectedPatient = $selectedAppointment->patient;
                $selectedDoctor = $selectedAppointment->doctor;
            }
        } elseif ($request->filled('patient_id')) {
            $selectedPatient = Patient::find($request->patient_id);
        }

        return Inertia::render('Hospital/MedicalRecords/Create', [
            'patients' => $patients,
            'doctors' => $doctors,
            'selectedAppointment' => $selectedAppointment,
            'selectedPatient' => $selectedPatient,
            'selectedDoctor' => $selectedDoctor,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'visit_date' => 'required|date',
            'chief_complaint' => 'required|string',
            'history_of_present_illness' => 'required|string',
            'physical_examination' => 'required|string',
            'diagnosis' => 'required|string',
            'treatment_plan' => 'required|string',
            'medications' => 'nullable|string',
            'lab_results' => 'nullable|string',
            'notes' => 'nullable|string',
            'next_visit_date' => 'nullable|date|after:visit_date',
            
            // Vital signs
            'vital_signs_temperature' => 'nullable|numeric|between:30,45',
            'vital_signs_blood_pressure_systolic' => 'nullable|integer|between:70,250',
            'vital_signs_blood_pressure_diastolic' => 'nullable|integer|between:40,150',
            'vital_signs_heart_rate' => 'nullable|integer|between:40,200',
            'vital_signs_respiratory_rate' => 'nullable|integer|between:10,60',
            'vital_signs_weight' => 'nullable|numeric|between:1,300',
            'vital_signs_height' => 'nullable|numeric|between:30,250',
        ]);

        $medicalRecord = MedicalRecord::create($validated);

        // Update appointment status if linked
        if ($medicalRecord->appointment_id) {
            $medicalRecord->appointment->update(['status' => 'completed']);
        }

        return redirect()->route('medical-records.show', $medicalRecord)
                        ->with('success', 'Rekam medis berhasil dibuat.');
    }

    public function show(MedicalRecord $medicalRecord)
    {
        $medicalRecord->load(['patient', 'doctor.user', 'appointment']);

        // Get patient's medical history
        $patientHistory = MedicalRecord::byPatient($medicalRecord->patient_id)
                                      ->with(['doctor.user'])
                                      ->where('id', '!=', $medicalRecord->id)
                                      ->orderBy('visit_date', 'desc')
                                      ->limit(5)
                                      ->get();

        return Inertia::render('Hospital/MedicalRecords/Show', [
            'medicalRecord' => $medicalRecord,
            'patientHistory' => $patientHistory,
        ]);
    }

    public function edit(MedicalRecord $medicalRecord)
    {
        $patients = Patient::active()->get();
        $doctors = Doctor::with('user')->active()->get();
        
        $medicalRecord->load(['patient', 'doctor', 'appointment']);

        return Inertia::render('Hospital/MedicalRecords/Edit', [
            'medicalRecord' => $medicalRecord,
            'patients' => $patients,
            'doctors' => $doctors,
        ]);
    }

    public function update(Request $request, MedicalRecord $medicalRecord)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'doctor_id' => 'required|exists:doctors,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'visit_date' => 'required|date',
            'chief_complaint' => 'required|string',
            'history_of_present_illness' => 'required|string',
            'physical_examination' => 'required|string',
            'diagnosis' => 'required|string',
            'treatment_plan' => 'required|string',
            'medications' => 'nullable|string',
            'lab_results' => 'nullable|string',
            'notes' => 'nullable|string',
            'next_visit_date' => 'nullable|date|after:visit_date',
            
            // Vital signs
            'vital_signs_temperature' => 'nullable|numeric|between:30,45',
            'vital_signs_blood_pressure_systolic' => 'nullable|integer|between:70,250',
            'vital_signs_blood_pressure_diastolic' => 'nullable|integer|between:40,150',
            'vital_signs_heart_rate' => 'nullable|integer|between:40,200',
            'vital_signs_respiratory_rate' => 'nullable|integer|between:10,60',
            'vital_signs_weight' => 'nullable|numeric|between:1,300',
            'vital_signs_height' => 'nullable|numeric|between:30,250',
        ]);

        $medicalRecord->update($validated);

        return redirect()->route('medical-records.show', $medicalRecord)
                        ->with('success', 'Rekam medis berhasil diperbarui.');
    }

    public function destroy(MedicalRecord $medicalRecord)
    {
        $medicalRecord->delete();

        return redirect()->route('medical-records.index')
                        ->with('success', 'Rekam medis berhasil dihapus.');
    }

    // Additional methods
    public function patientHistory($patientId)
    {
        $patient = Patient::findOrFail($patientId);
        
        $medicalRecords = MedicalRecord::byPatient($patientId)
                                      ->with(['doctor.user', 'appointment'])
                                      ->orderBy('visit_date', 'desc')
                                      ->paginate(10);

        return Inertia::render('Hospital/MedicalRecords/PatientHistory', [
            'patient' => $patient,
            'medicalRecords' => $medicalRecords,
        ]);
    }

    public function printRecord(MedicalRecord $medicalRecord)
    {
        $medicalRecord->load(['patient', 'doctor.user', 'appointment']);

        return Inertia::render('Hospital/MedicalRecords/Print', [
            'medicalRecord' => $medicalRecord,
        ]);
    }
}
```

## Langkah 3: Create Prescription Model

### 3.1 Generate Prescription Model
```bash
php artisan make:model Hospital/Prescription -m
```

Edit migration `database/migrations/xxxx_create_prescriptions_table.php`:
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->string('prescription_number', 20)->unique();
            $table->foreignId('medical_record_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained()->onDelete('restrict');
            $table->json('medications'); // Array of medications with dosage, frequency, duration
            $table->text('instructions')->nullable();
            $table->enum('status', ['pending', 'dispensed', 'completed', 'cancelled'])->default('pending');
            $table->datetime('dispensed_at')->nullable();
            $table->foreignId('dispensed_by')->nullable()->constrained('users');
            $table->text('pharmacist_notes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('prescriptions');
    }
};
```

Edit `app/Models/Hospital/Prescription.php`:
```php
<?php

namespace App\Models\Hospital;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_number',
        'medical_record_id',
        'patient_id',
        'doctor_id',
        'medications',
        'instructions',
        'status',
        'dispensed_at',
        'dispensed_by',
        'pharmacist_notes',
    ];

    protected function casts(): array
    {
        return [
            'medications' => 'array',
            'dispensed_at' => 'datetime',
        ];
    }

    // Generate prescription number otomatis
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            if (empty($prescription->prescription_number)) {
                $prescription->prescription_number = self::generatePrescriptionNumber();
            }
        });
    }

    private static function generatePrescriptionNumber(): string
    {
        $prefix = 'RX';
        $date = date('Ymd');
        
        $lastPrescription = self::where('prescription_number', 'like', $prefix . $date . '%')
                               ->orderBy('prescription_number', 'desc')
                               ->first();

        if ($lastPrescription) {
            $lastNumber = (int) substr($lastPrescription->prescription_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $date . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function medicalRecord()
    {
        return $this->belongsTo(MedicalRecord::class);
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function dispensedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'dispensed_by');
    }

    // Accessors
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => 'Menunggu',
            'dispensed' => 'Sudah Diserahkan',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeByDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }
}
```

## Langkah 4: Add Medical Records Routes

### 4.1 Update Routes
Edit `routes/web.php` dan tambahkan:
```php
// Medical Records routes
Route::middleware(['auth', 'role:admin,doctor,nurse'])->group(function () {
    Route::resource('medical-records', MedicalRecordController::class);
    Route::get('/patients/{patient}/medical-history', [MedicalRecordController::class, 'patientHistory'])
         ->name('patients.medical-history');
    Route::get('/medical-records/{medicalRecord}/print', [MedicalRecordController::class, 'printRecord'])
         ->name('medical-records.print');
});

// Prescription routes
Route::middleware(['auth', 'role:admin,doctor,pharmacist'])->group(function () {
    Route::resource('prescriptions', PrescriptionController::class);
});
```

## Langkah 5: Create React Components

### 5.1 Create Medical Records Index Component
Create `resources/js/Pages/Hospital/MedicalRecords/Index.jsx`:
```jsx
import { useState } from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, Link, router } from '@inertiajs/react';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import SelectInput from '@/Components/SelectInput';

export default function MedicalRecordIndex({ auth, medicalRecords, doctors, filters }) {
    const [search, setSearch] = useState(filters.search || '');
    const [doctor, setDoctor] = useState(filters.doctor || '');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(route('medical-records.index'), {
            search,
            doctor,
            date_from: dateFrom,
            date_to: dateTo,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Rekam Medis
                    </h2>
                    <Link href={route('medical-records.create')}>
                        <PrimaryButton>Buat Rekam Medis</PrimaryButton>
                    </Link>
                </div>
            }
        >
            <Head title="Rekam Medis" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Search and Filter */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6">
                            <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <TextInput
                                        placeholder="Cari pasien..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <SelectInput
                                        value={doctor}
                                        onChange={(e) => setDoctor(e.target.value)}
                                        className="w-full"
                                    >
                                        <option value="">Semua Dokter</option>
                                        {doctors.map((doc) => (
                                            <option key={doc.id} value={doc.id}>
                                                {doc.user.full_name}
                                            </option>
                                        ))}
                                    </SelectInput>
                                </div>
                                <div>
                                    <TextInput
                                        type="date"
                                        placeholder="Dari tanggal"
                                        value={dateFrom}
                                        onChange={(e) => setDateFrom(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <TextInput
                                        type="date"
                                        placeholder="Sampai tanggal"
                                        value={dateTo}
                                        onChange={(e) => setDateTo(e.target.value)}
                                        className="w-full"
                                    />
                                </div>
                                <div>
                                    <PrimaryButton type="submit" className="w-full">
                                        Cari
                                    </PrimaryButton>
                                </div>
                            </form>
                        </div>
                    </div>

                    {/* Medical Records Table */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                No. Rekam Medis
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Pasien
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Dokter
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Tanggal
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Diagnosis
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Aksi
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {medicalRecords.data.map((record) => (
                                            <tr key={record.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {record.record_number}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {record.patient.full_name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {record.patient.patient_id}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {record.doctor.user.full_name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {record.doctor.specialization}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {record.visit_date_formatted}
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="text-sm text-gray-900 max-w-xs truncate">
                                                        {record.diagnosis}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="flex gap-2">
                                                        <Link
                                                            href={route('medical-records.show', record.id)}
                                                            className="text-blue-600 hover:text-blue-900"
                                                        >
                                                            Lihat
                                                        </Link>
                                                        <Link
                                                            href={route('medical-records.edit', record.id)}
                                                            className="text-indigo-600 hover:text-indigo-900"
                                                        >
                                                            Edit
                                                        </Link>
                                                        <Link
                                                            href={route('medical-records.print', record.id)}
                                                            className="text-green-600 hover:text-green-900"
                                                        >
                                                            Print
                                                        </Link>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
```

### 5.2 Create Medical Record Form Component
Create `resources/js/Pages/Hospital/MedicalRecords/Form.jsx`:
```jsx
import { useState } from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import SelectInput from '@/Components/SelectInput';
import InputLabel from '@/Components/InputLabel';
import InputError from '@/Components/InputError';

export default function MedicalRecordForm({
    auth,
    medicalRecord = null,
    patients,
    doctors,
    selectedAppointment = null
}) {
    const isEdit = !!medicalRecord;

    const { data, setData, post, put, processing, errors } = useForm({
        patient_id: medicalRecord?.patient_id || selectedAppointment?.patient_id || '',
        doctor_id: medicalRecord?.doctor_id || selectedAppointment?.doctor_id || '',
        appointment_id: medicalRecord?.appointment_id || selectedAppointment?.id || '',
        visit_date: medicalRecord?.visit_date || new Date().toISOString().split('T')[0],
        chief_complaint: medicalRecord?.chief_complaint || '',
        history_of_present_illness: medicalRecord?.history_of_present_illness || '',
        physical_examination: medicalRecord?.physical_examination || '',
        diagnosis: medicalRecord?.diagnosis || '',
        treatment_plan: medicalRecord?.treatment_plan || '',
        medications: medicalRecord?.medications || '',
        lab_results: medicalRecord?.lab_results || '',
        notes: medicalRecord?.notes || '',
        next_visit_date: medicalRecord?.next_visit_date || '',
        // Vital Signs
        vital_signs_temperature: medicalRecord?.vital_signs_temperature || '',
        vital_signs_blood_pressure_systolic: medicalRecord?.vital_signs_blood_pressure_systolic || '',
        vital_signs_blood_pressure_diastolic: medicalRecord?.vital_signs_blood_pressure_diastolic || '',
        vital_signs_heart_rate: medicalRecord?.vital_signs_heart_rate || '',
        vital_signs_respiratory_rate: medicalRecord?.vital_signs_respiratory_rate || '',
        vital_signs_weight: medicalRecord?.vital_signs_weight || '',
        vital_signs_height: medicalRecord?.vital_signs_height || '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();

        if (isEdit) {
            put(route('medical-records.update', medicalRecord.id));
        } else {
            post(route('medical-records.store'));
        }
    };

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                    {isEdit ? 'Edit Rekam Medis' : 'Buat Rekam Medis Baru'}
                </h2>
            }
        >
            <Head title={isEdit ? 'Edit Rekam Medis' : 'Buat Rekam Medis'} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Patient and Doctor Selection */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <InputLabel htmlFor="patient_id" value="Pasien" />
                                        <SelectInput
                                            id="patient_id"
                                            value={data.patient_id}
                                            onChange={(e) => setData('patient_id', e.target.value)}
                                            className="mt-1 block w-full"
                                            disabled={!!selectedAppointment}
                                        >
                                            <option value="">Pilih Pasien</option>
                                            {patients.map((patient) => (
                                                <option key={patient.id} value={patient.id}>
                                                    {patient.full_name} - {patient.patient_id}
                                                </option>
                                            ))}
                                        </SelectInput>
                                        <InputError message={errors.patient_id} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="doctor_id" value="Dokter" />
                                        <SelectInput
                                            id="doctor_id"
                                            value={data.doctor_id}
                                            onChange={(e) => setData('doctor_id', e.target.value)}
                                            className="mt-1 block w-full"
                                            disabled={!!selectedAppointment}
                                        >
                                            <option value="">Pilih Dokter</option>
                                            {doctors.map((doctor) => (
                                                <option key={doctor.id} value={doctor.id}>
                                                    {doctor.user.full_name} - {doctor.specialization}
                                                </option>
                                            ))}
                                        </SelectInput>
                                        <InputError message={errors.doctor_id} className="mt-2" />
                                    </div>
                                </div>

                                <div>
                                    <InputLabel htmlFor="visit_date" value="Tanggal Kunjungan" />
                                    <TextInput
                                        id="visit_date"
                                        type="date"
                                        value={data.visit_date}
                                        onChange={(e) => setData('visit_date', e.target.value)}
                                        className="mt-1 block w-full"
                                    />
                                    <InputError message={errors.visit_date} className="mt-2" />
                                </div>

                                {/* Vital Signs Section */}
                                <div className="border-t pt-6">
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">Tanda Vital</h3>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        <div>
                                            <InputLabel htmlFor="vital_signs_temperature" value="Suhu (°C)" />
                                            <TextInput
                                                id="vital_signs_temperature"
                                                type="number"
                                                step="0.1"
                                                value={data.vital_signs_temperature}
                                                onChange={(e) => setData('vital_signs_temperature', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="36.5"
                                            />
                                        </div>
                                        <div>
                                            <InputLabel htmlFor="vital_signs_blood_pressure_systolic" value="Sistol (mmHg)" />
                                            <TextInput
                                                id="vital_signs_blood_pressure_systolic"
                                                type="number"
                                                value={data.vital_signs_blood_pressure_systolic}
                                                onChange={(e) => setData('vital_signs_blood_pressure_systolic', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="120"
                                            />
                                        </div>
                                        <div>
                                            <InputLabel htmlFor="vital_signs_blood_pressure_diastolic" value="Diastol (mmHg)" />
                                            <TextInput
                                                id="vital_signs_blood_pressure_diastolic"
                                                type="number"
                                                value={data.vital_signs_blood_pressure_diastolic}
                                                onChange={(e) => setData('vital_signs_blood_pressure_diastolic', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="80"
                                            />
                                        </div>
                                        <div>
                                            <InputLabel htmlFor="vital_signs_heart_rate" value="Detak Jantung (bpm)" />
                                            <TextInput
                                                id="vital_signs_heart_rate"
                                                type="number"
                                                value={data.vital_signs_heart_rate}
                                                onChange={(e) => setData('vital_signs_heart_rate', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="72"
                                            />
                                        </div>
                                        <div>
                                            <InputLabel htmlFor="vital_signs_respiratory_rate" value="Pernapasan (/menit)" />
                                            <TextInput
                                                id="vital_signs_respiratory_rate"
                                                type="number"
                                                value={data.vital_signs_respiratory_rate}
                                                onChange={(e) => setData('vital_signs_respiratory_rate', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="16"
                                            />
                                        </div>
                                        <div>
                                            <InputLabel htmlFor="vital_signs_weight" value="Berat Badan (kg)" />
                                            <TextInput
                                                id="vital_signs_weight"
                                                type="number"
                                                step="0.1"
                                                value={data.vital_signs_weight}
                                                onChange={(e) => setData('vital_signs_weight', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="70.5"
                                            />
                                        </div>
                                        <div>
                                            <InputLabel htmlFor="vital_signs_height" value="Tinggi Badan (cm)" />
                                            <TextInput
                                                id="vital_signs_height"
                                                type="number"
                                                value={data.vital_signs_height}
                                                onChange={(e) => setData('vital_signs_height', e.target.value)}
                                                className="mt-1 block w-full"
                                                placeholder="170"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Medical Information */}
                                <div className="border-t pt-6 space-y-4">
                                    <h3 className="text-lg font-medium text-gray-900">Informasi Medis</h3>

                                    <div>
                                        <InputLabel htmlFor="chief_complaint" value="Keluhan Utama" />
                                        <textarea
                                            id="chief_complaint"
                                            value={data.chief_complaint}
                                            onChange={(e) => setData('chief_complaint', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="3"
                                            required
                                        />
                                        <InputError message={errors.chief_complaint} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="history_of_present_illness" value="Riwayat Penyakit Sekarang" />
                                        <textarea
                                            id="history_of_present_illness"
                                            value={data.history_of_present_illness}
                                            onChange={(e) => setData('history_of_present_illness', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="4"
                                            required
                                        />
                                        <InputError message={errors.history_of_present_illness} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="physical_examination" value="Pemeriksaan Fisik" />
                                        <textarea
                                            id="physical_examination"
                                            value={data.physical_examination}
                                            onChange={(e) => setData('physical_examination', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="4"
                                            required
                                        />
                                        <InputError message={errors.physical_examination} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="diagnosis" value="Diagnosis" />
                                        <textarea
                                            id="diagnosis"
                                            value={data.diagnosis}
                                            onChange={(e) => setData('diagnosis', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="3"
                                            required
                                        />
                                        <InputError message={errors.diagnosis} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="treatment_plan" value="Rencana Pengobatan" />
                                        <textarea
                                            id="treatment_plan"
                                            value={data.treatment_plan}
                                            onChange={(e) => setData('treatment_plan', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="4"
                                            required
                                        />
                                        <InputError message={errors.treatment_plan} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="medications" value="Obat-obatan" />
                                        <textarea
                                            id="medications"
                                            value={data.medications}
                                            onChange={(e) => setData('medications', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="3"
                                        />
                                        <InputError message={errors.medications} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="lab_results" value="Hasil Lab" />
                                        <textarea
                                            id="lab_results"
                                            value={data.lab_results}
                                            onChange={(e) => setData('lab_results', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="3"
                                        />
                                        <InputError message={errors.lab_results} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="notes" value="Catatan Tambahan" />
                                        <textarea
                                            id="notes"
                                            value={data.notes}
                                            onChange={(e) => setData('notes', e.target.value)}
                                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                                            rows="3"
                                        />
                                        <InputError message={errors.notes} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="next_visit_date" value="Tanggal Kunjungan Berikutnya" />
                                        <TextInput
                                            id="next_visit_date"
                                            type="date"
                                            value={data.next_visit_date}
                                            onChange={(e) => setData('next_visit_date', e.target.value)}
                                            className="mt-1 block w-full"
                                        />
                                        <InputError message={errors.next_visit_date} className="mt-2" />
                                    </div>
                                </div>

                                <div className="flex items-center justify-end gap-4">
                                    <Link
                                        href={route('medical-records.index')}
                                        className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                                    >
                                        Batal
                                    </Link>
                                    <PrimaryButton disabled={processing}>
                                        {processing ? 'Menyimpan...' : (isEdit ? 'Update' : 'Simpan')}
                                    </PrimaryButton>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi dashboard dengan metrics dan statistics
- Membuat reporting system
- Setup notification system
- Finalisasi MVP testing

## Checklist Completion

- [x] Medical Record model dengan auto-numbering
- [x] Medical Record controller dengan CRUD operations
- [x] Vital signs recording system
- [x] BMI calculation dan categorization
- [x] Prescription model dan system
- [x] Patient medical history tracking
- [x] Print functionality untuk medical records
- [x] Comprehensive validation untuk medical data
- [x] React components untuk medical records management
- [x] Medical record form dengan vital signs input

**Estimasi Waktu**: 150-180 menit

**Difficulty Level**: Advanced

**File yang Dibuat**:
- MedicalRecord model
- Prescription model
- MedicalRecordController
- Medical records routes
- Prescription migration
- Medical Records Index React component
- Medical Record Form React component
