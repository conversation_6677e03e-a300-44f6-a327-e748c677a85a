# Chapter 4: Patient Management System

## Building Comprehensive Patient Registration and Management

In this chapter, we'll build a complete patient management system that handles patient registration, profile management, medical history tracking, and advanced search capabilities. This forms the core of our clinic management system.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create patient registration and profile management system
- Build advanced search and filtering capabilities
- Implement medical history tracking
- Design responsive patient management interfaces
- Set up patient data validation and security
- Create patient dashboard and self-service features

## 🏥 Patient Management Requirements

### Core Features

1. **Patient Registration**: Complete patient onboarding process
2. **Profile Management**: Comprehensive patient information management
3. **Medical History**: Track patient medical background and conditions
4. **Search & Filter**: Advanced patient search and filtering
5. **BPJS Integration**: Indonesian health insurance integration
6. **Data Security**: HIPAA-compliant data handling

### User Stories

- **Receptionist**: "I need to quickly register new patients and update their information"
- **Doctor**: "I need to access patient medical history and current conditions"
- **Nurse**: "I need to view patient information and update basic details"
- **Patient**: "I want to view and update my personal information"

## 🛠 Backend Implementation

### Step 1: Patient Controller

Create the patient controller:

```bash
php artisan make:controller Clinic/PatientController
```

**app/Http/Controllers/Clinic/PatientController.php**:

```php
<?php

namespace App\Http\Controllers\Clinic;

use App\Http\Controllers\Controller;
use App\Models\Patient;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class PatientController extends Controller
{
    public function index(Request $request)
    {
        $query = Patient::with(['user', 'clinic'])
            ->where('clinic_id', auth()->user()->clinic_id);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('patient_number', 'like', "%{$search}%")
                  ->orWhere('national_id', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by BPJS status
        if ($request->filled('bpjs_status')) {
            $query->where('has_bpjs', $request->bpjs_status === 'yes');
        }

        // Filter by gender
        if ($request->filled('gender')) {
            $query->where('gender', $request->gender);
        }

        // Filter by age range
        if ($request->filled('age_min') || $request->filled('age_max')) {
            $ageMin = $request->age_min;
            $ageMax = $request->age_max;
            
            if ($ageMin) {
                $query->whereRaw('TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) >= ?', [$ageMin]);
            }
            if ($ageMax) {
                $query->whereRaw('TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) <= ?', [$ageMax]);
            }
        }

        $patients = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('clinic/patients/index', [
            'patients' => $patients,
            'filters' => $request->only(['search', 'bpjs_status', 'gender', 'age_min', 'age_max'])
        ]);
    }

    public function create()
    {
        return Inertia::render('clinic/patients/create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'national_id' => 'required|string|unique:patients,national_id',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|unique:patients,email',
            'address' => 'required|string',
            
            // Emergency contact
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'emergency_contact_relationship' => 'required|string|max:100',
            
            // Medical information
            'blood_type' => 'nullable|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            'allergies' => 'nullable|array',
            'chronic_conditions' => 'nullable|array',
            'medical_notes' => 'nullable|string',
            
            // BPJS information
            'has_bpjs' => 'boolean',
            'bpjs_number' => 'nullable|string|unique:patients,bpjs_number',
            'bpjs_class' => 'nullable|in:1,2,3',
            
            // User account creation
            'create_user_account' => 'boolean',
            'password' => 'required_if:create_user_account,true|min:8|confirmed',
        ]);

        DB::beginTransaction();
        
        try {
            // Generate patient number
            $patientNumber = $this->generatePatientNumber();
            
            // Create user account if requested
            $userId = null;
            if ($validated['create_user_account']) {
                $user = User::create([
                    'name' => $validated['first_name'] . ' ' . $validated['last_name'],
                    'email' => $validated['email'] ?? $validated['first_name'] . '.' . $validated['last_name'] . '@patient.local',
                    'password' => Hash::make($validated['password']),
                    'role' => 'patient',
                    'phone' => $validated['phone'],
                    'date_of_birth' => $validated['date_of_birth'],
                    'gender' => $validated['gender'],
                    'clinic_id' => auth()->user()->clinic_id,
                ]);
                $userId = $user->id;
            }

            // Create patient record
            $patient = Patient::create([
                'user_id' => $userId,
                'clinic_id' => auth()->user()->clinic_id,
                'patient_number' => $patientNumber,
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'national_id' => $validated['national_id'],
                'date_of_birth' => $validated['date_of_birth'],
                'gender' => $validated['gender'],
                'phone' => $validated['phone'],
                'email' => $validated['email'],
                'address' => $validated['address'],
                'emergency_contact_name' => $validated['emergency_contact_name'],
                'emergency_contact_phone' => $validated['emergency_contact_phone'],
                'emergency_contact_relationship' => $validated['emergency_contact_relationship'],
                'blood_type' => $validated['blood_type'],
                'allergies' => $validated['allergies'],
                'chronic_conditions' => $validated['chronic_conditions'],
                'medical_notes' => $validated['medical_notes'],
                'has_bpjs' => $validated['has_bpjs'],
                'bpjs_number' => $validated['bpjs_number'],
                'bpjs_class' => $validated['bpjs_class'],
            ]);

            DB::commit();

            return redirect()->route('clinic.patients.show', $patient)
                ->with('success', 'Patient registered successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to register patient. Please try again.']);
        }
    }

    public function show(Patient $patient)
    {
        $this->authorize('view', $patient);
        
        $patient->load([
            'user',
            'clinic',
            'appointments.doctor.user',
            'medicalRecords.doctor.user',
            'prescriptions.doctor.user',
            'billings'
        ]);

        return Inertia::render('clinic/patients/show', [
            'patient' => $patient
        ]);
    }

    public function edit(Patient $patient)
    {
        $this->authorize('update', $patient);
        
        return Inertia::render('clinic/patients/edit', [
            'patient' => $patient
        ]);
    }

    public function update(Request $request, Patient $patient)
    {
        $this->authorize('update', $patient);

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'national_id' => ['required', 'string', Rule::unique('patients')->ignore($patient->id)],
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'phone' => 'required|string|max:20',
            'email' => ['nullable', 'email', Rule::unique('patients')->ignore($patient->id)],
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'emergency_contact_relationship' => 'required|string|max:100',
            'blood_type' => 'nullable|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            'allergies' => 'nullable|array',
            'chronic_conditions' => 'nullable|array',
            'medical_notes' => 'nullable|string',
            'has_bpjs' => 'boolean',
            'bpjs_number' => ['nullable', 'string', Rule::unique('patients')->ignore($patient->id)],
            'bpjs_class' => 'nullable|in:1,2,3',
        ]);

        $patient->update($validated);

        return redirect()->route('clinic.patients.show', $patient)
            ->with('success', 'Patient information updated successfully.');
    }

    private function generatePatientNumber(): string
    {
        $clinicCode = auth()->user()->clinic->code;
        $year = date('Y');
        $month = date('m');
        
        // Get the last patient number for this clinic this month
        $lastPatient = Patient::where('clinic_id', auth()->user()->clinic_id)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastPatient) {
            // Extract sequence number from last patient number
            $lastNumber = substr($lastPatient->patient_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        return $clinicCode . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

### Step 2: Patient Policy

Create authorization policy:

```bash
php artisan make:policy PatientPolicy
```

**app/Policies/PatientPolicy.php**:

```php
<?php

namespace App\Policies;

use App\Models\Patient;
use App\Models\User;

class PatientPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->hasPermission('view_patients');
    }

    public function view(User $user, Patient $patient): bool
    {
        if (!$user->hasPermission('view_patients')) {
            return false;
        }

        // Patients can only view their own records
        if ($user->isPatient()) {
            return $user->patient && $user->patient->id === $patient->id;
        }

        // Staff can view patients from their clinic
        return $user->canAccessClinic($patient->clinic_id);
    }

    public function create(User $user): bool
    {
        return $user->hasPermission('create_patients');
    }

    public function update(User $user, Patient $patient): bool
    {
        if (!$user->hasPermission('edit_patients')) {
            return false;
        }

        // Patients can edit their own basic information
        if ($user->isPatient()) {
            return $user->patient && $user->patient->id === $patient->id;
        }

        // Staff can edit patients from their clinic
        return $user->canAccessClinic($patient->clinic_id);
    }

    public function delete(User $user, Patient $patient): bool
    {
        return $user->hasPermission('delete_patients') && 
               $user->canAccessClinic($patient->clinic_id);
    }
}
```

## 🎨 Frontend Implementation

### Step 1: Patient Types

Create **resources/js/types/clinic.d.ts**:

```typescript
export interface Patient {
    id: number;
    user_id?: number;
    clinic_id: number;
    patient_number: string;
    national_id: string;
    first_name: string;
    last_name: string;
    full_name: string;
    date_of_birth: string;
    age: number;
    gender: 'male' | 'female';
    phone: string;
    email?: string;
    address: string;
    emergency_contact_name: string;
    emergency_contact_phone: string;
    emergency_contact_relationship: string;
    blood_type?: string;
    allergies?: string[];
    chronic_conditions?: string[];
    medical_notes?: string;
    has_bpjs: boolean;
    bpjs_number?: string;
    bpjs_class?: '1' | '2' | '3';
    is_active: boolean;
    created_at: string;
    updated_at: string;
    user?: User;
    clinic?: Clinic;
    appointments?: Appointment[];
    medical_records?: MedicalRecord[];
    prescriptions?: Prescription[];
    billings?: Billing[];
}

export interface PatientFilters {
    search?: string;
    bpjs_status?: 'yes' | 'no';
    gender?: 'male' | 'female';
    age_min?: number;
    age_max?: number;
}
```

### Step 2: Routes Configuration

Update **routes/web.php** to include patient routes:

```php
// Patient Management Routes
Route::middleware(['auth', 'verified', 'role:admin,doctor,nurse,receptionist'])
    ->prefix('clinic')
    ->name('clinic.')
    ->group(function () {

        Route::middleware('permission:view_patients')->group(function () {
            Route::get('patients', [PatientController::class, 'index'])->name('patients.index');
            Route::get('patients/{patient}', [PatientController::class, 'show'])->name('patients.show');
        });

        Route::middleware('permission:create_patients')->group(function () {
            Route::get('patients/create', [PatientController::class, 'create'])->name('patients.create');
            Route::post('patients', [PatientController::class, 'store'])->name('patients.store');
        });

        Route::middleware('permission:edit_patients')->group(function () {
            Route::get('patients/{patient}/edit', [PatientController::class, 'edit'])->name('patients.edit');
            Route::put('patients/{patient}', [PatientController::class, 'update'])->name('patients.update');
        });
    });
```

## 📋 Chapter Summary

In this chapter, we:

1. ✅ **Built comprehensive patient controller** with CRUD operations
2. ✅ **Implemented advanced search and filtering** capabilities
3. ✅ **Created patient authorization policies** for secure access
4. ✅ **Designed patient data types** for TypeScript integration
5. ✅ **Added patient registration workflow** with validation
6. ✅ **Integrated BPJS support** for Indonesian health insurance

### What We Have Now

- Complete patient management system with registration and profiles
- Advanced search and filtering capabilities
- Secure authorization with role-based access
- TypeScript types for patient data
- BPJS integration foundation

### Next Steps

In **Chapter 5: Doctor and Staff Management**, we'll:

- Build doctor and staff profile management
- Create department and specialization management
- Implement staff scheduling and availability
- Design staff directory and search features

---

**Ready to continue?** Proceed to [Chapter 5: Doctor and Staff Management](./chapter-05-staff-management.md) to build the staff management system.
