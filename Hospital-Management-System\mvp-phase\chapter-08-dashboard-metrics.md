# Chapter 08 - Dashboard dan Metrics System

## Tujuan Chapter
Pada chapter ini, kita akan:
- Implementasi dashboard dengan real-time metrics
- Membuat statistics dan analytics untuk hospital operations
- Setup role-based dashboard views
- Implementasi charts dan visualizations
- Membuat quick actions dan shortcuts

## Langkah 1: Update Dashboard Controller

### 1.1 Enhance Dashboard Controller
Edit `app/Http/Controllers/Hospital/DashboardController.php`:
```php
<?php

namespace App\Http\Controllers\Hospital;

use App\Http\Controllers\Controller;
use App\Models\Hospital\Patient;
use App\Models\Hospital\Doctor;
use App\Models\Hospital\Staff;
use App\Models\Hospital\Appointment;
use App\Models\Hospital\MedicalRecord;
use App\Models\Hospital\Department;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        // Redirect berdasarkan role
        switch ($user->role) {
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'doctor':
                return redirect()->route('doctor.dashboard');
            case 'nurse':
                return redirect()->route('nurse.dashboard');
            case 'receptionist':
                return redirect()->route('receptionist.dashboard');
            case 'pharmacist':
                return redirect()->route('pharmacist.dashboard');
            case 'lab_technician':
                return redirect()->route('lab.dashboard');
            default:
                return $this->generalDashboard();
        }
    }

    public function admin()
    {
        $stats = $this->getAdminStats();
        $charts = $this->getAdminCharts();
        $recentActivities = $this->getRecentActivities();

        return Inertia::render('Hospital/Admin/Dashboard', [
            'user' => auth()->user(),
            'stats' => $stats,
            'charts' => $charts,
            'recentActivities' => $recentActivities,
        ]);
    }

    public function doctor()
    {
        $user = auth()->user();
        $doctor = $user->doctor;

        if (!$doctor) {
            return redirect()->route('dashboard')->with('error', 'Profile dokter tidak ditemukan.');
        }

        $stats = $this->getDoctorStats($doctor->id);
        $todayAppointments = $this->getTodayAppointments($doctor->id);
        $upcomingAppointments = $this->getUpcomingAppointments($doctor->id);

        return Inertia::render('Hospital/Doctor/Dashboard', [
            'user' => $user,
            'doctor' => $doctor,
            'stats' => $stats,
            'todayAppointments' => $todayAppointments,
            'upcomingAppointments' => $upcomingAppointments,
        ]);
    }

    public function nurse()
    {
        $stats = $this->getNurseStats();
        $todayAppointments = $this->getTodayAppointmentsForNurse();
        $pendingTasks = $this->getPendingNurseTasks();

        return Inertia::render('Hospital/Nurse/Dashboard', [
            'user' => auth()->user(),
            'stats' => $stats,
            'todayAppointments' => $todayAppointments,
            'pendingTasks' => $pendingTasks,
        ]);
    }

    public function receptionist()
    {
        $stats = $this->getReceptionistStats();
        $todayAppointments = $this->getTodayAppointmentsForReceptionist();
        $walkInQueue = $this->getWalkInQueue();

        return Inertia::render('Hospital/Receptionist/Dashboard', [
            'user' => auth()->user(),
            'stats' => $stats,
            'todayAppointments' => $todayAppointments,
            'walkInQueue' => $walkInQueue,
        ]);
    }

    public function pharmacist()
    {
        $stats = $this->getPharmacistStats();
        $pendingPrescriptions = $this->getPendingPrescriptions();

        return Inertia::render('Hospital/Pharmacist/Dashboard', [
            'user' => auth()->user(),
            'stats' => $stats,
            'pendingPrescriptions' => $pendingPrescriptions,
        ]);
    }

    public function labTechnician()
    {
        $stats = $this->getLabStats();
        $pendingTests = $this->getPendingLabTests();

        return Inertia::render('Hospital/Lab/Dashboard', [
            'user' => auth()->user(),
            'stats' => $stats,
            'pendingTests' => $pendingTests,
        ]);
    }

    private function getAdminStats(): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_patients' => Patient::active()->count(),
            'new_patients_today' => Patient::whereDate('created_at', $today)->count(),
            'new_patients_this_month' => Patient::where('created_at', '>=', $thisMonth)->count(),
            
            'total_doctors' => Doctor::active()->count(),
            'available_doctors' => Doctor::available()->count(),
            
            'total_staff' => Staff::active()->count(),
            
            'total_departments' => Department::active()->count(),
            
            'appointments_today' => Appointment::today()->count(),
            'appointments_completed_today' => Appointment::today()->where('status', 'completed')->count(),
            'appointments_pending_today' => Appointment::today()->whereIn('status', ['scheduled', 'confirmed'])->count(),
            'appointments_cancelled_today' => Appointment::today()->where('status', 'cancelled')->count(),
            
            'appointments_this_month' => Appointment::where('appointment_date', '>=', $thisMonth)->count(),
            'appointments_last_month' => Appointment::whereBetween('appointment_date', [$lastMonth, $thisMonth])->count(),
            
            'medical_records_today' => MedicalRecord::whereDate('visit_date', $today)->count(),
            'medical_records_this_month' => MedicalRecord::where('visit_date', '>=', $thisMonth)->count(),
            
            'revenue_today' => Appointment::today()->where('payment_status', 'paid')->sum('fee'),
            'revenue_this_month' => Appointment::where('appointment_date', '>=', $thisMonth)
                                              ->where('payment_status', 'paid')->sum('fee'),
        ];
    }

    private function getAdminCharts(): array
    {
        // Appointments per day for last 7 days
        $appointmentsChart = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::today()->subDays($i);
            $appointmentsChart[] = [
                'date' => $date->format('Y-m-d'),
                'label' => $date->format('d/m'),
                'appointments' => Appointment::whereDate('appointment_date', $date)->count(),
                'completed' => Appointment::whereDate('appointment_date', $date)->where('status', 'completed')->count(),
            ];
        }

        // Patients by department
        $departmentChart = Department::withCount(['appointments' => function ($query) {
            $query->where('appointment_date', '>=', Carbon::now()->startOfMonth());
        }])->get()->map(function ($dept) {
            return [
                'name' => $dept->name,
                'count' => $dept->appointments_count,
            ];
        });

        // Appointment status distribution
        $statusChart = [
            ['status' => 'Completed', 'count' => Appointment::where('status', 'completed')->count()],
            ['status' => 'Scheduled', 'count' => Appointment::where('status', 'scheduled')->count()],
            ['status' => 'Confirmed', 'count' => Appointment::where('status', 'confirmed')->count()],
            ['status' => 'Cancelled', 'count' => Appointment::where('status', 'cancelled')->count()],
        ];

        return [
            'appointments_trend' => $appointmentsChart,
            'department_distribution' => $departmentChart,
            'appointment_status' => $statusChart,
        ];
    }

    private function getDoctorStats($doctorId): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'appointments_today' => Appointment::byDoctor($doctorId)->today()->count(),
            'appointments_completed_today' => Appointment::byDoctor($doctorId)->today()->where('status', 'completed')->count(),
            'appointments_pending_today' => Appointment::byDoctor($doctorId)->today()->whereIn('status', ['scheduled', 'confirmed'])->count(),
            
            'appointments_this_month' => Appointment::byDoctor($doctorId)->where('appointment_date', '>=', $thisMonth)->count(),
            'patients_seen_this_month' => Appointment::byDoctor($doctorId)
                                                    ->where('appointment_date', '>=', $thisMonth)
                                                    ->where('status', 'completed')
                                                    ->distinct('patient_id')
                                                    ->count(),
            
            'medical_records_today' => MedicalRecord::byDoctor($doctorId)->whereDate('visit_date', $today)->count(),
            'medical_records_this_month' => MedicalRecord::byDoctor($doctorId)->where('visit_date', '>=', $thisMonth)->count(),
            
            'next_appointment' => Appointment::byDoctor($doctorId)
                                            ->where('appointment_date', '>', now())
                                            ->whereIn('status', ['scheduled', 'confirmed'])
                                            ->orderBy('appointment_date')
                                            ->first(),
        ];
    }

    private function getTodayAppointments($doctorId)
    {
        return Appointment::byDoctor($doctorId)
                         ->today()
                         ->with(['patient', 'department'])
                         ->orderBy('appointment_date')
                         ->get();
    }

    private function getUpcomingAppointments($doctorId, $limit = 5)
    {
        return Appointment::byDoctor($doctorId)
                         ->where('appointment_date', '>', now())
                         ->whereIn('status', ['scheduled', 'confirmed'])
                         ->with(['patient', 'department'])
                         ->orderBy('appointment_date')
                         ->limit($limit)
                         ->get();
    }

    private function getNurseStats(): array
    {
        $today = Carbon::today();

        return [
            'patients_assigned' => 0, // Will be implemented with patient assignment system
            'appointments_today' => Appointment::today()->count(),
            'completed_appointments_today' => Appointment::today()->where('status', 'completed')->count(),
            'pending_check_ins' => Appointment::today()->whereIn('status', ['scheduled', 'confirmed'])->count(),
            'in_progress_appointments' => Appointment::today()->where('status', 'in_progress')->count(),
        ];
    }

    private function getTodayAppointmentsForNurse()
    {
        return Appointment::today()
                         ->with(['patient', 'doctor.user', 'department'])
                         ->orderBy('appointment_date')
                         ->get();
    }

    private function getPendingNurseTasks()
    {
        // This would be implemented with a tasks system
        return [];
    }

    private function getReceptionistStats(): array
    {
        $today = Carbon::today();

        return [
            'appointments_today' => Appointment::today()->count(),
            'check_ins_today' => Appointment::today()->whereNotNull('checked_in_at')->count(),
            'walk_ins_today' => 0, // Will be implemented with walk-in system
            'pending_appointments' => Appointment::today()->whereIn('status', ['scheduled', 'confirmed'])->count(),
            'new_patients_today' => Patient::whereDate('created_at', $today)->count(),
        ];
    }

    private function getTodayAppointmentsForReceptionist()
    {
        return Appointment::today()
                         ->with(['patient', 'doctor.user', 'department'])
                         ->orderBy('appointment_date')
                         ->get();
    }

    private function getWalkInQueue()
    {
        // This would be implemented with a queue management system
        return [];
    }

    private function getPharmacistStats(): array
    {
        return [
            'pending_prescriptions' => 0, // Will be implemented with prescription system
            'dispensed_today' => 0,
            'inventory_low_stock' => 0, // Will be implemented with inventory system
        ];
    }

    private function getPendingPrescriptions()
    {
        // This would be implemented with prescription system
        return [];
    }

    private function getLabStats(): array
    {
        return [
            'pending_tests' => 0, // Will be implemented with lab system
            'completed_tests_today' => 0,
            'urgent_tests' => 0,
        ];
    }

    private function getPendingLabTests()
    {
        // This would be implemented with lab system
        return [];
    }

    private function getRecentActivities($limit = 10)
    {
        $activities = collect();

        // Recent appointments
        $recentAppointments = Appointment::with(['patient', 'doctor.user'])
                                        ->orderBy('created_at', 'desc')
                                        ->limit(5)
                                        ->get()
                                        ->map(function ($appointment) {
                                            return [
                                                'type' => 'appointment',
                                                'title' => 'Appointment dibuat',
                                                'description' => "Appointment untuk {$appointment->patient->full_name} dengan {$appointment->doctor->user->full_name}",
                                                'time' => $appointment->created_at,
                                                'icon' => 'calendar',
                                                'color' => 'blue',
                                            ];
                                        });

        // Recent patients
        $recentPatients = Patient::orderBy('created_at', 'desc')
                                ->limit(5)
                                ->get()
                                ->map(function ($patient) {
                                    return [
                                        'type' => 'patient',
                                        'title' => 'Pasien baru terdaftar',
                                        'description' => "Pasien {$patient->full_name} telah terdaftar",
                                        'time' => $patient->created_at,
                                        'icon' => 'user-plus',
                                        'color' => 'green',
                                    ];
                                });

        // Recent medical records
        $recentRecords = MedicalRecord::with(['patient', 'doctor.user'])
                                     ->orderBy('created_at', 'desc')
                                     ->limit(5)
                                     ->get()
                                     ->map(function ($record) {
                                         return [
                                             'type' => 'medical_record',
                                             'title' => 'Rekam medis dibuat',
                                             'description' => "Rekam medis untuk {$record->patient->full_name} oleh {$record->doctor->user->full_name}",
                                             'time' => $record->created_at,
                                             'icon' => 'document-text',
                                             'color' => 'purple',
                                         ];
                                     });

        return $activities->merge($recentAppointments)
                         ->merge($recentPatients)
                         ->merge($recentRecords)
                         ->sortByDesc('time')
                         ->take($limit)
                         ->values();
    }

    private function generalDashboard()
    {
        return Inertia::render('Dashboard', [
            'user' => auth()->user()
        ]);
    }
}
```

## Langkah 2: Create Dashboard Components

### 2.1 Create Admin Dashboard Component
Create `resources/js/Pages/Hospital/Admin/Dashboard.jsx`:
```jsx
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import StatsCard from '@/Components/Hospital/StatsCard';
import ChartCard from '@/Components/Hospital/ChartCard';
import ActivityFeed from '@/Components/Hospital/ActivityFeed';

export default function AdminDashboard({ auth, stats, charts, recentActivities }) {
    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                    Dashboard Administrator
                </h2>
            }
        >
            <Head title="Dashboard Admin" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Stats Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <StatsCard
                            title="Total Pasien"
                            value={stats.total_patients}
                            subtitle={`${stats.new_patients_today} baru hari ini`}
                            icon="users"
                            color="blue"
                        />
                        <StatsCard
                            title="Dokter Tersedia"
                            value={`${stats.available_doctors}/${stats.total_doctors}`}
                            subtitle="Dokter aktif"
                            icon="user-group"
                            color="green"
                        />
                        <StatsCard
                            title="Appointment Hari Ini"
                            value={stats.appointments_today}
                            subtitle={`${stats.appointments_completed_today} selesai`}
                            icon="calendar"
                            color="purple"
                        />
                        <StatsCard
                            title="Pendapatan Hari Ini"
                            value={`Rp ${stats.revenue_today?.toLocaleString('id-ID') || 0}`}
                            subtitle="Total pembayaran"
                            icon="currency-dollar"
                            color="yellow"
                        />
                    </div>

                    {/* Charts Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <ChartCard
                            title="Trend Appointment 7 Hari Terakhir"
                            data={charts.appointments_trend}
                            type="line"
                        />
                        <ChartCard
                            title="Distribusi per Departemen"
                            data={charts.department_distribution}
                            type="pie"
                        />
                    </div>

                    {/* Activity Feed and Quick Actions */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                            <ActivityFeed activities={recentActivities} />
                        </div>
                        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div className="p-6">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">
                                    Quick Actions
                                </h3>
                                <div className="space-y-3">
                                    <a
                                        href={route('patients.create')}
                                        className="block w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                                    >
                                        <div className="font-medium text-blue-900">Daftar Pasien Baru</div>
                                        <div className="text-sm text-blue-600">Registrasi pasien baru</div>
                                    </a>
                                    <a
                                        href={route('appointments.create')}
                                        className="block w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
                                    >
                                        <div className="font-medium text-green-900">Buat Appointment</div>
                                        <div className="text-sm text-green-600">Jadwalkan appointment baru</div>
                                    </a>
                                    <a
                                        href={route('doctors.index')}
                                        className="block w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
                                    >
                                        <div className="font-medium text-purple-900">Kelola Dokter</div>
                                        <div className="text-sm text-purple-600">Manajemen data dokter</div>
                                    </a>
                                    <a
                                        href={route('staff.index')}
                                        className="block w-full text-left px-4 py-2 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors"
                                    >
                                        <div className="font-medium text-yellow-900">Kelola Staff</div>
                                        <div className="text-sm text-yellow-600">Manajemen data staff</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
```

## Langkah 3: Create Reusable Components

### 3.1 Create StatsCard Component
Create `resources/js/Components/Hospital/StatsCard.jsx`:
```jsx
import { 
    UsersIcon, 
    UserGroupIcon, 
    CalendarIcon, 
    CurrencyDollarIcon,
    ChartBarIcon,
    DocumentTextIcon 
} from '@heroicons/react/24/outline';

const iconMap = {
    users: UsersIcon,
    'user-group': UserGroupIcon,
    calendar: CalendarIcon,
    'currency-dollar': CurrencyDollarIcon,
    'chart-bar': ChartBarIcon,
    'document-text': DocumentTextIcon,
};

const colorMap = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
    indigo: 'bg-indigo-500',
};

export default function StatsCard({ title, value, subtitle, icon, color = 'blue' }) {
    const Icon = iconMap[icon] || ChartBarIcon;
    const bgColor = colorMap[color] || colorMap.blue;

    return (
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div className="p-6">
                <div className="flex items-center">
                    <div className="flex-shrink-0">
                        <div className={`${bgColor} rounded-md p-3`}>
                            <Icon className="h-6 w-6 text-white" />
                        </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                        <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">
                                {title}
                            </dt>
                            <dd className="text-lg font-medium text-gray-900">
                                {value}
                            </dd>
                            {subtitle && (
                                <dd className="text-sm text-gray-600">
                                    {subtitle}
                                </dd>
                            )}
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    );
}
```

## Langkah 4: Update Routes for Dashboard

### 4.1 Add Dashboard Routes
Edit `routes/web.php` dan tambahkan:
```php
// Dashboard routes with role-based access
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'admin'])->name('dashboard');
});

Route::middleware(['auth', 'role:doctor'])->prefix('doctor')->name('doctor.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'doctor'])->name('dashboard');
});

Route::middleware(['auth', 'role:nurse'])->prefix('nurse')->name('nurse.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'nurse'])->name('dashboard');
});

Route::middleware(['auth', 'role:receptionist'])->prefix('receptionist')->name('receptionist.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'receptionist'])->name('dashboard');
});

Route::middleware(['auth', 'role:pharmacist'])->prefix('pharmacist')->name('pharmacist.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'pharmacist'])->name('dashboard');
});

Route::middleware(['auth', 'role:lab_technician'])->prefix('lab')->name('lab.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'labTechnician'])->name('dashboard');
});
```

## Next Steps

Pada chapter selanjutnya, kita akan:
- Implementasi testing untuk semua MVP features
- Setup production deployment
- Membuat user documentation
- Persiapan untuk Advanced Phase

## Checklist Completion

- [ ] Dashboard controller dengan role-based views
- [ ] Admin dashboard dengan comprehensive stats
- [ ] Doctor dashboard dengan appointment management
- [ ] Nurse dashboard dengan patient care focus
- [ ] Receptionist dashboard dengan appointment handling
- [ ] Real-time metrics dan statistics
- [ ] Charts dan visualizations
- [ ] Activity feed dan recent activities
- [ ] Quick actions shortcuts
- [ ] Responsive dashboard components

**Estimasi Waktu**: 120-150 menit

**Difficulty Level**: Advanced

**File yang Dibuat**:
- Enhanced DashboardController
- Admin Dashboard React component
- StatsCard reusable component
- Role-based dashboard routes
- Dashboard metrics system

**MVP Phase Complete**: ✅
Semua fitur inti Hospital Management System telah diimplementasi dan siap untuk testing serta deployment.
